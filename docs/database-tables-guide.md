# دليل جداول قاعدة البيانات

## نظرة عامة

قاعدة البيانات `mohammi` تحتوي على جداول لنظامين:
1. **نظام إدارة القضايا القانونية** (النظام الداخلي)
2. **الموقع الرئيسي** (الواجهة العامة)

---

## 📋 جداول نظام إدارة القضايا

### 1. **الجداول الأساسية**

#### `clients` - العملاء
```sql
CREATE TABLE clients (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  email VARCHAR(255),
  address TEXT,
  id_number VARCHAR(20) UNIQUE,
  status VARCHAR(20) DEFAULT 'active',
  cases_count INTEGER DEFAULT 0,
  created_date DATE DEFAULT CURRENT_DATE
);
```
**الغرض**: إدارة بيانات العملاء والزبائن

#### `employees` - الموظفين
```sql
CREATE TABLE employees (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  position VARCHAR(255),
  department VARCHAR(255),
  phone VARCHAR(20),
  email VARCHAR(255),
  salary DECIMAL(10,2),
  hire_date DATE,
  status VARCHAR(20) DEFAULT 'active'
);
```
**الغرض**: إدارة بيانات الموظفين والمحامين

#### `issues` - القضايا
```sql
CREATE TABLE issues (
  id SERIAL PRIMARY KEY,
  case_number VARCHAR(50) UNIQUE NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  client_id INTEGER REFERENCES clients(id),
  court_name VARCHAR(255),
  issue_type_id INTEGER REFERENCES issue_types(id),
  status VARCHAR(50) DEFAULT 'pending',
  amount DECIMAL(12,2),
  next_hearing DATE
);
```
**الغرض**: إدارة القضايا والدعاوى القانونية

### 2. **جداول النظام المالي**

#### `case_distribution` - توزيع القضايا
```sql
CREATE TABLE case_distribution (
  id SERIAL PRIMARY KEY,
  issue_id INTEGER,                    -- معرف القضية
  lineage_id INTEGER REFERENCES lineages(id),  -- مجموعة النسب المالية
  admin_amount DECIMAL(15,2) DEFAULT 0,        -- مبلغ الإدارة
  remaining_amount DECIMAL(15,2) DEFAULT 0,    -- المبلغ المتبقي للتوزيع
  created_date DATE DEFAULT CURRENT_DATE
);
```
**الغرض**: 
- تحديد كيفية توزيع مبلغ القضية
- حساب نصيب الإدارة من إجمالي مبلغ القضية
- تحديد المبلغ المتبقي للتوزيع على الخدمات

**مثال عملي**:
- قضية بمبلغ 100,000 ريال
- نسبة الإدارة 20% = 20,000 ريال
- المبلغ المتبقي للتوزيع = 80,000 ريال

#### `service_distributions` - تفاصيل توزيع الخدمات
```sql
CREATE TABLE service_distributions (
  id SERIAL PRIMARY KEY,
  case_distribution_id INTEGER REFERENCES case_distribution(id),
  service_id INTEGER REFERENCES services(id),  -- نوع الخدمة (إعداد، جلسة، متابعة)
  percentage DECIMAL(5,2) DEFAULT 0,           -- نسبة الخدمة من المبلغ المتبقي
  amount DECIMAL(15,2) DEFAULT 0,              -- المبلغ المحسوب للخدمة
  lawyer_id INTEGER,                           -- المحامي المسؤول عن الخدمة
  created_date DATE DEFAULT CURRENT_DATE
);
```
**الغرض**:
- توزيع المبلغ المتبقي على الخدمات المختلفة
- تحديد نسبة كل خدمة (إعداد، جلسة، متابعة، إلخ)
- ربط كل خدمة بالمحامي المسؤول عنها

**مثال عملي**:
من المبلغ المتبقي 80,000 ريال:
- خدمة الإعداد: 30% = 24,000 ريال → المحامي أحمد
- خدمة الجلسة: 40% = 32,000 ريال → المحامي محمد  
- خدمة المتابعة: 30% = 24,000 ريال → المحامي علي

#### `lineages` - النسب المالية
```sql
CREATE TABLE lineages (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  management_share DECIMAL(5,2) DEFAULT 0.00,  -- نسبة الإدارة
  court_share DECIMAL(5,2) DEFAULT 0.00,       -- نسبة المحكمة
  commission_share DECIMAL(5,2) DEFAULT 0.00,  -- نسبة العمولة
  other_share DECIMAL(5,2) DEFAULT 0.00        -- نسب أخرى
);
```
**الغرض**: تحديد مجموعات النسب المالية المختلفة حسب نوع القضية

#### `services` - خدمات النظام الداخلي
```sql
CREATE TABLE services (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,           -- اسم الخدمة (إعداد، جلسة، متابعة)
  lineage_id INTEGER,                   -- ربط بمجموعة النسب
  employee_id INTEGER,                  -- الموظف المسؤول
  created_date DATE DEFAULT CURRENT_DATE
);
```
**الغرض**: تعريف الخدمات الداخلية للنظام (إعداد، جلسة، متابعة، إشراف، إلخ)

---

## 🌐 جداول الموقع الرئيسي

### `serviceslow` - خدمات الموقع الرئيسي
```sql
CREATE TABLE serviceslow (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,          -- عنوان الخدمة للعرض
  slug VARCHAR(255) UNIQUE NOT NULL,    -- الرابط الفريد
  description TEXT,                     -- وصف مختصر
  content TEXT,                        -- محتوى تفصيلي (HTML)
  icon_name VARCHAR(100) DEFAULT 'Scale', -- اسم الأيقونة
  icon_color VARCHAR(50) DEFAULT '#2563eb', -- لون الأيقونة
  image_url VARCHAR(500),              -- رابط الصورة
  is_active BOOLEAN DEFAULT true,       -- حالة النشاط
  sort_order INTEGER DEFAULT 0,        -- ترتيب العرض
  meta_title VARCHAR(255),             -- عنوان SEO
  meta_description TEXT,               -- وصف SEO
  created_date DATE DEFAULT CURRENT_DATE,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
**الغرض**: 
- عرض الخدمات في الموقع الرئيسي للزوار
- إدارة محتوى صفحات الخدمات
- تحسين محركات البحث (SEO)

**الفرق بين `services` و `serviceslow`**:
- `services`: للاستخدام الداخلي في نظام إدارة القضايا
- `serviceslow`: للعرض العام في الموقع الرئيسي

---

## 🔗 العلاقات بين الجداول

### نظام توزيع الأرباح:
```
القضية (issues)
    ↓
توزيع القضية (case_distribution)
    ↓
تفاصيل توزيع الخدمات (service_distributions)
    ↓
الخدمات (services) + المحامين (employees)
```

### مثال كامل:
1. **قضية**: "قضية عقارية" بمبلغ 200,000 ريال
2. **توزيع القضية**: 
   - نسبة الإدارة 25% = 50,000 ريال
   - المبلغ المتبقي = 150,000 ريال
3. **توزيع الخدمات**:
   - إعداد القضية: 20% من 150,000 = 30,000 ريال
   - حضور الجلسات: 50% من 150,000 = 75,000 ريال
   - المتابعة: 30% من 150,000 = 45,000 ريال

---

## 📊 إحصائيات الجداول

### التحقق من وجود الجداول:
```sql
SELECT table_name, 
       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
FROM information_schema.tables t
WHERE table_schema = 'public' 
  AND table_name IN ('serviceslow', 'services', 'case_distribution', 'service_distributions')
ORDER BY table_name;
```

### عرض البيانات:
```sql
-- خدمات الموقع الرئيسي
SELECT COUNT(*) as serviceslow_count FROM serviceslow;

-- خدمات النظام الداخلي  
SELECT COUNT(*) as services_count FROM services;

-- توزيعات القضايا
SELECT COUNT(*) as distributions_count FROM case_distribution;
```

---

## 🛠️ إنشاء الجداول

### 1. تشغيل السكريبت:
```bash
node scripts/create-serviceslow-table.js
```

### 2. تشغيل SQL مباشرة:
```bash
psql -U postgres -h localhost -d mohammi -f database/create_serviceslow_table.sql
```

### 3. من خلال النظام:
```bash
# الجداول تُنشأ تلقائياً عند استدعاء initializeTables()
```

---

## 🔧 الصيانة والمراقبة

### مراقبة الأداء:
```sql
-- حجم الجداول
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('serviceslow', 'services', 'case_distribution', 'service_distributions');
```

### النسخ الاحتياطي:
```bash
# نسخ احتياطي لجداول محددة
pg_dump -U postgres -h localhost -d mohammi -t serviceslow -t services > services_backup.sql
```
