# إعداد جدول serviceslow

## نظرة عامة

تم إنشاء جدول `serviceslow` لإدارة خدمات الموقع الرئيسي، مع الحفاظ على جدول `services` الأصلي المستخدم في نظام إدارة القضايا القانونية.

**النظام الموحد:**
- قاعدة بيانات واحدة: `mohammi` (PostgreSQL)
- منفذ واحد: `5432`
- خادم واحد: `localhost`
- تطبيق واحد يخدم كل من نظام إدارة القضايا والموقع الرئيسي

## الهيكل

### الجداول:
- `services` - للنظام الداخلي لإدارة القضايا القانونية
- `serviceslow` - لخدمات الموقع الرئيسي

### هيكل جدول serviceslow:

```sql
CREATE TABLE serviceslow (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,           -- عنوان الخدمة
  slug VARCHAR(255) UNIQUE NOT NULL,     -- الرابط الفريد
  description TEXT,                      -- وصف مختصر
  content TEXT,                         -- محتوى تفصيلي (HTML)
  icon_name VARCHAR(100) DEFAULT 'Scale', -- اسم الأيقونة
  icon_color VARCHAR(50) DEFAULT '#2563eb', -- لون الأيقونة
  image_url VARCHAR(500),               -- رابط الصورة
  is_active BOOLEAN DEFAULT true,       -- حالة النشاط
  sort_order INTEGER DEFAULT 0,         -- ترتيب العرض
  meta_title VARCHAR(255),              -- عنوان SEO
  meta_description TEXT,                -- وصف SEO
  created_date DATE DEFAULT CURRENT_DATE,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Endpoints

### 1. جلب جميع الخدمات
```
GET /api/serviceslow
GET /api/serviceslow?active=true  // الخدمات النشطة فقط
```

### 2. إضافة خدمة جديدة
```
POST /api/serviceslow
Content-Type: application/json

{
  "title": "عنوان الخدمة",
  "slug": "service-slug",
  "description": "وصف الخدمة",
  "content": "<h2>محتوى تفصيلي</h2>",
  "icon_name": "Gavel",
  "icon_color": "#2563eb",
  "is_active": true,
  "sort_order": 1
}
```

### 3. تحديث خدمة
```
PUT /api/serviceslow/[id]
Content-Type: application/json

{
  "title": "عنوان محدث",
  "description": "وصف محدث"
}
```

### 4. حذف خدمة
```
DELETE /api/serviceslow/[id]
```

### 5. جلب خدمة بالرابط
```
GET /api/serviceslow/slug/[slug]
```

### 6. إدراج البيانات الافتراضية
```
POST /api/serviceslow/seed
```

## الأيقونات المتاحة

```javascript
const iconOptions = [
  'Gavel',        // مطرقة القاضي
  'FileText',     // ملف نصي
  'Building',     // مبنى
  'Shield',       // درع
  'Briefcase',    // حقيبة
  'UserCheck',    // مستخدم مع علامة
  'BookOpen',     // كتاب مفتوح
  'TrendingUp',   // اتجاه صاعد
  'CheckCircle',  // دائرة مع علامة
  'Search'        // بحث
];
```

## إعداد الجدول

### 1. إنشاء الجدول (تلقائياً عند تشغيل النظام):
الجدول يتم إنشاؤه تلقائياً عند استدعاء `initializeTables()` في ملف `database.ts`

### 2. إدراج البيانات الافتراضية:

#### باستخدام API:
```bash
curl -X POST http://localhost:7443/api/serviceslow/seed
```

#### باستخدام السكريبت:
```bash
node scripts/seed-serviceslow.js
```

### 3. التحقق من الإعداد:
```bash
# التحقق من وجود الجدول
psql -h localhost -U postgres -d mohammi -c "SELECT COUNT(*) FROM serviceslow;"
```

## استخدام في المكونات

### جلب الخدمات في React:

```typescript
const [services, setServices] = useState<Service[]>([]);

useEffect(() => {
  const fetchServices = async () => {
    try {
      const response = await fetch('/api/serviceslow?active=true');
      const data = await response.json();
      if (data.success) {
        setServices(data.data);
      }
    } catch (error) {
      console.error('خطأ في جلب الخدمات:', error);
    }
  };

  fetchServices();
}, []);
```

### عرض الخدمات:

```tsx
{services.map((service) => (
  <ServiceCard key={service.id} service={service} />
))}
```

## لوحة التحكم

يمكن إدارة الخدمات من خلال:
- `/admin/serviceslow` - صفحة إدارة خدمات الموقع الرئيسي

## الفرق بين الجدولين

| الخاصية | services | serviceslow |
|---------|----------|-------------|
| الاستخدام | نظام إدارة القضايا | الموقع الرئيسي |
| الصفحات | لوحة التحكم الداخلية | الصفحة الرئيسية |
| API | `/api/services` | `/api/serviceslow` |
| الإدارة | `/admin/services` | `/admin/serviceslow` |

## ملاحظات مهمة

1. **عدم التداخل**: الجدولان منفصلان تماماً ولا يؤثر أحدهما على الآخر
2. **البيانات الافتراضية**: كل جدول له بياناته الافتراضية المنفصلة
3. **الصلاحيات**: يمكن إدارة كل جدول بشكل مستقل
4. **النسخ الاحتياطي**: يجب أخذ نسخة احتياطية من كلا الجدولين

## استكشاف الأخطاء

### مشكلة: الجدول غير موجود
```bash
# تشغيل إعداد قاعدة البيانات
curl -X POST http://localhost:7443/api/setup-db
```

### مشكلة: لا توجد بيانات
```bash
# إدراج البيانات الافتراضية
curl -X POST http://localhost:7443/api/serviceslow/seed
```

### مشكلة: خطأ في الرابط المكرر
تأكد من أن كل خدمة لها `slug` فريد.
