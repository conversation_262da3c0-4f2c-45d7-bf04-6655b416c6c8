const { Pool } = require('pg');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'legal_system',
  password: '123456',
  port: 5432,
});

async function implementProperAccountingStructure() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 بدء تطبيق التصميم المحاسبي الصحيح...');
    
    // 1. إنشاء الحسابات الرئيسية للعملاء والموظفين والموردين
    console.log('📊 إنشاء الحسابات الرئيسية...');
    
    // حساب العملاء الرئيسي
    const clientsMainAccount = await client.query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'العملاء', 'أصول', 3,
        true, true, 'clients',
        true, 'مدين', false
      ) 
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `);
    
    // حساب الموظفين الرئيسي
    const employeesMainAccount = await client.query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'الموظفين', 'خصوم', 3,
        true, true, 'employees',
        true, 'دائن', false
      )
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `);
    
    // حساب الموردين الرئيسي
    const suppliersMainAccount = await client.query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_type, account_level,
        is_main_account, is_control_account, linked_table,
        auto_create_sub_accounts, account_nature, allow_posting
      ) VALUES (
        '********', 'الموردين', 'خصوم', 3,
        true, true, 'suppliers',
        true, 'دائن', false
      )
      ON CONFLICT (account_code) DO UPDATE SET
        account_name = EXCLUDED.account_name,
        linked_table = EXCLUDED.linked_table,
        auto_create_sub_accounts = EXCLUDED.auto_create_sub_accounts,
        is_control_account = EXCLUDED.is_control_account
      RETURNING id
    `);
    
    console.log('✅ تم إنشاء الحسابات الرئيسية');
    
    // 2. التأكد من وجود عمود account_id في جداول العملاء والموظفين
    console.log('🔧 التأكد من هيكل الجداول...');
    
    // إضافة عمود account_id للعملاء إذا لم يكن موجوداً
    try {
      await client.query(`
        ALTER TABLE clients 
        ADD COLUMN IF NOT EXISTS account_id INTEGER REFERENCES chart_of_accounts(id)
      `);
    } catch (error) {
      console.log('عمود account_id موجود بالفعل في جدول clients');
    }
    
    // إضافة عمود account_id للموظفين إذا لم يكن موجوداً
    try {
      await client.query(`
        ALTER TABLE employees 
        ADD COLUMN IF NOT EXISTS account_id INTEGER REFERENCES chart_of_accounts(id)
      `);
    } catch (error) {
      console.log('عمود account_id موجود بالفعل في جدول employees');
    }
    
    // إنشاء جدول الموردين إذا لم يكن موجوداً
    await client.query(`
      CREATE TABLE IF NOT EXISTS suppliers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        tax_number VARCHAR(50),
        supplier_type VARCHAR(100),
        account_id INTEGER REFERENCES chart_of_accounts(id),
        status VARCHAR(20) DEFAULT 'active',
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ تم التأكد من هيكل الجداول');
    
    // 3. إنشاء حسابات فرعية للعملاء الموجودين
    console.log('👥 إنشاء حسابات فرعية للعملاء...');
    
    const clients = await client.query('SELECT * FROM clients WHERE account_id IS NULL');
    const clientsMainAccountId = clientsMainAccount.rows[0].id;
    
    for (const clientRow of clients.rows) {
      const subAccountCode = `12010${String(clientRow.id).padStart(3, '0')}`;
      const subAccountName = `العميل - ${clientRow.name}`;
      
      // إنشاء حساب فرعي للعميل
      const subAccount = await client.query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_level,
          parent_id, is_sub_account, account_nature, allow_posting,
          linked_table, linked_record_id
        ) VALUES (
          $1, $2, 'أصول', 4,
          $3, true, 'مدين', true,
          'clients', $4
        ) RETURNING id
      `, [subAccountCode, subAccountName, clientsMainAccountId, clientRow.id]);
      
      // ربط العميل بالحساب الفرعي
      await client.query(
        'UPDATE clients SET account_id = $1 WHERE id = $2',
        [subAccount.rows[0].id, clientRow.id]
      );
    }
    
    console.log(`✅ تم إنشاء ${clients.rows.length} حساب فرعي للعملاء`);
    
    // 4. إنشاء حسابات فرعية للموظفين الموجودين
    console.log('👨‍💼 إنشاء حسابات فرعية للموظفين...');
    
    const employees = await client.query('SELECT * FROM employees WHERE account_id IS NULL');
    const employeesMainAccountId = employeesMainAccount.rows[0].id;
    
    for (const employee of employees.rows) {
      const subAccountCode = `21010${String(employee.id).padStart(3, '0')}`;
      const subAccountName = `الموظف - ${employee.name}`;
      
      // إنشاء حساب فرعي للموظف
      const subAccount = await client.query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_level,
          parent_id, is_sub_account, account_nature, allow_posting,
          linked_table, linked_record_id
        ) VALUES (
          $1, $2, 'خصوم', 4,
          $3, true, 'دائن', true,
          'employees', $4
        ) RETURNING id
      `, [subAccountCode, subAccountName, employeesMainAccountId, employee.id]);
      
      // ربط الموظف بالحساب الفرعي
      await client.query(
        'UPDATE employees SET account_id = $1 WHERE id = $2',
        [subAccount.rows[0].id, employee.id]
      );
    }
    
    console.log(`✅ تم إنشاء ${employees.rows.length} حساب فرعي للموظفين`);
    
    console.log('🎉 تم تطبيق التصميم المحاسبي الصحيح بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في تطبيق التصميم المحاسبي:', error);
    throw error;
  } finally {
    client.release();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  implementProperAccountingStructure()
    .then(() => {
      console.log('✅ تم الانتهاء من تطبيق التصميم المحاسبي');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ فشل في تطبيق التصميم المحاسبي:', error);
      process.exit(1);
    });
}

module.exports = { implementProperAccountingStructure };
