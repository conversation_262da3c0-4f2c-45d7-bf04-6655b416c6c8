{"models": [{"title": "CodeGeeX2-6B Local", "provider": "openai", "model": "codegeex2-6b", "apiBase": "http://localhost:8000/v1", "apiKey": "local-key", "contextLength": 2048, "completionOptions": {"temperature": 0.2, "topP": 0.9, "maxTokens": 128}}], "tabAutocompleteModel": {"title": "CodeGeeX2-6B Local Autocomplete", "provider": "openai", "model": "codegeex2-6b", "apiBase": "http://localhost:8000/v1", "apiKey": "local-key", "contextLength": 1024, "completionOptions": {"temperature": 0.1, "topP": 0.95, "maxTokens": 64}}, "customCommands": [{"name": "test-local-model", "prompt": "Test the local CodeGeeX model connection", "description": "Test connection to local CodeGeeX2-6B model"}], "allowAnonymousTelemetry": false, "embeddingsProvider": {"provider": "transformers.js"}}