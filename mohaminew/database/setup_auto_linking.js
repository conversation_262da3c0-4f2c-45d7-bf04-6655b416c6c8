const { Client } = require('pg');

async function setupAutoLinking() {
  const client = new Client({
    user: 'postgres',
    host: 'localhost',
    database: 'law_firm_db',
    password: 'your_password_here',
    port: 5432,
  });

  try {
    await client.connect();
    console.log('🔗 بدء إعداد نظام الربط التلقائي للحسابات...');

    // 1. التأكد من وجود الجداول المطلوبة
    console.log('📋 التحقق من وجود الجداول المطلوبة...');
    
    const tablesCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('account_linking_settings', 'account_sub_links', 'chart_of_accounts')
    `);
    
    console.log(`✅ تم العثور على ${tablesCheck.rows.length} جداول من أصل 3`);

    // 2. إدراج إعدادات الربط الافتراضية
    console.log('⚙️ إعداد إعدادات الربط الافتراضية...');

    const defaultSettings = [
      {
        table: 'clients',
        display: 'الموكلين',
        description: 'ربط حسابات الموكلين تلقائياً عند إضافة موكل جديد',
        prefix: 'CLI',
        name_field: 'name',
        account_code: '1121'
      },
      {
        table: 'employees',
        display: 'الموظفين',
        description: 'ربط حسابات الموظفين تلقائياً عند إضافة موظف جديد',
        prefix: 'EMP',
        name_field: 'name',
        account_code: '1122'
      },
      {
        table: 'issues',
        display: 'القضايا',
        description: 'ربط حسابات القضايا تلقائياً عند إضافة قضية جديدة',
        prefix: 'CASE',
        name_field: 'title',
        account_code: '4110'
      },
      {
        table: 'courts',
        display: 'المحاكم',
        description: 'ربط حسابات المحاكم تلقائياً عند إضافة محكمة جديدة',
        prefix: 'CRT',
        name_field: 'name',
        account_code: '5110'
      },
      {
        table: 'governorates',
        display: 'المحافظات',
        description: 'ربط حسابات المحافظات تلقائياً',
        prefix: 'GOV',
        name_field: 'name',
        account_code: '5120'
      },
      {
        table: 'branches',
        display: 'الفروع',
        description: 'ربط حسابات الفروع تلقائياً',
        prefix: 'BR',
        name_field: 'name',
        account_code: '5130'
      }
    ];

    for (const setting of defaultSettings) {
      // البحث عن الحساب الرئيسي
      const accountResult = await client.query(
        'SELECT id FROM chart_of_accounts WHERE account_code = $1',
        [setting.account_code]
      );

      const accountId = accountResult.rows.length > 0 ? accountResult.rows[0].id : null;

      if (accountId) {
        // إدراج أو تحديث إعدادات الربط
        await client.query(`
          INSERT INTO account_linking_settings 
          (table_name, table_display_name, table_description, is_enabled, auto_create_on_insert, 
           account_prefix, name_field, id_field, default_main_account_id)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          ON CONFLICT (table_name) DO UPDATE SET
            table_display_name = $2,
            table_description = $3,
            is_enabled = $4,
            auto_create_on_insert = $5,
            account_prefix = $6,
            name_field = $7,
            id_field = $8,
            default_main_account_id = $9
        `, [
          setting.table,
          setting.display,
          setting.description,
          true, // is_enabled
          true, // auto_create_on_insert
          setting.prefix,
          setting.name_field,
          'id',
          accountId
        ]);

        // تحديث الحساب الرئيسي لتفعيل الربط التلقائي
        await client.query(`
          UPDATE chart_of_accounts 
          SET linked_table = $1, auto_create_sub_accounts = true
          WHERE id = $2
        `, [setting.table, accountId]);

        console.log(`✅ تم إعداد ربط ${setting.display} بالحساب ${setting.account_code}`);
      } else {
        console.log(`⚠️ لم يتم العثور على الحساب ${setting.account_code} لـ ${setting.display}`);
      }
    }

    // 3. إنشاء روابط للسجلات الموجودة
    console.log('🔄 إنشاء روابط للسجلات الموجودة...');

    for (const setting of defaultSettings) {
      const accountResult = await client.query(
        'SELECT id, account_code FROM chart_of_accounts WHERE account_code = $1',
        [setting.account_code]
      );

      if (accountResult.rows.length > 0) {
        const account = accountResult.rows[0];
        
        try {
          // جلب السجلات الموجودة من الجدول
          const recordsResult = await client.query(
            `SELECT id, ${setting.name_field} as name FROM ${setting.table} WHERE is_active = true`
          );

          let linkedCount = 0;
          
          for (const record of recordsResult.rows) {
            const subAccountCode = `${account.account_code}-${record.id.toString().padStart(4, '0')}`;
            const subAccountName = `${setting.display.slice(0, -2)}: ${record.name}`;

            // التحقق من عدم وجود الرابط مسبقاً
            const existingLink = await client.query(
              'SELECT id FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2 AND linked_record_id = $3',
              [account.id, setting.table, record.id]
            );

            if (existingLink.rows.length === 0) {
              await client.query(`
                INSERT INTO account_sub_links
                (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
                VALUES ($1, $2, $3, $4, $5, $6)
              `, [
                account.id,
                setting.table,
                record.id,
                subAccountCode,
                subAccountName,
                'النظام'
              ]);
              linkedCount++;
            }
          }

          console.log(`✅ تم ربط ${linkedCount} سجل من ${setting.display}`);
        } catch (error) {
          console.log(`⚠️ خطأ في ربط سجلات ${setting.display}: ${error.message}`);
        }
      }
    }

    // 4. إنشاء دوال قاعدة البيانات للربط التلقائي
    console.log('🔧 إنشاء دوال قاعدة البيانات للربط التلقائي...');

    // دالة عامة للربط التلقائي
    await client.query(`
      CREATE OR REPLACE FUNCTION auto_create_account_link()
      RETURNS TRIGGER AS $$
      DECLARE
        settings_record RECORD;
        sub_code VARCHAR(50);
        sub_name VARCHAR(255);
        record_name VARCHAR(255);
      BEGIN
        -- البحث عن إعدادات الربط للجدول
        SELECT * INTO settings_record
        FROM account_linking_settings
        WHERE table_name = TG_TABLE_NAME 
        AND is_enabled = true 
        AND auto_create_on_insert = true
        LIMIT 1;

        -- إذا تم العثور على الإعدادات، قم بإنشاء الرابط
        IF settings_record IS NOT NULL THEN
          -- الحصول على اسم السجل
          EXECUTE format('SELECT %I FROM %I WHERE id = $1', settings_record.name_field, TG_TABLE_NAME)
          INTO record_name
          USING NEW.id;

          -- إنشاء كود الحساب الفرعي
          SELECT account_code INTO sub_code
          FROM chart_of_accounts
          WHERE id = settings_record.default_main_account_id;
          
          sub_code := sub_code || '-' || LPAD(NEW.id::text, 4, '0');
          sub_name := settings_record.table_display_name || ': ' || COALESCE(record_name, 'سجل رقم ' || NEW.id);

          -- إنشاء الرابط
          INSERT INTO account_sub_links
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES (
            settings_record.default_main_account_id,
            TG_TABLE_NAME,
            NEW.id,
            sub_code,
            sub_name,
            'النظام'
          );
        END IF;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // 5. إنشاء المحفزات للجداول
    const tables = ['clients', 'employees', 'issues', 'courts', 'governorates', 'branches'];
    
    for (const table of tables) {
      // التحقق من وجود الجدول
      const tableExists = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = $1
        )
      `, [table]);

      if (tableExists.rows[0].exists) {
        // حذف المحفز القديم إن وجد
        await client.query(`DROP TRIGGER IF EXISTS trigger_auto_create_account_link ON ${table}`);
        
        // إنشاء المحفز الجديد
        await client.query(`
          CREATE TRIGGER trigger_auto_create_account_link
            AFTER INSERT ON ${table}
            FOR EACH ROW
            EXECUTE FUNCTION auto_create_account_link()
        `);
        
        console.log(`✅ تم إنشاء محفز الربط التلقائي لجدول ${table}`);
      } else {
        console.log(`⚠️ الجدول ${table} غير موجود`);
      }
    }

    // 6. عرض إحصائيات النتائج
    console.log('\n📊 إحصائيات نظام الربط التلقائي:');

    const settingsCount = await client.query('SELECT COUNT(*) as count FROM account_linking_settings WHERE is_enabled = true');
    console.log(`✅ إعدادات الربط المفعلة: ${settingsCount.rows[0].count}`);

    const linksCount = await client.query('SELECT COUNT(*) as count FROM account_sub_links WHERE is_active = true');
    console.log(`✅ إجمالي الروابط النشطة: ${linksCount.rows[0].count}`);

    const totalBalance = await client.query('SELECT SUM(current_balance) as total FROM account_sub_links WHERE is_active = true');
    console.log(`💰 إجمالي أرصدة الحسابات الفرعية: ${totalBalance.rows[0].total || 0}`);

    // عرض تفاصيل كل جدول
    for (const setting of defaultSettings) {
      const tableLinks = await client.query(
        'SELECT COUNT(*) as count FROM account_sub_links WHERE linked_table = $1 AND is_active = true',
        [setting.table]
      );
      console.log(`  📋 ${setting.display}: ${tableLinks.rows[0].count} رابط`);
    }

    console.log('\n🎉 تم إعداد نظام الربط التلقائي بنجاح!');
    console.log('\n📝 ملاحظات مهمة:');
    console.log('   • سيتم ربط السجلات الجديدة تلقائياً عند إضافتها');
    console.log('   • يمكن إدارة الإعدادات من صفحة "إعدادات الربط التلقائي"');
    console.log('   • يمكن عرض وتعديل الروابط من صفحة "روابط الحسابات الفرعية"');
    console.log('   • تأكد من تحديث كلمة مرور قاعدة البيانات في هذا الملف');

  } catch (error) {
    console.error('❌ خطأ في إعداد نظام الربط التلقائي:', error);
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  setupAutoLinking();
}

module.exports = { setupAutoLinking };
