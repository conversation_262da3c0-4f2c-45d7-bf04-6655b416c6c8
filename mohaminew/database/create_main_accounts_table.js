// إنشاء جدول الحسابات الرئيسية
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function createMainAccountsTable() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 إنشاء جدول الحسابات الرئيسية...');
    await client.connect();

    // حذف الجداول القديمة إذا كانت موجودة
    await client.query('DROP TABLE IF EXISTS account_linking_settings CASCADE');
    await client.query('DROP TABLE IF EXISTS account_sub_links CASCADE');

    // إنشاء جدول الحسابات الرئيسية الجديد
    await client.query(`
      CREATE TABLE IF NOT EXISTS main_accounts (
        id SERIAL PRIMARY KEY,
        account_name VARCHAR(255) NOT NULL UNIQUE,
        account_code VARCHAR(20),
        chart_account_id INTEGER REFERENCES chart_of_accounts(id),
        is_required BOOLEAN DEFAULT TRUE,
        description TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إدراج الحسابات الرئيسية المطلوبة
    const mainAccounts = [
      {
        name: 'حساب العملاء التجميعي',
        description: 'الحساب التجميعي لجميع حسابات العملاء'
      },
      {
        name: 'حساب الايرادات',
        description: 'الحساب الرئيسي للإيرادات'
      },
      {
        name: 'حساب المصروفات',
        description: 'الحساب الرئيسي للمصروفات'
      },
      {
        name: 'حساب رأس المال',
        description: 'حساب رأس المال المستثمر'
      },
      {
        name: 'حساب الموظفين التجميعي',
        description: 'الحساب التجميعي لجميع حسابات الموظفين'
      },
      {
        name: 'حساب الصندوق الرئيسي',
        description: 'الصندوق الرئيسي للنقدية'
      },
      {
        name: 'حساب الفوارق',
        description: 'حساب الفوارق المحاسبية'
      },
      {
        name: 'حساب فوارق العملات',
        description: 'حساب فوارق أسعار الصرف'
      },
      {
        name: 'حساب مصلحة الضرائب',
        description: 'حساب المستحقات الضريبية'
      },
      {
        name: 'حساب المرتبات',
        description: 'حساب مرتبات الموظفين'
      }
    ];

    for (const account of mainAccounts) {
      await client.query(`
        INSERT INTO main_accounts (account_name, description)
        VALUES ($1, $2)
        ON CONFLICT (account_name) DO NOTHING
      `, [account.name, account.description]);
    }

    // إنشاء فهرس للأداء
    await client.query('CREATE INDEX IF NOT EXISTS idx_main_accounts_name ON main_accounts(account_name)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_main_accounts_code ON main_accounts(account_code)');

    // عرض النتائج
    const count = await client.query('SELECT COUNT(*) as count FROM main_accounts');
    console.log(`✅ تم إنشاء جدول الحسابات الرئيسية بنجاح!`);
    console.log(`📊 عدد الحسابات الرئيسية: ${count.rows[0].count}`);

    // عرض الحسابات المُدرجة
    const accounts = await client.query('SELECT account_name FROM main_accounts ORDER BY id');
    console.log('\n📋 الحسابات الرئيسية المُدرجة:');
    accounts.rows.forEach((account, index) => {
      console.log(`   ${index + 1}. ${account.account_name}`);
    });

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

createMainAccountsTable();