const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function insertChartOfAccounts() {
  try {
    console.log('🔄 إدراج دليل الحسابات الأساسي...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // دليل الحسابات الأساسي للشركة القانونية
    const accounts = [
      // المستوى الأول - الأصول
      {
        account_code: '01',
        account_name: 'الأصول',
        account_name_en: 'Assets',
        level_1_code: '01',
        account_level: 1,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'مجموعة الأصول الرئيسية'
      },
      
      // المستوى الثاني - الأصول المتداولة
      {
        account_code: '0101',
        account_name: 'الأصول المتداولة',
        account_name_en: 'Current Assets',
        level_1_code: '01',
        level_2_code: '0101',
        account_level: 2,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'الأصول قصيرة الأجل'
      },
      
      // المستوى الثالث - النقدية والبنوك
      {
        account_code: '010101',
        account_name: 'النقدية والبنوك',
        account_name_en: 'Cash and Banks',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010101',
        account_level: 3,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'النقدية في الصندوق والبنوك'
      },
      
      // المستوى الرابع - الحسابات الفرعية للنقدية
      {
        account_code: '********',
        account_name: 'صندوق النقدية الرئيسي',
        account_name_en: 'Main Cash Box',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010101',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'صندوق النقدية الرئيسي للمكتب'
      },
      
      {
        account_code: '********',
        account_name: 'البنك الأهلي اليمني',
        account_name_en: 'National Bank of Yemen',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010101',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'حساب البنك الأهلي اليمني'
      },
      
      // المستوى الثالث - العملاء
      {
        account_code: '010102',
        account_name: 'حسابات العملاء',
        account_name_en: 'Clients Accounts',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010102',
        account_level: 3,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'حسابات العملاء والموكلين'
      },
      
      // المستوى الرابع - حساب تحكم العملاء
      {
        account_code: '********',
        account_name: 'حساب تحكم العملاء',
        account_name_en: 'Clients Control Account',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010102',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        linked_table: 'clients',
        auto_create_sub_accounts: true,
        description: 'حساب تحكم لجميع العملاء'
      },
      
      // المستوى الأول - الخصوم
      {
        account_code: '02',
        account_name: 'الخصوم',
        account_name_en: 'Liabilities',
        level_1_code: '02',
        account_level: 1,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'مجموعة الخصوم الرئيسية'
      },
      
      // المستوى الثاني - الخصوم المتداولة
      {
        account_code: '0201',
        account_name: 'الخصوم المتداولة',
        account_name_en: 'Current Liabilities',
        level_1_code: '02',
        level_2_code: '0201',
        account_level: 2,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'الخصوم قصيرة الأجل'
      },
      
      // المستوى الثالث - حسابات الموردين
      {
        account_code: '020101',
        account_name: 'حسابات الموردين',
        account_name_en: 'Suppliers Accounts',
        level_1_code: '02',
        level_2_code: '0201',
        level_3_code: '020101',
        account_level: 3,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'حسابات الموردين والدائنين'
      },
      
      // المستوى الرابع - حساب تحكم الموردين
      {
        account_code: '********',
        account_name: 'حساب تحكم الموردين',
        account_name_en: 'Suppliers Control Account',
        level_1_code: '02',
        level_2_code: '0201',
        level_3_code: '020101',
        level_4_code: '********',
        account_level: 4,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: true,
        description: 'حساب تحكم لجميع الموردين'
      },
      
      // المستوى الثالث - رواتب الموظفين
      {
        account_code: '020102',
        account_name: 'رواتب الموظفين',
        account_name_en: 'Employee Salaries',
        level_1_code: '02',
        level_2_code: '0201',
        level_3_code: '020102',
        account_level: 3,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'رواتب ومستحقات الموظفين'
      },
      
      // المستوى الرابع - حساب تحكم الموظفين
      {
        account_code: '********',
        account_name: 'حساب تحكم الموظفين',
        account_name_en: 'Employees Control Account',
        level_1_code: '02',
        level_2_code: '0201',
        level_3_code: '020102',
        level_4_code: '********',
        account_level: 4,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: true,
        linked_table: 'employees',
        auto_create_sub_accounts: true,
        description: 'حساب تحكم لجميع الموظفين'
      },
      
      // المستوى الأول - حقوق الملكية
      {
        account_code: '03',
        account_name: 'حقوق الملكية',
        account_name_en: 'Equity',
        level_1_code: '03',
        account_level: 1,
        account_type: 'حقوق ملكية',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'حقوق ملكية الشركة'
      },
      
      // المستوى الثاني - رأس المال
      {
        account_code: '0301',
        account_name: 'رأس المال',
        account_name_en: 'Capital',
        level_1_code: '03',
        level_2_code: '0301',
        account_level: 2,
        account_type: 'حقوق ملكية',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'رأس مال الشركة'
      },
      
      // المستوى الثالث - رأس المال المدفوع
      {
        account_code: '030101',
        account_name: 'رأس المال المدفوع',
        account_name_en: 'Paid Capital',
        level_1_code: '03',
        level_2_code: '0301',
        level_3_code: '030101',
        account_level: 3,
        account_type: 'حقوق ملكية',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'رأس المال المدفوع فعلياً'
      },
      
      // المستوى الرابع - رأس المال الأساسي
      {
        account_code: '********',
        account_name: 'رأس المال الأساسي',
        account_name_en: 'Basic Capital',
        level_1_code: '03',
        level_2_code: '0301',
        level_3_code: '030101',
        level_4_code: '********',
        account_level: 4,
        account_type: 'حقوق ملكية',
        account_nature: 'دائن',
        allow_transactions: true,
        description: 'رأس المال الأساسي للشركة'
      }
    ];

    console.log('\n📊 إدراج الحسابات الأساسية...');
    
    for (const account of accounts) {
      // البحث عن parent_id إذا لم يكن المستوى الأول
      let parent_id = null;
      if (account.account_level > 1) {
        let parent_code = '';
        if (account.account_level === 2) {
          parent_code = account.level_1_code;
        } else if (account.account_level === 3) {
          parent_code = account.level_2_code;
        } else if (account.account_level === 4) {
          parent_code = account.level_3_code;
        }
        
        const parentResult = await client.query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [parent_code]
        );
        
        if (parentResult.rows.length > 0) {
          parent_id = parentResult.rows[0].id;
        }
      }
      
      // إدراج الحساب
      await client.query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_name_en, level_1_code, level_2_code, 
          level_3_code, level_4_code, account_level, parent_id, account_type, 
          account_nature, allow_transactions, linked_table, auto_create_sub_accounts, description
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
      `, [
        account.account_code, account.account_name, account.account_name_en,
        account.level_1_code, account.level_2_code, account.level_3_code, account.level_4_code,
        account.account_level, parent_id, account.account_type, account.account_nature,
        account.allow_transactions, account.linked_table, account.auto_create_sub_accounts,
        account.description
      ]);
      
      console.log(`   ✅ تم إدراج: ${account.account_code} - ${account.account_name}`);
    }

    console.log('\n✅ تم إدراج دليل الحسابات الأساسي بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إدراج دليل الحسابات:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  insertChartOfAccounts()
    .then(() => {
      console.log('\n🎉 تم إدراج دليل الحسابات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل في إدراج دليل الحسابات:', error);
      process.exit(1);
    });
}

module.exports = { insertChartOfAccounts };
