// إصلاح جدول تاريخ الأرصدة الافتتاحية
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function fixOpeningBalancesHistory() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من هيكل جدول الأرصدة الحالي
    const balancesColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'opening_balances'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 هيكل جدول الأرصدة الحالي:');
    balancesColumns.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    // حذف الجدول القديم وإعادة إنشاؤه
    await client.query('DROP TABLE IF EXISTS opening_balances_history CASCADE');
    console.log('✅ تم حذف الجدول القديم');

    // إنشاء جدول التاريخ بالهيكل الصحيح
    await client.query(`
      CREATE TABLE opening_balances_history (
        id SERIAL PRIMARY KEY,
        opening_balance_id INTEGER,
        account_name VARCHAR(255),
        account_code VARCHAR(50),
        old_debit_amount DECIMAL(15,2) DEFAULT 0,
        old_credit_amount DECIMAL(15,2) DEFAULT 0,
        new_debit_amount DECIMAL(15,2) DEFAULT 0,
        new_credit_amount DECIMAL(15,2) DEFAULT 0,
        old_balance_type VARCHAR(20),
        new_balance_type VARCHAR(20),
        change_type VARCHAR(50),
        changed_by VARCHAR(255),
        change_reason TEXT,
        change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ تم إنشاء جدول opening_balances_history بالهيكل الصحيح');

    // نسخ البيانات الحالية
    const currentBalances = await client.query('SELECT * FROM opening_balances ORDER BY id');
    
    for (const balance of currentBalances.rows) {
      await client.query(`
        INSERT INTO opening_balances_history 
        (opening_balance_id, account_name, account_code, new_debit_amount, new_credit_amount, new_balance_type, change_type, changed_by, change_reason)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        balance.id,
        balance.account_name,
        balance.account_code,
        balance.debit_amount,
        balance.credit_amount,
        balance.balance_type,
        'INITIAL_IMPORT',
        'النظام',
        'استيراد البيانات الأولية'
      ]);
    }
    
    console.log(`✅ تم نسخ ${currentBalances.rows.length} رصيد إلى جدول التاريخ`);

    // إضافة بيانات تجريبية للتاريخ
    const historyData = [
      {
        opening_balance_id: 1,
        account_name: 'النقدية بالصندوق',
        account_code: '1001',
        old_debit_amount: 45000.00,
        new_debit_amount: 50000.00,
        old_balance_type: 'مدين',
        new_balance_type: 'مدين',
        change_type: 'UPDATE',
        changed_by: 'ماجد أحمد',
        change_reason: 'تصحيح رصيد الصندوق بعد الجرد'
      },
      {
        opening_balance_id: 2,
        account_name: 'البنك الأهلي',
        account_code: '1002',
        old_debit_amount: 140000.00,
        new_debit_amount: 150000.00,
        old_balance_type: 'مدين',
        new_balance_type: 'مدين',
        change_type: 'UPDATE',
        changed_by: 'محمد صالح',
        change_reason: 'تحديث رصيد البنك حسب كشف الحساب'
      }
    ];

    for (const history of historyData) {
      await client.query(`
        INSERT INTO opening_balances_history 
        (opening_balance_id, account_name, account_code, old_debit_amount, old_credit_amount, 
         new_debit_amount, new_credit_amount, old_balance_type, new_balance_type, 
         change_type, changed_by, change_reason)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      `, [
        history.opening_balance_id,
        history.account_name,
        history.account_code,
        history.old_debit_amount,
        0,
        history.new_debit_amount,
        0,
        history.old_balance_type,
        history.new_balance_type,
        history.change_type,
        history.changed_by,
        history.change_reason
      ]);
    }
    
    console.log(`✅ تم إضافة ${historyData.length} سجل تاريخي`);

    // التحقق من النتائج
    const totalHistory = await client.query('SELECT COUNT(*) FROM opening_balances_history');
    const totalCurrent = await client.query('SELECT COUNT(*) FROM opening_balances');
    
    console.log('📋 ملخص الأرصدة الافتتاحية:');
    console.log(`   ✅ الأرصدة الحالية: ${totalCurrent.rows[0].count} سجل`);
    console.log(`   ✅ تاريخ الأرصدة: ${totalHistory.rows[0].count} سجل`);

    console.log('🎉 تم إصلاح جدول تاريخ الأرصدة الافتتاحية بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح جدول التاريخ:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإصلاح
fixOpeningBalancesHistory();
