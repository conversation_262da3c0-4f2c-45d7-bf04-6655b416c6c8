const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function fixAccountClassification() {
  try {
    console.log('🔧 إصلاح تصنيف الحسابات الرئيسية والفرعية...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. عرض الوضع الحالي قبل الإصلاح
    console.log('\n📊 الوضع الحالي قبل الإصلاح:');
    const currentStatus = await client.query(`
      SELECT 
        account_code,
        account_name,
        account_level,
        parent_id,
        is_main_account,
        is_sub_account,
        (SELECT account_name FROM chart_of_accounts p WHERE p.id = c.parent_id) as parent_name
      FROM chart_of_accounts c
      ORDER BY account_code
    `);
    
    currentStatus.rows.forEach(account => {
      const parentInfo = account.parent_name ? ` (تحت: ${account.parent_name})` : ' (حساب رئيسي)';
      console.log(`   ${account.account_code}: ${account.account_name} - المستوى ${account.account_level} - رئيسي: ${account.is_main_account} - فرعي: ${account.is_sub_account}${parentInfo}`);
    });

    // 2. إصلاح المنطق: الحسابات الرئيسية هي التي ليس لها parent_id
    console.log('\n🔄 جاري إصلاح تصنيف الحسابات...');
    
    // إعادة تعيين جميع الحسابات أولاً
    await client.query(`
      UPDATE chart_of_accounts 
      SET is_main_account = FALSE, is_sub_account = FALSE
    `);
    console.log('   ✅ تم إعادة تعيين جميع التصنيفات');

    // تحديد الحسابات الرئيسية (التي ليس لها parent_id)
    const mainAccountsResult = await client.query(`
      UPDATE chart_of_accounts 
      SET is_main_account = TRUE, is_sub_account = FALSE
      WHERE parent_id IS NULL
      RETURNING account_code, account_name
    `);
    
    console.log(`   ✅ تم تصنيف ${mainAccountsResult.rowCount} حساب كحسابات رئيسية:`);
    mainAccountsResult.rows.forEach(account => {
      console.log(`      - ${account.account_code}: ${account.account_name}`);
    });

    // تحديد الحسابات الفرعية (التي لها parent_id)
    const subAccountsResult = await client.query(`
      UPDATE chart_of_accounts 
      SET is_main_account = FALSE, is_sub_account = TRUE
      WHERE parent_id IS NOT NULL
      RETURNING account_code, account_name, account_level
    `);
    
    console.log(`   ✅ تم تصنيف ${subAccountsResult.rowCount} حساب كحسابات فرعية:`);
    subAccountsResult.rows.forEach(account => {
      console.log(`      - ${account.account_code}: ${account.account_name} (المستوى ${account.account_level})`);
    });

    // 3. التحقق من صحة المستويات وإصلاحها
    console.log('\n🔍 التحقق من صحة المستويات...');
    
    // إصلاح المستويات بناءً على العلاقة الهرمية
    await client.query(`
      WITH RECURSIVE account_levels AS (
        -- الحسابات الرئيسية (المستوى 1)
        SELECT id, account_code, account_name, 1 as correct_level
        FROM chart_of_accounts 
        WHERE parent_id IS NULL
        
        UNION ALL
        
        -- الحسابات الفرعية (مستوى الأب + 1)
        SELECT c.id, c.account_code, c.account_name, al.correct_level + 1
        FROM chart_of_accounts c
        INNER JOIN account_levels al ON c.parent_id = al.id
      )
      UPDATE chart_of_accounts 
      SET account_level = al.correct_level
      FROM account_levels al
      WHERE chart_of_accounts.id = al.id
        AND chart_of_accounts.account_level != al.correct_level
    `);
    
    console.log('   ✅ تم إصلاح المستويات بناءً على التسلسل الهرمي');

    // 4. عرض النتائج بعد الإصلاح
    console.log('\n📊 الوضع بعد الإصلاح:');
    const fixedStatus = await client.query(`
      SELECT 
        account_code,
        account_name,
        account_level,
        parent_id,
        is_main_account,
        is_sub_account,
        (SELECT account_name FROM chart_of_accounts p WHERE p.id = c.parent_id) as parent_name,
        (SELECT COUNT(*) FROM chart_of_accounts child WHERE child.parent_id = c.id) as children_count
      FROM chart_of_accounts c
      ORDER BY account_code
    `);
    
    let currentLevel = 0;
    fixedStatus.rows.forEach(account => {
      if (account.account_level !== currentLevel) {
        currentLevel = account.account_level;
        console.log(`\n   📁 المستوى ${currentLevel}:`);
      }
      
      const indent = '   ' + '  '.repeat(account.account_level - 1);
      const parentInfo = account.parent_name ? ` (تحت: ${account.parent_name})` : '';
      const childrenInfo = account.children_count > 0 ? ` [${account.children_count} فرعي]` : '';
      const typeInfo = account.is_main_account ? ' [رئيسي]' : ' [فرعي]';
      
      console.log(`${indent}├── ${account.account_code}: ${account.account_name}${typeInfo}${parentInfo}${childrenInfo}`);
    });

    // 5. إحصائيات نهائية
    console.log('\n📈 إحصائيات نهائية:');
    const finalStats = await client.query(`
      SELECT 
        'الحسابات الرئيسية' as type,
        COUNT(*) as count
      FROM chart_of_accounts 
      WHERE is_main_account = TRUE
      
      UNION ALL
      
      SELECT 
        'الحسابات الفرعية' as type,
        COUNT(*) as count
      FROM chart_of_accounts 
      WHERE is_sub_account = TRUE
      
      UNION ALL
      
      SELECT 
        'إجمالي الحسابات' as type,
        COUNT(*) as count
      FROM chart_of_accounts
    `);
    
    finalStats.rows.forEach(stat => {
      console.log(`   ${stat.type}: ${stat.count}`);
    });

    // 6. التحقق من عدم وجود تضارب
    console.log('\n🔍 التحقق من عدم وجود تضارب:');
    const conflicts = await client.query(`
      SELECT 
        account_code,
        account_name,
        is_main_account,
        is_sub_account,
        parent_id
      FROM chart_of_accounts 
      WHERE (is_main_account = TRUE AND is_sub_account = TRUE)
         OR (is_main_account = FALSE AND is_sub_account = FALSE)
         OR (is_main_account = TRUE AND parent_id IS NOT NULL)
         OR (is_sub_account = TRUE AND parent_id IS NULL)
    `);
    
    if (conflicts.rows.length === 0) {
      console.log('   ✅ لا توجد تضاربات في التصنيف');
    } else {
      console.log('   ❌ توجد تضاربات في التصنيف:');
      conflicts.rows.forEach(conflict => {
        console.log(`      ${conflict.account_code}: ${conflict.account_name} - رئيسي: ${conflict.is_main_account}, فرعي: ${conflict.is_sub_account}, parent_id: ${conflict.parent_id}`);
      });
    }

    console.log('\n✅ تم إصلاح تصنيف الحسابات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إصلاح التصنيف:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  fixAccountClassification()
    .then(() => {
      console.log('🎉 تم إنجاز إصلاح التصنيف بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في إصلاح التصنيف:', error);
      process.exit(1);
    });
}

module.exports = { fixAccountClassification };
