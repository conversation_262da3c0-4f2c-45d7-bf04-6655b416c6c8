const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function createUnifiedVoucherSystem() {
  try {
    console.log('🏗️ إنشاء النظام الموحد للسندات والقيود...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إنشاء جدول مراكز التكلفة إذا لم يكن موجوداً
    console.log('\n📊 إنشاء جدول مراكز التكلفة...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS cost_centers (
        id SERIAL PRIMARY KEY,
        center_code VARCHAR(20) UNIQUE NOT NULL,
        center_name VARCHAR(255) NOT NULL,
        parent_id INTEGER REFERENCES cost_centers(id),
        center_level INTEGER DEFAULT 1,
        is_active BOOLEAN DEFAULT TRUE,
        description TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('   ✅ تم إنشاء جدول cost_centers');

    // إضافة بيانات تجريبية لمراكز التكلفة
    await client.query(`
      INSERT INTO cost_centers (center_code, center_name, center_level, description) 
      VALUES 
        ('CC001', 'الإدارة العامة', 1, 'مركز تكلفة الإدارة العامة'),
        ('CC002', 'القسم القانوني', 1, 'مركز تكلفة القسم القانوني'),
        ('CC003', 'المحاسبة والمالية', 1, 'مركز تكلفة المحاسبة والمالية'),
        ('CC004', 'الموارد البشرية', 1, 'مركز تكلفة الموارد البشرية')
      ON CONFLICT (center_code) DO NOTHING;
    `);
    console.log('   ✅ تم إضافة بيانات تجريبية لمراكز التكلفة');

    // 2. إنشاء الجدول الأب للسندات والقيود
    console.log('\n📋 إنشاء الجدول الأب للسندات والقيود...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS vouchers_master (
        id SERIAL PRIMARY KEY,
        voucher_number VARCHAR(50) UNIQUE NOT NULL,
        voucher_date DATE NOT NULL,
        voucher_type VARCHAR(20) NOT NULL CHECK (voucher_type IN ('سند صرف', 'سند قبض', 'قيد يومي')),
        cost_center_id INTEGER REFERENCES cost_centers(id),
        user_id INTEGER,
        description TEXT,
        total_amount DECIMAL(15,2) DEFAULT 0,
        status VARCHAR(20) DEFAULT 'مسودة' CHECK (status IN ('مسودة', 'معتمد', 'ملغي')),
        reference_number VARCHAR(100),
        notes TEXT,
        created_by INTEGER,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        approved_by INTEGER,
        approved_date TIMESTAMP
      );
    `);
    console.log('   ✅ تم إنشاء جدول vouchers_master');

    // 3. إنشاء جدول العمليات المالية
    console.log('\n💰 إنشاء جدول العمليات المالية...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS financial_transactions (
        id SERIAL PRIMARY KEY,
        voucher_id INTEGER NOT NULL REFERENCES vouchers_master(id) ON DELETE CASCADE,
        debit_amount DECIMAL(15,2) DEFAULT 0,
        credit_amount DECIMAL(15,2) DEFAULT 0,
        debit_account_id INTEGER REFERENCES chart_of_accounts(id),
        credit_account_id INTEGER REFERENCES chart_of_accounts(id),
        debit_account_code VARCHAR(20),
        credit_account_code VARCHAR(20),
        debit_description TEXT,
        credit_description TEXT,
        transaction_date DATE,
        cost_center_id INTEGER REFERENCES cost_centers(id),
        line_number INTEGER DEFAULT 1,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('   ✅ تم إنشاء جدول financial_transactions');

    // 4. إنشاء الفهارس للأداء
    console.log('\n🔍 إنشاء الفهارس...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_vouchers_master_date ON vouchers_master(voucher_date);',
      'CREATE INDEX IF NOT EXISTS idx_vouchers_master_type ON vouchers_master(voucher_type);',
      'CREATE INDEX IF NOT EXISTS idx_vouchers_master_status ON vouchers_master(status);',
      'CREATE INDEX IF NOT EXISTS idx_vouchers_master_cost_center ON vouchers_master(cost_center_id);',
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_voucher ON financial_transactions(voucher_id);',
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_date ON financial_transactions(transaction_date);',
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_debit_account ON financial_transactions(debit_account_id);',
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_credit_account ON financial_transactions(credit_account_id);',
      'CREATE INDEX IF NOT EXISTS idx_cost_centers_parent ON cost_centers(parent_id);'
    ];

    for (const indexQuery of indexes) {
      await client.query(indexQuery);
    }
    console.log('   ✅ تم إنشاء جميع الفهارس');

    // 5. إنشاء دوال مساعدة
    console.log('\n⚙️ إنشاء الدوال المساعدة...');
    
    // دالة لتوليد رقم السند التلقائي
    await client.query(`
      CREATE OR REPLACE FUNCTION generate_voucher_number(v_type VARCHAR(20))
      RETURNS VARCHAR(50) AS $$
      DECLARE
        prefix VARCHAR(10);
        next_number INTEGER;
        current_year VARCHAR(4);
      BEGIN
        current_year := EXTRACT(YEAR FROM CURRENT_DATE)::VARCHAR;
        
        CASE v_type
          WHEN 'سند صرف' THEN prefix := 'PAY';
          WHEN 'سند قبض' THEN prefix := 'REC';
          WHEN 'قيد يومي' THEN prefix := 'JE';
          ELSE prefix := 'VOC';
        END CASE;
        
        SELECT COALESCE(MAX(
          CAST(SUBSTRING(voucher_number FROM LENGTH(prefix || current_year) + 1) AS INTEGER)
        ), 0) + 1
        INTO next_number
        FROM vouchers_master 
        WHERE voucher_type = v_type 
          AND voucher_number LIKE prefix || current_year || '%';
        
        RETURN prefix || current_year || LPAD(next_number::VARCHAR, 4, '0');
      END;
      $$ LANGUAGE plpgsql;
    `);
    console.log('   ✅ تم إنشاء دالة generate_voucher_number');

    // دالة لحساب إجمالي السند
    await client.query(`
      CREATE OR REPLACE FUNCTION calculate_voucher_total(v_id INTEGER)
      RETURNS DECIMAL(15,2) AS $$
      DECLARE
        total_amount DECIMAL(15,2);
      BEGIN
        SELECT COALESCE(SUM(GREATEST(debit_amount, credit_amount)), 0)
        INTO total_amount
        FROM financial_transactions
        WHERE voucher_id = v_id;
        
        UPDATE vouchers_master 
        SET total_amount = total_amount,
            updated_date = CURRENT_TIMESTAMP
        WHERE id = v_id;
        
        RETURN total_amount;
      END;
      $$ LANGUAGE plpgsql;
    `);
    console.log('   ✅ تم إنشاء دالة calculate_voucher_total');

    // 6. إنشاء محفزات لتحديث الإجماليات
    console.log('\n🔄 إنشاء المحفزات...');
    
    await client.query(`
      CREATE OR REPLACE FUNCTION trigger_update_voucher_total()
      RETURNS TRIGGER AS $$
      BEGIN
        PERFORM calculate_voucher_total(COALESCE(NEW.voucher_id, OLD.voucher_id));
        RETURN COALESCE(NEW, OLD);
      END;
      $$ LANGUAGE plpgsql;
    `);

    await client.query(`
      DROP TRIGGER IF EXISTS trigger_financial_transactions_total ON financial_transactions;
      CREATE TRIGGER trigger_financial_transactions_total
        AFTER INSERT OR UPDATE OR DELETE ON financial_transactions
        FOR EACH ROW
        EXECUTE FUNCTION trigger_update_voucher_total();
    `);
    console.log('   ✅ تم إنشاء المحفزات');

    // 7. إضافة بيانات تجريبية
    console.log('\n📝 إضافة بيانات تجريبية...');
    
    // إضافة سندات تجريبية
    const sampleVouchers = [
      {
        type: 'سند قبض',
        description: 'قبض أتعاب قضية رقم 2024/001',
        cost_center: 2,
        amount: 5000
      },
      {
        type: 'سند صرف', 
        description: 'صرف مصروفات محكمة',
        cost_center: 2,
        amount: 500
      },
      {
        type: 'قيد يومي',
        description: 'قيد تسوية نهاية الشهر',
        cost_center: 3,
        amount: 1000
      }
    ];

    for (const voucher of sampleVouchers) {
      const voucherNumber = await client.query(`
        SELECT generate_voucher_number($1) as number
      `, [voucher.type]);

      const voucherResult = await client.query(`
        INSERT INTO vouchers_master (
          voucher_number, voucher_date, voucher_type, 
          cost_center_id, description, created_by
        ) VALUES ($1, CURRENT_DATE, $2, $3, $4, 1)
        RETURNING id
      `, [
        voucherNumber.rows[0].number,
        voucher.type,
        voucher.cost_center,
        voucher.description
      ]);

      const voucherId = voucherResult.rows[0].id;

      // إضافة عمليات مالية تجريبية
      if (voucher.type === 'سند قبض') {
        await client.query(`
          INSERT INTO financial_transactions (
            voucher_id, debit_amount, credit_amount,
            debit_account_code, credit_account_code,
            debit_description, credit_description,
            transaction_date, line_number
          ) VALUES 
          ($1, $2, 0, '1111', '', 'قبض نقدي', '', CURRENT_DATE, 1),
          ($1, 0, $2, '', '411', '', 'أتعاب قانونية', CURRENT_DATE, 2)
        `, [voucherId, voucher.amount]);
      } else if (voucher.type === 'سند صرف') {
        await client.query(`
          INSERT INTO financial_transactions (
            voucher_id, debit_amount, credit_amount,
            debit_account_code, credit_account_code,
            debit_description, credit_description,
            transaction_date, line_number
          ) VALUES 
          ($1, $2, 0, '511', '', 'مصروفات محكمة', '', CURRENT_DATE, 1),
          ($1, 0, $2, '', '1111', '', 'دفع نقدي', CURRENT_DATE, 2)
        `, [voucherId, voucher.amount]);
      }

      console.log(`   ✅ تم إضافة ${voucher.type}: ${voucherNumber.rows[0].number}`);
    }

    // 8. عرض ملخص النظام
    console.log('\n📊 ملخص النظام الجديد:');
    
    const summary = await client.query(`
      SELECT 
        voucher_type,
        COUNT(*) as count,
        SUM(total_amount) as total
      FROM vouchers_master
      GROUP BY voucher_type
      ORDER BY voucher_type
    `);

    summary.rows.forEach(row => {
      console.log(`   ${row.voucher_type}: ${row.count} سند، إجمالي: ${parseFloat(row.total || 0).toLocaleString()}`);
    });

    const transactionCount = await client.query(`
      SELECT COUNT(*) as count FROM financial_transactions
    `);
    console.log(`   إجمالي العمليات المالية: ${transactionCount.rows[0].count}`);

    const costCenterCount = await client.query(`
      SELECT COUNT(*) as count FROM cost_centers
    `);
    console.log(`   مراكز التكلفة: ${costCenterCount.rows[0].count}`);

    console.log('\n✅ تم إنشاء النظام الموحد للسندات والقيود بنجاح!');
    console.log('🎯 الجداول الجديدة:');
    console.log('   - vouchers_master (الجدول الأب للسندات والقيود)');
    console.log('   - financial_transactions (جدول العمليات المالية)');
    console.log('   - cost_centers (جدول مراكز التكلفة)');

  } catch (error) {
    console.error('❌ خطأ في إنشاء النظام:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  createUnifiedVoucherSystem()
    .then(() => {
      console.log('🎉 تم إنجاز إنشاء النظام بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في إنشاء النظام:', error);
      process.exit(1);
    });
}

module.exports = { createUnifiedVoucherSystem };
