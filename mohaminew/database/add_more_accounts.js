const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function addMoreAccounts() {
  try {
    console.log('🔄 إضافة المزيد من الحسابات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // حسابات إضافية للمستوى الثالث والرابع
    const additionalAccounts = [
      // تحت الأصول المتداولة - المخزون
      {
        account_code: '010103',
        account_name: 'المخزون',
        account_name_en: 'Inventory',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010103',
        account_level: 3,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'مخزون المكتب من القرطاسية والمستلزمات'
      },
      
      // حسابات فرعية للمخزون
      {
        account_code: '********',
        account_name: 'مخزون القرطاسية',
        account_name_en: 'Stationery Inventory',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010103',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'مخزون القرطاسية والأوراق'
      },
      
      {
        account_code: '********',
        account_name: 'مخزون المستلزمات المكتبية',
        account_name_en: 'Office Supplies Inventory',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010103',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'مخزون المستلزمات المكتبية'
      },

      // تحت الأصول المتداولة - المصروفات المدفوعة مقدماً
      {
        account_code: '010104',
        account_name: 'المصروفات المدفوعة مقدماً',
        account_name_en: 'Prepaid Expenses',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010104',
        account_level: 3,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'المصروفات المدفوعة مقدماً'
      },

      {
        account_code: '********',
        account_name: 'إيجار مدفوع مقدماً',
        account_name_en: 'Prepaid Rent',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010104',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'إيجار المكتب المدفوع مقدماً'
      },

      {
        account_code: '********',
        account_name: 'تأمين مدفوع مقدماً',
        account_name_en: 'Prepaid Insurance',
        level_1_code: '01',
        level_2_code: '0101',
        level_3_code: '010104',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'أقساط التأمين المدفوعة مقدماً'
      },

      // الأصول الثابتة
      {
        account_code: '0102',
        account_name: 'الأصول الثابتة',
        account_name_en: 'Fixed Assets',
        level_1_code: '01',
        level_2_code: '0102',
        account_level: 2,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'الأصول طويلة الأجل'
      },

      // الأثاث والمعدات
      {
        account_code: '010201',
        account_name: 'الأثاث والمعدات',
        account_name_en: 'Furniture and Equipment',
        level_1_code: '01',
        level_2_code: '0102',
        level_3_code: '010201',
        account_level: 3,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'أثاث ومعدات المكتب'
      },

      {
        account_code: '********',
        account_name: 'أثاث المكتب',
        account_name_en: 'Office Furniture',
        level_1_code: '01',
        level_2_code: '0102',
        level_3_code: '010201',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'أثاث المكتب والمكاتب والكراسي'
      },

      {
        account_code: '********',
        account_name: 'أجهزة الكمبيوتر',
        account_name_en: 'Computer Equipment',
        level_1_code: '01',
        level_2_code: '0102',
        level_3_code: '010201',
        level_4_code: '********',
        account_level: 4,
        account_type: 'أصول',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'أجهزة الكمبيوتر والطابعات'
      },

      // تحت الخصوم المتداولة - مصروفات مستحقة
      {
        account_code: '020103',
        account_name: 'المصروفات المستحقة',
        account_name_en: 'Accrued Expenses',
        level_1_code: '02',
        level_2_code: '0201',
        level_3_code: '020103',
        account_level: 3,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'المصروفات المستحقة غير المدفوعة'
      },

      {
        account_code: '********',
        account_name: 'إيجار مستحق',
        account_name_en: 'Accrued Rent',
        level_1_code: '02',
        level_2_code: '0201',
        level_3_code: '020103',
        level_4_code: '********',
        account_level: 4,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: true,
        description: 'إيجار المكتب المستحق'
      },

      {
        account_code: '********',
        account_name: 'فواتير كهرباء مستحقة',
        account_name_en: 'Accrued Electricity Bills',
        level_1_code: '02',
        level_2_code: '0201',
        level_3_code: '020103',
        level_4_code: '********',
        account_level: 4,
        account_type: 'خصوم',
        account_nature: 'دائن',
        allow_transactions: true,
        description: 'فواتير الكهرباء المستحقة'
      },

      // حسابات الإيرادات
      {
        account_code: '04',
        account_name: 'الإيرادات',
        account_name_en: 'Revenues',
        level_1_code: '04',
        account_level: 1,
        account_type: 'إيرادات',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'مجموعة الإيرادات الرئيسية'
      },

      {
        account_code: '0401',
        account_name: 'إيرادات الخدمات القانونية',
        account_name_en: 'Legal Services Revenue',
        level_1_code: '04',
        level_2_code: '0401',
        account_level: 2,
        account_type: 'إيرادات',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'إيرادات الخدمات القانونية'
      },

      {
        account_code: '040101',
        account_name: 'أتعاب المحاماة',
        account_name_en: 'Legal Fees',
        level_1_code: '04',
        level_2_code: '0401',
        level_3_code: '040101',
        account_level: 3,
        account_type: 'إيرادات',
        account_nature: 'دائن',
        allow_transactions: false,
        description: 'أتعاب المحاماة والاستشارات'
      },

      {
        account_code: '********',
        account_name: 'أتعاب القضايا المدنية',
        account_name_en: 'Civil Cases Fees',
        level_1_code: '04',
        level_2_code: '0401',
        level_3_code: '040101',
        level_4_code: '********',
        account_level: 4,
        account_type: 'إيرادات',
        account_nature: 'دائن',
        allow_transactions: true,
        description: 'أتعاب القضايا المدنية'
      },

      {
        account_code: '********',
        account_name: 'أتعاب القضايا التجارية',
        account_name_en: 'Commercial Cases Fees',
        level_1_code: '04',
        level_2_code: '0401',
        level_3_code: '040101',
        level_4_code: '********',
        account_level: 4,
        account_type: 'إيرادات',
        account_nature: 'دائن',
        allow_transactions: true,
        description: 'أتعاب القضايا التجارية'
      },

      // حسابات المصروفات
      {
        account_code: '05',
        account_name: 'المصروفات',
        account_name_en: 'Expenses',
        level_1_code: '05',
        account_level: 1,
        account_type: 'مصروفات',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'مجموعة المصروفات الرئيسية'
      },

      {
        account_code: '0501',
        account_name: 'المصروفات التشغيلية',
        account_name_en: 'Operating Expenses',
        level_1_code: '05',
        level_2_code: '0501',
        account_level: 2,
        account_type: 'مصروفات',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'المصروفات التشغيلية للمكتب'
      },

      {
        account_code: '050101',
        account_name: 'مصروفات الإيجار',
        account_name_en: 'Rent Expenses',
        level_1_code: '05',
        level_2_code: '0501',
        level_3_code: '050101',
        account_level: 3,
        account_type: 'مصروفات',
        account_nature: 'مدين',
        allow_transactions: false,
        description: 'مصروفات إيجار المكتب'
      },

      {
        account_code: '********',
        account_name: 'إيجار المكتب الرئيسي',
        account_name_en: 'Main Office Rent',
        level_1_code: '05',
        level_2_code: '0501',
        level_3_code: '050101',
        level_4_code: '********',
        account_level: 4,
        account_type: 'مصروفات',
        account_nature: 'مدين',
        allow_transactions: true,
        description: 'إيجار المكتب الرئيسي'
      }
    ];

    console.log('\n📊 إدراج الحسابات الإضافية...');
    
    for (const account of additionalAccounts) {
      // البحث عن parent_id إذا لم يكن المستوى الأول
      let parent_id = null;
      if (account.account_level > 1) {
        let parent_code = '';
        if (account.account_level === 2) {
          parent_code = account.level_1_code;
        } else if (account.account_level === 3) {
          parent_code = account.level_2_code;
        } else if (account.account_level === 4) {
          parent_code = account.level_3_code;
        }
        
        const parentResult = await client.query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [parent_code]
        );
        
        if (parentResult.rows.length > 0) {
          parent_id = parentResult.rows[0].id;
        }
      }
      
      // إدراج الحساب
      await client.query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_name_en, 
          level_1_code, level_2_code, level_3_code, level_4_code,
          account_level, parent_id, account_type, account_nature,
          allow_transactions, linked_table, auto_create_sub_accounts,
          opening_balance, current_balance, description
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        ON CONFLICT (account_code) DO NOTHING
      `, [
        account.account_code, account.account_name, account.account_name_en,
        account.level_1_code, account.level_2_code, account.level_3_code, account.level_4_code,
        account.account_level, parent_id, account.account_type, account.account_nature,
        account.allow_transactions, account.linked_table, account.auto_create_sub_accounts,
        0, 0, account.description
      ]);
      
      console.log(`   ✅ تم إدراج: ${account.account_code} - ${account.account_name}`);
    }

    console.log('\n✅ تم إدراج الحسابات الإضافية بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إدراج الحسابات:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  addMoreAccounts()
    .then(() => {
      console.log('\n🎉 تم إدراج الحسابات الإضافية بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل في إدراج الحسابات:', error);
      process.exit(1);
    });
}

module.exports = { addMoreAccounts };
