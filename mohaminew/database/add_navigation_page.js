// إضافة صفحة إدارة صفحات التنقل
const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function addNavigationPage() {
  try {
    await client.connect();
    console.log('🔗 تم الاتصال بقاعدة البيانات');

    // التحقق من وجود الصفحة
    const existingPage = await client.query(
      'SELECT id FROM navigation_pages WHERE page_url = $1',
      ['/settings/navigation-pages']
    );

    if (existingPage.rows.length === 0) {
      // إضافة صفحة إدارة صفحات التنقل
      await client.query(`
        INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords)
        VALUES ($1, $2, $3, $4, $5)
      `, [
        'صفحات التنقل',
        '/settings/navigation-pages',
        'إدارة صفحات النظام للبحث الذكي',
        'إعدادات',
        'صفحات,تنقل,navigation,pages,بحث,ذكي'
      ]);
      console.log('✅ تم إضافة صفحة إدارة صفحات التنقل بنجاح');
    } else {
      console.log('ℹ️ صفحة إدارة صفحات التنقل موجودة مسبقاً');
    }



    // عرض جميع الصفحات
    const result = await client.query('SELECT * FROM navigation_pages ORDER BY category, page_title');
    console.log(`\n📋 إجمالي الصفحات: ${result.rows.length}`);

  } catch (error) {
    console.error('❌ خطأ في إضافة الصفحة:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإضافة
addNavigationPage();
