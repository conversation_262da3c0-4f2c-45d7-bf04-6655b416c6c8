// إنشاء جدول تاريخ الأرصدة الافتتاحية ونسخ البيانات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function createOpeningBalancesHistory() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إنشاء جدول تاريخ الأرصدة الافتتاحية
    console.log('🔄 جاري إنشاء جدول تاريخ الأرصدة الافتتاحية...');
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS opening_balances_history (
        id SERIAL PRIMARY KEY,
        opening_balance_id INTEGER,
        account_name VARCHAR(255) NOT NULL,
        account_code VARCHAR(50) NOT NULL,
        old_debit_amount DECIMAL(15,2) DEFAULT 0,
        old_credit_amount DECIMAL(15,2) DEFAULT 0,
        new_debit_amount DECIMAL(15,2) DEFAULT 0,
        new_credit_amount DECIMAL(15,2) DEFAULT 0,
        old_balance_type VARCHAR(20),
        new_balance_type VARCHAR(20),
        change_type VARCHAR(50), -- 'INSERT', 'UPDATE', 'DELETE'
        changed_by VARCHAR(255), -- اسم المستخدم الذي قام بالتغيير
        change_reason TEXT, -- سبب التغيير
        change_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_date DATE DEFAULT CURRENT_DATE
      )
    `);
    console.log('✅ تم إنشاء جدول opening_balances_history');

    // نسخ البيانات الحالية من opening_balances إلى التاريخ
    console.log('🔄 جاري نسخ البيانات الحالية إلى جدول التاريخ...');
    
    const currentBalances = await client.query('SELECT * FROM opening_balances ORDER BY id');
    
    for (const balance of currentBalances.rows) {
      await client.query(`
        INSERT INTO opening_balances_history 
        (opening_balance_id, account_name, account_code, new_debit_amount, new_credit_amount, new_balance_type, change_type, changed_by, change_reason)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        balance.id,
        balance.account_name,
        balance.account_code,
        balance.debit_amount,
        balance.credit_amount,
        balance.balance_type,
        'INITIAL_IMPORT',
        'النظام',
        'استيراد البيانات الأولية'
      ]);
    }
    
    console.log(`✅ تم نسخ ${currentBalances.rows.length} رصيد إلى جدول التاريخ`);

    // إضافة بيانات تجريبية إضافية لتوضيح كيفية عمل التاريخ
    console.log('🔄 جاري إضافة بيانات تجريبية للتاريخ...');
    
    const historyData = [
      {
        opening_balance_id: 1,
        account_name: 'النقدية بالصندوق',
        account_code: '1001',
        old_debit_amount: 45000.00,
        old_credit_amount: 0.00,
        new_debit_amount: 50000.00,
        new_credit_amount: 0.00,
        old_balance_type: 'مدين',
        new_balance_type: 'مدين',
        change_type: 'UPDATE',
        changed_by: 'ماجد أحمد',
        change_reason: 'تصحيح رصيد الصندوق بعد الجرد'
      },
      {
        opening_balance_id: 2,
        account_name: 'البنك الأهلي',
        account_code: '1002',
        old_debit_amount: 140000.00,
        old_credit_amount: 0.00,
        new_debit_amount: 150000.00,
        new_credit_amount: 0.00,
        old_balance_type: 'مدين',
        new_balance_type: 'مدين',
        change_type: 'UPDATE',
        changed_by: 'محمد صالح',
        change_reason: 'تحديث رصيد البنك حسب كشف الحساب'
      },
      {
        opening_balance_id: 5,
        account_name: 'رأس المال',
        account_code: '3001',
        old_debit_amount: 0.00,
        old_credit_amount: 180000.00,
        new_debit_amount: 0.00,
        new_credit_amount: 200000.00,
        old_balance_type: 'دائن',
        new_balance_type: 'دائن',
        change_type: 'UPDATE',
        changed_by: 'ماجد أحمد',
        change_reason: 'زيادة رأس المال حسب قرار الشركاء'
      }
    ];

    for (const history of historyData) {
      await client.query(`
        INSERT INTO opening_balances_history 
        (opening_balance_id, account_name, account_code, old_debit_amount, old_credit_amount, 
         new_debit_amount, new_credit_amount, old_balance_type, new_balance_type, 
         change_type, changed_by, change_reason)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      `, [
        history.opening_balance_id,
        history.account_name,
        history.account_code,
        history.old_debit_amount,
        history.old_credit_amount,
        history.new_debit_amount,
        history.new_credit_amount,
        history.old_balance_type,
        history.new_balance_type,
        history.change_type,
        history.changed_by,
        history.change_reason
      ]);
    }
    
    console.log(`✅ تم إضافة ${historyData.length} سجل تاريخي تجريبي`);

    // إنشاء فهارس لتحسين الأداء
    console.log('🔄 جاري إنشاء الفهارس...');
    
    await client.query('CREATE INDEX IF NOT EXISTS idx_opening_balances_history_balance_id ON opening_balances_history(opening_balance_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_opening_balances_history_account_code ON opening_balances_history(account_code)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_opening_balances_history_change_date ON opening_balances_history(change_date)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_opening_balances_history_changed_by ON opening_balances_history(changed_by)');
    
    console.log('✅ تم إنشاء الفهارس');

    // التحقق من النتائج
    console.log('🔄 جاري التحقق من النتائج...');
    
    const totalHistory = await client.query('SELECT COUNT(*) FROM opening_balances_history');
    const totalCurrent = await client.query('SELECT COUNT(*) FROM opening_balances');
    
    console.log('📋 ملخص الأرصدة الافتتاحية:');
    console.log(`   ✅ الأرصدة الحالية: ${totalCurrent.rows[0].count} سجل`);
    console.log(`   ✅ تاريخ الأرصدة: ${totalHistory.rows[0].count} سجل`);

    // عرض أمثلة من التاريخ
    const sampleHistory = await client.query(`
      SELECT 
        account_name,
        change_type,
        old_debit_amount,
        new_debit_amount,
        changed_by,
        change_reason,
        change_date
      FROM opening_balances_history 
      ORDER BY change_date DESC 
      LIMIT 5
    `);
    
    console.log('📋 أمثلة من تاريخ التغييرات:');
    sampleHistory.rows.forEach(row => {
      console.log(`   - ${row.account_name}: ${row.change_type} بواسطة ${row.changed_by}`);
      console.log(`     السبب: ${row.change_reason}`);
      console.log(`     التاريخ: ${row.change_date}`);
      console.log('');
    });

    console.log('🎉 تم إنشاء جدول تاريخ الأرصدة الافتتاحية بنجاح!');
    console.log('📋 الفوائد:');
    console.log('   ✅ تتبع جميع التغييرات على الأرصدة');
    console.log('   ✅ معرفة من قام بالتغيير ومتى');
    console.log('   ✅ إمكانية المراجعة والتدقيق');
    console.log('   ✅ استعادة الأرصدة السابقة عند الحاجة');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء جدول التاريخ:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإنشاء
createOpeningBalancesHistory();
