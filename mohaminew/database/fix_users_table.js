// إصلاح جدول المستخدمين
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function fixUsersTable() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من هيكل جدول المستخدمين الحالي
    const usersColumns = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'users'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 هيكل جدول المستخدمين الحالي:');
    usersColumns.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    // إضافة الأعمدة المفقودة
    const columnsToAdd = [
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS device_id VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP'
    ];

    for (const sql of columnsToAdd) {
      try {
        await client.query(sql);
        console.log(`✅ تم تنفيذ: ${sql}`);
      } catch (error) {
        console.log(`⚠️ تم تخطي: ${error.message}`);
      }
    }

    // حذف عمود role إذا كان موجوداً
    try {
      await client.query('ALTER TABLE users DROP COLUMN IF EXISTS role');
      console.log('✅ تم حذف عمود role');
    } catch (error) {
      console.log(`⚠️ لم يتم حذف role: ${error.message}`);
    }

    // تحديث بيانات المستخدمين
    await client.query('TRUNCATE TABLE users RESTART IDENTITY CASCADE');
    
    const usersData = [
      { 
        username: 'admin', 
        email: '<EMAIL>',
        employee_id: 1, 
        password_hash: 'hashed_password_123',
        device_id: 'DEVICE_001',
        last_login: '2024-01-15 10:30:00',
        is_active: true 
      },
      { 
        username: 'majed.ahmed', 
        email: '<EMAIL>',
        employee_id: 1, 
        password_hash: 'hashed_password_456',
        device_id: 'DEVICE_002',
        last_login: '2024-01-14 09:15:00',
        is_active: true 
      },
      { 
        username: 'yahya.ali', 
        email: '<EMAIL>',
        employee_id: 2, 
        password_hash: 'hashed_password_789',
        device_id: 'DEVICE_003',
        last_login: '2024-01-13 14:20:00',
        is_active: true 
      },
      { 
        username: 'ahmed.saleh', 
        email: '<EMAIL>',
        employee_id: 3, 
        password_hash: 'hashed_password_101',
        device_id: 'DEVICE_004',
        last_login: '2024-01-12 11:45:00',
        is_active: true 
      }
    ];

    for (const user of usersData) {
      await client.query(`
        INSERT INTO users (username, email, employee_id, password_hash, device_id, last_login, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [user.username, user.email, user.employee_id, user.password_hash, user.device_id, user.last_login, user.is_active]);
    }
    console.log(`✅ تم إدراج ${usersData.length} مستخدم`);

    // التحقق من النتائج النهائية
    const finalCheck = await client.query(`
      SELECT u.*, e.name as employee_name 
      FROM users u 
      LEFT JOIN employees e ON u.employee_id = e.id 
      ORDER BY u.id
    `);
    
    console.log('📋 المستخدمين المحدثين:');
    finalCheck.rows.forEach(row => {
      console.log(`   - ${row.username} (${row.employee_name}) - آخر دخول: ${row.last_login}`);
    });

    console.log('🎉 تم إصلاح جدول المستخدمين بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح جدول المستخدمين:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإصلاح
fixUsersTable();
