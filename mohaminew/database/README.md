# تحديث قاعدة البيانات للنظام القانوني

## المتطلبات
- PostgreSQL 14 أو أحدث
- قاعدة بيانات باسم `mohammi`
- مستخدم `postgres` بكلمة مرور `yemen123`

## الجداول المطلوبة

### 1. جدول النسب المالية (lineages)
```sql
CREATE TABLE lineages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    admin_percentage DECIMAL(5,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);
```

### 2. جدول الخدمات (services)
```sql
CREATE TABLE services (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    created_date DATE DEFAULT CURRENT_DATE
);
```

### 3. جدول توزيع القضايا (case_distribution)
```sql
CREATE TABLE case_distribution (
    id SERIAL PRIMARY KEY,
    issue_id INTEGER REFERENCES issues(id) ON DELETE CASCADE,
    lineage_id INTEGER REFERENCES lineages(id) ON DELETE SET NULL,
    admin_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);
```

### 4. جدول تفاصيل توزيع الخدمات (service_distributions)
```sql
CREATE TABLE service_distributions (
    id SERIAL PRIMARY KEY,
    case_distribution_id INTEGER REFERENCES case_distribution(id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
    percentage DECIMAL(5,2) DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    lawyer_id INTEGER REFERENCES employees(id) ON DELETE SET NULL,
    created_date DATE DEFAULT CURRENT_DATE
);
```

## طريقة التحديث

### الطريقة الأولى: استخدام psql
```bash
# تشغيل ملف التحديث
psql -h localhost -p 5432 -U postgres -d mohammi -f update_tables.sql

# التحقق من النتائج
psql -h localhost -p 5432 -U postgres -d mohammi -f check_tables.sql
```

### الطريقة الثانية: استخدام pgAdmin
1. افتح pgAdmin
2. اتصل بقاعدة البيانات `mohammi`
3. افتح Query Tool
4. انسخ محتوى ملف `update_tables.sql`
5. شغل الاستعلام

### الطريقة الثالثة: استخدام ملفات bat (Windows)
```bash
# تشغيل التحديث
run_update.bat

# التحقق من النتائج
run_check.bat
```

## البيانات النموذجية

### النسب المالية
- نسب القضايا المدنية (15%)
- نسب القضايا التجارية (20%)
- نسب القضايا الجنائية (10%)
- نسب القضايا العمالية (25%)
- نسب القضايا العقارية (18%)
- نسب القضايا الإدارية (12%)
- نسب القضايا الأسرية (8%)
- نسب القضايا الضريبية (22%)

### الخدمات
- اعداد
- جلسة
- متابعة
- اشراف
- مصروفات قضائية
- استشارات قانونية
- صياغة عقود
- تمثيل قانوني

## التحقق من نجاح التحديث

بعد تشغيل التحديث، تأكد من:

1. **جدول lineages يحتوي على 3 أعمدة فقط:**
   - id
   - name
   - admin_percentage

2. **جدول services يحتوي على الخدمات الأساسية**

3. **الجداول الجديدة تم إنشاؤها:**
   - case_distribution
   - service_distributions

## استكشاف الأخطاء

### خطأ: psql غير معروف
```bash
# أضف PostgreSQL إلى PATH أو استخدم المسار الكامل
"C:\Program Files\PostgreSQL\14\bin\psql.exe" -h localhost -p 5432 -U postgres -d mohammi -f update_tables.sql
```

### خطأ: قاعدة البيانات غير موجودة
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE mohammi;
```

### خطأ: المستخدم غير موجود
```sql
-- إنشاء المستخدم
CREATE USER postgres WITH PASSWORD 'yemen123';
GRANT ALL PRIVILEGES ON DATABASE mohammi TO postgres;
```

## ملاحظات مهمة

1. **النسخ الاحتياطي:** قم بعمل نسخة احتياطية قبل التحديث
2. **البيئة:** هذا التحديث للبيئة التطويرية
3. **البيانات:** سيتم الاحتفاظ بالبيانات الموجودة
4. **الأداء:** تم إضافة فهارس لتحسين الأداء

## الدعم

في حالة وجود مشاكل:
1. تحقق من تشغيل PostgreSQL
2. تحقق من بيانات الاتصال
3. تحقق من صلاحيات المستخدم
4. راجع ملف الأخطاء في PostgreSQL
