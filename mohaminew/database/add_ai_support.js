const { Client } = require('pg');

async function addAISupport() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123',
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // تعديل جدول الرسائل لدعم الذكاء الاصطناعي
    console.log('🤖 إضافة دعم الذكاء الاصطناعي...');
    
    await client.query(`
      ALTER TABLE messages 
      DROP CONSTRAINT IF EXISTS messages_sender_type_check
    `);
    
    await client.query(`
      ALTER TABLE messages 
      ADD CONSTRAINT messages_sender_type_check 
      CHECK (sender_type IN ('user', 'client', 'ai'))
    `);
    
    console.log('✅ تم تعديل جدول الرسائل لدعم sender_type = "ai"');

    // تعديل جدول حالة القراءة أيضاً
    await client.query(`
      ALTER TABLE message_read_status 
      DROP CONSTRAINT IF EXISTS message_read_status_reader_type_check
    `);
    
    await client.query(`
      ALTER TABLE message_read_status 
      ADD CONSTRAINT message_read_status_reader_type_check 
      CHECK (reader_type IN ('user', 'client', 'ai'))
    `);
    
    console.log('✅ تم تعديل جدول حالة القراءة لدعم reader_type = "ai"');

    // اختبار إدراج رسالة من الذكاء الاصطناعي
    const testResult = await client.query(`
      INSERT INTO messages 
      (conversation_id, sender_type, sender_id, message_text, message_type, created_at)
      VALUES (1, 'ai', 0, 'اختبار رسالة من الذكاء الاصطناعي', 'text', CURRENT_TIMESTAMP)
      RETURNING id
    `);
    
    console.log(`✅ تم إدراج رسالة اختبار من الذكاء الاصطناعي برقم: ${testResult.rows[0].id}`);

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

addAISupport();