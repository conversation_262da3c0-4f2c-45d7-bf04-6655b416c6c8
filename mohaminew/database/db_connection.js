// ملف الاتصال بقاعدة البيانات وتطبيق التحديثات
const { Client } = require('pg');

// معلومات الاتصال من ملف mohammi.txt
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'yemen123'
};

// SQL للتحديثات المطلوبة
const updateQueries = [
  // 1. تحديث جدول النسب المالية
  `
  -- حذف الأعمدة غير المطلوبة إذا كانت موجودة
  DO $$ 
  BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'management_percentage') THEN
      ALTER TABLE lineages DROP COLUMN management_percentage;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'court_percentage') THEN
      ALTER TABLE lineages DROP COLUMN court_percentage;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'commission_percentage') THEN
      ALTER TABLE lineages DROP COLUMN commission_percentage;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'other_percentage') THEN
      ALTER TABLE lineages DROP COLUMN other_percentage;
    END IF;
    
    -- إعادة تسمية group_name إلى name إذا كان موجوداً
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'group_name') THEN
      ALTER TABLE lineages RENAME COLUMN group_name TO name;
    END IF;
    
    -- إضافة admin_percentage إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'lineages' AND column_name = 'admin_percentage') THEN
      ALTER TABLE lineages ADD COLUMN admin_percentage DECIMAL(5,2) DEFAULT 0;
    END IF;
  END $$;
  `,

  // 2. إنشاء جدول النسب المالية إذا لم يكن موجوداً
  `
  CREATE TABLE IF NOT EXISTS lineages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    admin_percentage DECIMAL(5,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,

  // 3. حذف البيانات القديمة وإدراج البيانات الجديدة
  `
  TRUNCATE TABLE lineages RESTART IDENTITY CASCADE;
  
  INSERT INTO lineages (name, admin_percentage, created_date) VALUES
  ('نسب القضايا المدنية', 15.0, '2024-01-01'),
  ('نسب القضايا التجارية', 20.0, '2024-01-02'),
  ('نسب القضايا الجنائية', 10.0, '2024-01-03'),
  ('نسب القضايا العمالية', 25.0, '2024-01-04'),
  ('نسب القضايا العقارية', 18.0, '2024-01-05'),
  ('نسب القضايا الإدارية', 12.0, '2024-01-06'),
  ('نسب القضايا الأسرية', 8.0, '2024-01-07'),
  ('نسب القضايا الضريبية', 22.0, '2024-01-08');
  `,

  // 4. إنشاء جدول الخدمات
  `
  CREATE TABLE IF NOT EXISTS services (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    lineage_id INTEGER REFERENCES lineages(id) ON DELETE SET NULL,
    employee_id INTEGER,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,

  // 5. إدراج بيانات الخدمات
  `
  TRUNCATE TABLE services RESTART IDENTITY CASCADE;
  
  INSERT INTO services (name, lineage_id, employee_id, created_date) VALUES
  ('اعداد', 1, 1, '2024-01-01'),
  ('جلسة', 1, 2, '2024-01-01'),
  ('متابعة', 2, 3, '2024-01-01'),
  ('اشراف', 2, 4, '2024-01-01'),
  ('مصروفات قضائية', 3, 1, '2024-01-01'),
  ('استشارات قانونية', 4, 2, '2024-01-01'),
  ('صياغة عقود', 5, 3, '2024-01-01'),
  ('تمثيل قانوني', 6, 4, '2024-01-01');
  `,

  // 6. تحديث جدول المحاكم
  `
  ALTER TABLE courts 
  ADD COLUMN IF NOT EXISTS employee_id INTEGER,
  ADD COLUMN IF NOT EXISTS issue_id INTEGER;
  `,

  // 7. إنشاء جدول توزيع القضايا
  `
  CREATE TABLE IF NOT EXISTS case_distribution (
    id SERIAL PRIMARY KEY,
    issue_id INTEGER,
    lineage_id INTEGER REFERENCES lineages(id) ON DELETE SET NULL,
    admin_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,

  // 8. إنشاء جدول تفاصيل توزيع الخدمات
  `
  CREATE TABLE IF NOT EXISTS service_distributions (
    id SERIAL PRIMARY KEY,
    case_distribution_id INTEGER REFERENCES case_distribution(id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
    percentage DECIMAL(5,2) DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    lawyer_id INTEGER,
    created_date DATE DEFAULT CURRENT_DATE
  );
  `,

  // 9. إنشاء الفهارس
  `
  CREATE INDEX IF NOT EXISTS idx_lineages_name ON lineages(name);
  CREATE INDEX IF NOT EXISTS idx_services_name ON services(name);
  CREATE INDEX IF NOT EXISTS idx_services_lineage_id ON services(lineage_id);
  CREATE INDEX IF NOT EXISTS idx_services_employee_id ON services(employee_id);
  CREATE INDEX IF NOT EXISTS idx_courts_employee_id ON courts(employee_id);
  CREATE INDEX IF NOT EXISTS idx_courts_issue_id ON courts(issue_id);
  `
];

async function updateDatabase() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    console.log('🔄 جاري تطبيق التحديثات...');
    
    for (let i = 0; i < updateQueries.length; i++) {
      try {
        console.log(`🔄 تطبيق التحديث ${i + 1}/${updateQueries.length}...`);
        await client.query(updateQueries[i]);
        console.log(`✅ تم تطبيق التحديث ${i + 1} بنجاح`);
      } catch (error) {
        console.error(`❌ خطأ في التحديث ${i + 1}:`, error.message);
      }
    }

    // التحقق من النتائج
    console.log('🔄 جاري التحقق من النتائج...');
    
    const lineagesResult = await client.query('SELECT COUNT(*) FROM lineages');
    console.log(`✅ جدول النسب المالية: ${lineagesResult.rows[0].count} سجل`);
    
    const servicesResult = await client.query('SELECT COUNT(*) FROM services');
    console.log(`✅ جدول الخدمات: ${servicesResult.rows[0].count} سجل`);

    // عرض هيكل جدول النسب المالية
    const lineagesStructure = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'lineages' 
      ORDER BY ordinal_position
    `);
    
    console.log('📋 هيكل جدول النسب المالية:');
    lineagesStructure.rows.forEach(row => {
      console.log(`   - ${row.column_name}: ${row.data_type}`);
    });

    // عرض بيانات النسب المالية
    const lineagesData = await client.query('SELECT * FROM lineages ORDER BY id');
    console.log('📋 بيانات النسب المالية:');
    lineagesData.rows.forEach(row => {
      console.log(`   - ${row.id}: ${row.name} (${row.admin_percentage}%)`);
    });

    console.log('🎉 تم تحديث قاعدة البيانات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
    console.error('تأكد من:');
    console.error('- تشغيل PostgreSQL');
    console.error('- صحة معلومات الاتصال');
    console.error('- وجود قاعدة البيانات mohammi');
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التحديث
updateDatabase();
