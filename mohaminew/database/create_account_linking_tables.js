// إنشاء جداول نظام ربط الحسابات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function createAccountLinkingTables() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🚀 إنشاء جداول نظام ربط الحسابات...');
    await client.connect();

    // 1. إنشاء جدول إعدادات ربط الحسابات
    console.log('📋 إنشاء جدول account_linking_settings...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS account_linking_settings (
        id SERIAL PRIMARY KEY,
        account_type VARCHAR(100) NOT NULL,
        account_name VARCHAR(255) NOT NULL,
        account_code VARCHAR(20) NOT NULL,
        linked_table VARCHAR(100),
        description TEXT,
        is_enabled BOOLEAN DEFAULT TRUE,
        auto_create BOOLEAN DEFAULT FALSE,
        icon VARCHAR(50) DEFAULT 'DollarSign',
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 2. التأكد من وجود جدول دليل الحسابات بالهيكل المطلوب
    console.log('📊 التأكد من جدول chart_of_accounts...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS chart_of_accounts (
        id SERIAL PRIMARY KEY,
        account_code VARCHAR(20) UNIQUE NOT NULL,
        account_name VARCHAR(255) NOT NULL,
        account_name_en VARCHAR(255),
        account_type VARCHAR(50) NOT NULL,
        parent_id INTEGER REFERENCES chart_of_accounts(id),
        account_level INTEGER DEFAULT 1,
        is_main_account BOOLEAN DEFAULT FALSE,
        is_sub_account BOOLEAN DEFAULT FALSE,
        is_control_account BOOLEAN DEFAULT FALSE,
        linked_table VARCHAR(100),
        auto_create_sub_accounts BOOLEAN DEFAULT FALSE,
        sub_account_prefix VARCHAR(10),
        account_nature VARCHAR(20) DEFAULT 'مدين',
        opening_balance DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        allow_posting BOOLEAN DEFAULT TRUE,
        notes TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 3. إنشاء جدول الحسابات الفرعية المربوطة
    console.log('🔗 إنشاء جدول account_sub_links...');
    await client.query(`
      CREATE TABLE IF NOT EXISTS account_sub_links (
        id SERIAL PRIMARY KEY,
        main_account_id INTEGER REFERENCES chart_of_accounts(id) ON DELETE CASCADE,
        linked_table VARCHAR(100) NOT NULL,
        linked_record_id INTEGER NOT NULL,
        sub_account_code VARCHAR(50) UNIQUE NOT NULL,
        sub_account_name VARCHAR(255) NOT NULL,
        opening_balance DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(100),
        notes TEXT,
        UNIQUE(main_account_id, linked_table, linked_record_id)
      )
    `);

    // 4. إدراج الإعدادات الافتراضية
    console.log('⚙️ إدراج الإعدادات الافتراضية...');
    const defaultSettings = [
      {
        account_type: 'revenue',
        account_name: 'حساب الإيرادات الرئيسي',
        account_code: '4000',
        description: 'الحساب الرئيسي لجميع إيرادات المكتب من القضايا والخدمات القانونية',
        is_enabled: true,
        auto_create: true,
        icon: 'TrendingUp'
      },
      {
        account_type: 'expenses',
        account_name: 'حساب المصروفات الرئيسي',
        account_code: '5000',
        description: 'الحساب الرئيسي لجميع مصروفات المكتب التشغيلية والإدارية',
        is_enabled: true,
        auto_create: true,
        icon: 'TrendingDown'
      },
      {
        account_type: 'capital',
        account_name: 'حساب رأس المال',
        account_code: '3000',
        description: 'حساب رأس المال المستثمر في المكتب',
        is_enabled: true,
        auto_create: true,
        icon: 'Briefcase'
      },
      {
        account_type: 'cash',
        account_name: 'الصندوق الرئيسي',
        account_code: '1111',
        description: 'صندوق النقدية الرئيسي للمكتب',
        is_enabled: true,
        auto_create: true,
        icon: 'Wallet'
      },
      {
        account_type: 'clients_control',
        account_name: 'الحساب الرئيسي للعملاء',
        account_code: '1121',
        linked_table: 'clients',
        description: 'الحساب المراقب لجميع حسابات العملاء الفردية',
        is_enabled: true,
        auto_create: true,
        icon: 'Users'
      },
      {
        account_type: 'employees_control',
        account_name: 'الحساب الرئيسي للموظفين',
        account_code: '1122',
        linked_table: 'employees',
        description: 'الحساب المراقب لجميع حسابات الموظفين الفردية',
        is_enabled: true,
        auto_create: true,
        icon: 'Building'
      },
      {
        account_type: 'intermediate_accounts',
        account_name: 'الحسابات الوسيطة',
        account_code: '2500',
        description: 'حسابات وسيطة للعمليات المحاسبية المؤقتة',
        is_enabled: true,
        auto_create: true,
        icon: 'Home'
      }
    ];

    for (const setting of defaultSettings) {
      await client.query(`
        INSERT INTO account_linking_settings 
        (account_type, account_name, account_code, linked_table, description, is_enabled, auto_create, icon)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT DO NOTHING
      `, [
        setting.account_type,
        setting.account_name,
        setting.account_code,
        setting.linked_table || null,
        setting.description,
        setting.is_enabled,
        setting.auto_create,
        setting.icon
      ]);
    }

    // 5. إنشاء الفهارس للأداء
    console.log('📈 إنشاء الفهارس...');
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_account_linking_type ON account_linking_settings(account_type)',
      'CREATE INDEX IF NOT EXISTS idx_account_linking_code ON account_linking_settings(account_code)',
      'CREATE INDEX IF NOT EXISTS idx_chart_accounts_parent ON chart_of_accounts(parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_chart_accounts_type ON chart_of_accounts(account_type)',
      'CREATE INDEX IF NOT EXISTS idx_chart_accounts_linked_table ON chart_of_accounts(linked_table)',
      'CREATE INDEX IF NOT EXISTS idx_chart_accounts_code ON chart_of_accounts(account_code)',
      'CREATE INDEX IF NOT EXISTS idx_sub_links_main_account ON account_sub_links(main_account_id)',
      'CREATE INDEX IF NOT EXISTS idx_sub_links_table ON account_sub_links(linked_table)',
      'CREATE INDEX IF NOT EXISTS idx_sub_links_record ON account_sub_links(linked_record_id)'
    ];

    for (const index of indexes) {
      await client.query(index);
    }

    // 6. إنشاء دوال مساعدة
    console.log('🔧 إنشاء الدوال المساعدة...');
    
    // دالة للحصول على رصيد الحساب
    await client.query(`
      CREATE OR REPLACE FUNCTION get_account_balance(account_code VARCHAR)
      RETURNS DECIMAL(15,2) AS $$
      BEGIN
        RETURN (SELECT COALESCE(current_balance, 0) FROM chart_of_accounts WHERE account_code = $1);
      END;
      $$ LANGUAGE plpgsql;
    `);

    // دالة لإنشاء حساب فرعي تلقائياً
    await client.query(`
      CREATE OR REPLACE FUNCTION create_sub_account(
        p_main_account_id INTEGER,
        p_table_name VARCHAR,
        p_record_id INTEGER,
        p_record_name VARCHAR
      ) RETURNS VARCHAR AS $$
      DECLARE
        v_prefix VARCHAR;
        v_sub_code VARCHAR;
        v_sub_name VARCHAR;
      BEGIN
        SELECT sub_account_prefix INTO v_prefix 
        FROM chart_of_accounts 
        WHERE id = p_main_account_id;
        
        v_sub_code := (SELECT account_code FROM chart_of_accounts WHERE id = p_main_account_id) 
                     || '-' || LPAD(p_record_id::TEXT, 4, '0');
        v_sub_name := 'حساب ' || p_record_name;
        
        INSERT INTO account_sub_links 
        (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
        VALUES (p_main_account_id, p_table_name, p_record_id, v_sub_code, v_sub_name, 'النظام')
        ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING;
        
        RETURN v_sub_code;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // عرض النتائج
    const settingsCount = await client.query('SELECT COUNT(*) as count FROM account_linking_settings');
    const accountsCount = await client.query('SELECT COUNT(*) as count FROM chart_of_accounts');
    const subLinksCount = await client.query('SELECT COUNT(*) as count FROM account_sub_links');

    console.log('✅ تم إنشاء جداول نظام ربط الحسابات بنجاح!');
    console.log('📊 الإحصائيات:');
    console.log(`   - إعدادات الربط: ${settingsCount.rows[0].count}`);
    console.log(`   - الحسابات: ${accountsCount.rows[0].count}`);
    console.log(`   - الحسابات الفرعية: ${subLinksCount.rows[0].count}`);

    console.log('\n🎯 الميزات المتاحة:');
    console.log('   ✅ إعدادات ربط الحسابات');
    console.log('   ✅ دليل الحسابات المتكامل');
    console.log('   ✅ الحسابات الفرعية المربوطة');
    console.log('   ✅ دوال مساعدة للعمليات');
    console.log('   ✅ فهارس للأداء المحسن');

    console.log('\n📋 الخطوات التالية:');
    console.log('   1. افتح صفحة ربط الحسابات: /accounting/account-linking');
    console.log('   2. قم بإنشاء الحسابات الأساسية');
    console.log('   3. تحقق من دليل الحسابات: /accounting/chart-of-accounts');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

createAccountLinkingTables();