// نسخ بيانات المحافظات إلى قاعدة البيانات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'yemen123'
};

// البيانات التجريبية للمحافظات من API
const governoratesData = [
  { name: 'صنعاء', code: 'SA', region: 'الوسط', population: 3500000, is_capital: true },
  { name: 'عدن', code: 'AD', region: 'الجنوب', population: 1200000, is_capital: false },
  { name: 'تعز', code: 'TA', region: 'الوسط', population: 2800000, is_capital: false },
  { name: 'الحديدة', code: 'HD', region: 'الغرب', population: 2400000, is_capital: false },
  { name: 'إب', code: 'IB', region: 'الوسط', population: 2200000, is_capital: false },
  { name: 'ذمار', code: 'DH', region: 'الوسط', population: 1800000, is_capital: false },
  { name: 'حضرموت', code: 'HA', region: 'الشرق', population: 1500000, is_capital: false },
  { name: 'لحج', code: 'LA', region: 'الجنوب', population: 900000, is_capital: false },
  { name: 'مأرب', code: 'MA', region: 'الشرق', population: 400000, is_capital: false },
  { name: 'أبين', code: 'AB', region: 'الجنوب', population: 600000, is_capital: false },
  { name: 'الجوف', code: 'JO', region: 'الشمال', population: 500000, is_capital: false },
  { name: 'صعدة', code: 'SA2', region: 'الشمال', population: 700000, is_capital: false }
];

async function migrateGovernorates() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من وجود الجدول
    const tableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'governorates'
      )
    `);

    if (!tableExists.rows[0].exists) {
      // إنشاء جدول المحافظات
      console.log('🔄 جاري إنشاء جدول المحافظات...');
      await client.query(`
        CREATE TABLE governorates (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          code VARCHAR(10) UNIQUE NOT NULL,
          region VARCHAR(100),
          population INTEGER,
          is_capital BOOLEAN DEFAULT false,
          is_active BOOLEAN DEFAULT true,
          created_date DATE DEFAULT CURRENT_DATE
        )
      `);
      console.log('✅ تم إنشاء جدول المحافظات');
    } else {
      console.log('📋 جدول المحافظات موجود مسبقاً');
    }

    // حذف البيانات القديمة
    await client.query('TRUNCATE TABLE governorates RESTART IDENTITY CASCADE');
    console.log('🔄 تم حذف البيانات القديمة');

    // إدراج البيانات الجديدة
    console.log('🔄 جاري إدراج بيانات المحافظات...');
    for (const gov of governoratesData) {
      await client.query(`
        INSERT INTO governorates (name, code, region, population, is_capital)
        VALUES ($1, $2, $3, $4, $5)
      `, [gov.name, gov.code, gov.region, gov.population, gov.is_capital]);
    }

    // التحقق من النتائج
    const result = await client.query('SELECT COUNT(*) FROM governorates');
    console.log(`✅ تم إدراج ${result.rows[0].count} محافظة`);

    // عرض البيانات
    const data = await client.query('SELECT * FROM governorates ORDER BY id');
    console.log('📋 المحافظات المدرجة:');
    data.rows.forEach(row => {
      const capital = row.is_capital ? ' (العاصمة)' : '';
      console.log(`   - ${row.name} (${row.code}) - ${row.region} - ${row.population.toLocaleString()} نسمة${capital}`);
    });

    console.log('🎉 تم نسخ بيانات المحافظات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في نسخ بيانات المحافظات:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل النسخ
migrateGovernorates();
