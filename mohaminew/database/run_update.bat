@echo off
echo ========================================
echo تحديث قاعدة البيانات للنظام القانوني
echo ========================================
echo.

echo جاري الاتصال بقاعدة البيانات...
echo Database: mohammi
echo User: postgres
echo Host: localhost
echo Port: 5432
echo.

echo جاري تنفيذ التحديثات...
psql -h localhost -p 5432 -U postgres -d mohammi -f update_tables.sql

echo.
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم تحديث قاعدة البيانات بنجاح!
    echo.
    echo الجداول المحدثة:
    echo - lineages (النسب المالية)
    echo - services (الخدمات)
    echo - case_distribution (توزيع القضايا)
    echo - service_distributions (تفاصيل توزيع الخدمات)
    echo.
) else (
    echo ❌ فشل في تحديث قاعدة البيانات!
    echo تأكد من:
    echo - تشغيل PostgreSQL
    echo - صحة بيانات الاتصال
    echo - وجود قاعدة البيانات mohammi
    echo.
)

echo اضغط أي مفتاح للخروج...
pause > nul
