// تحديث جدول companies لإضافة الأعمدة الجديدة
const { Client } = require('pg');

const dbConfig = {
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: 'yemen123',
  port: 5432,
};

async function updateCompaniesTable() {
  const client = new Client(dbConfig);

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات...');

    // التحقق من وجود الأعمدة الجديدة
    console.log('🔍 فحص بنية جدول companies...');

    const checkColumns = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'companies'
      AND table_schema = 'public'
    `);

    const existingColumns = checkColumns.rows.map(row => row.column_name);
    console.log('الأعمدة الموجودة:', existingColumns);

    // قائمة الأعمدة الجديدة المطلوبة
    const newColumns = [
      { name: 'legal_name', type: 'VARCHAR(255)' },
      { name: 'registration_number', type: 'VARCHAR(100)' },
      { name: 'city', type: 'VARCHAR(100)' },
      { name: 'country', type: 'VARCHAR(100)' },
      { name: 'tax_number', type: 'VARCHAR(50)' },
      { name: 'commercial_register', type: 'VARCHAR(50)' },
      { name: 'logo_url', type: 'VARCHAR(500)' },
      { name: 'logo_right_text', type: 'TEXT' },
      { name: 'logo_left_text', type: 'TEXT' },
      { name: 'logo_image_url', type: 'VARCHAR(500)' },
      { name: 'legal_form', type: 'VARCHAR(100)' },
      { name: 'capital', type: 'DECIMAL(15,2)' },
      { name: 'description', type: 'TEXT' },
      { name: 'is_active', type: 'BOOLEAN DEFAULT true' },
      { name: 'updated_at', type: 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP' }
    ];

    // إضافة الأعمدة المفقودة
    for (const column of newColumns) {
      if (!existingColumns.includes(column.name)) {
        console.log(`➕ إضافة عمود: ${column.name}`);
        await client.query(`
          ALTER TABLE companies
          ADD COLUMN ${column.name} ${column.type}
        `);
      } else {
        console.log(`✅ العمود ${column.name} موجود بالفعل`);
      }
    }

    // التحقق من وجود بيانات في الجدول
    const countResult = await client.query('SELECT COUNT(*) FROM companies');
    const recordCount = parseInt(countResult.rows[0].count);

    console.log(`📊 عدد السجلات الموجودة: ${recordCount}`);

    // إذا لم توجد بيانات، إضافة شركة افتراضية
    if (recordCount === 0) {
      console.log('📝 إضافة شركة افتراضية...');

      await client.query(`
        INSERT INTO companies (
          name, legal_name, registration_number, address, city, country,
          phone, email, website, tax_number, commercial_register,
          logo_url, logo_right_text, logo_left_text, logo_image_url,
          established_date, legal_form, capital, description, is_active
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
      `, [
        'مكتب المحاماة والاستشارات القانونية',
        'شركة المحاماة والاستشارات القانونية المحدودة',
        'CR-2024-001',
        'شارع الزبيري، مبنى رقم 15، الطابق الثالث',
        'صنعاء',
        'اليمن',
        '+967-1-123456',
        '<EMAIL>',
        'www.legalfirm.ye',
        'TAX-*********',
        'COM-2024-001',
        '/images/company-logo.png',
        'مكتب المحاماة والاستشارات القانونية',
        'العدالة والنزاهة في خدمة القانون',
        '/images/logo.png',
        '2020-01-15',
        'شركة محدودة المسؤولية',
        1000000,
        'مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات',
        true
      ]);

      console.log('✅ تم إضافة الشركة الافتراضية بنجاح');
    }

    // عرض البنية النهائية للجدول
    console.log('\n📋 البنية النهائية لجدول companies:');
    const finalStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'companies'
      AND table_schema = 'public'
      ORDER BY ordinal_position
    `);

    finalStructure.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? 'NOT NULL' : ''} ${row.column_default ? `DEFAULT ${row.column_default}` : ''}`);
    });

    console.log('\n🎉 تم تحديث جدول companies بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في تحديث الجدول:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التحديث
updateCompaniesTable();
