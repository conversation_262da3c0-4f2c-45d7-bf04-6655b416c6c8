// إصلاح قيود جدول دليل الحسابات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function fixChartConstraints() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من القيود الموجودة
    console.log('🔄 جاري التحقق من القيود الموجودة...');
    
    const constraints = await client.query(`
      SELECT constraint_name, constraint_type 
      FROM information_schema.table_constraints 
      WHERE table_name = 'chart_of_accounts'
    `);
    
    console.log('📋 القيود الموجودة:');
    constraints.rows.forEach(row => {
      console.log(`   - ${row.constraint_name}: ${row.constraint_type}`);
    });

    // حذف القيود المشكلة
    console.log('🔄 جاري حذف القيود المشكلة...');
    
    try {
      await client.query('ALTER TABLE chart_of_accounts DROP CONSTRAINT IF EXISTS chart_of_accounts_linked_table_check');
      console.log('   ✅ تم حذف قيد linked_table');
    } catch (error) {
      console.log('   ⚠️  قيد linked_table غير موجود');
    }

    try {
      await client.query('ALTER TABLE chart_of_accounts DROP CONSTRAINT IF EXISTS chart_of_accounts_auto_generate_check');
      console.log('   ✅ تم حذف قيد auto_generate');
    } catch (error) {
      console.log('   ⚠️  قيد auto_generate غير موجود');
    }

    // إضافة البيانات المطلوبة
    console.log('🔄 جاري إضافة البيانات...');

    const accountsData = [
      {
        code: '5100',
        name: 'مصروفات المحاكم',
        type: 'مصروفات',
        nature: 'مدين',
        level: 2,
        parent_code: '5000',
        linked_table: 'courts',
        auto_create: true,
        prefix: 'CRT'
      },
      {
        code: '5200',
        name: 'مصروفات الفروع',
        type: 'مصروفات',
        nature: 'مدين',
        level: 2,
        parent_code: '5000',
        linked_table: 'branches',
        auto_create: true,
        prefix: 'BR'
      },
      {
        code: '5300',
        name: 'مراكز التكلفة',
        type: 'مصروفات',
        nature: 'مدين',
        level: 2,
        parent_code: '5000',
        linked_table: 'cost_centers',
        auto_create: true,
        prefix: 'CC'
      }
    ];

    for (const account of accountsData) {
      // البحث عن الحساب الأب
      let parentId = null;
      if (account.parent_code) {
        const parentResult = await client.query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [account.parent_code]
        );
        if (parentResult.rows.length > 0) {
          parentId = parentResult.rows[0].id;
        }
      }

      // التحقق من وجود الحساب
      const existingAccount = await client.query(
        'SELECT id FROM chart_of_accounts WHERE account_code = $1',
        [account.code]
      );

      if (existingAccount.rows.length === 0) {
        await client.query(`
          INSERT INTO chart_of_accounts 
          (account_code, account_name, account_type, parent_id, account_level, 
           is_main_account, is_sub_account, linked_table, auto_create_sub_accounts, 
           sub_account_prefix, account_nature, is_active)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        `, [
          account.code,
          account.name,
          account.type,
          parentId,
          account.level,
          false,
          true,
          account.linked_table,
          account.auto_create,
          account.prefix,
          account.nature,
          true
        ]);
        console.log(`   ✅ تم إضافة حساب: ${account.code} - ${account.name}`);
      } else {
        // تحديث الحساب الموجود
        await client.query(`
          UPDATE chart_of_accounts 
          SET parent_id = $1, account_level = $2, is_main_account = $3, 
              is_sub_account = $4, linked_table = $5, auto_create_sub_accounts = $6,
              sub_account_prefix = $7, account_nature = $8
          WHERE account_code = $9
        `, [
          parentId,
          account.level,
          false,
          true,
          account.linked_table,
          account.auto_create,
          account.prefix,
          account.nature,
          account.code
        ]);
        console.log(`   ✅ تم تحديث حساب: ${account.code} - ${account.name}`);
      }
    }

    // إنشاء جدول ربط الحسابات
    console.log('🔄 جاري إنشاء جدول ربط الحسابات...');
    
    await client.query(`
      CREATE TABLE IF NOT EXISTS account_links (
        id SERIAL PRIMARY KEY,
        main_account_id INTEGER REFERENCES chart_of_accounts(id),
        linked_table VARCHAR(100) NOT NULL,
        linked_record_id INTEGER NOT NULL,
        sub_account_code VARCHAR(50),
        sub_account_name VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(100),
        notes TEXT,
        UNIQUE(main_account_id, linked_table, linked_record_id)
      )
    `);
    console.log('✅ تم إنشاء جدول account_links');

    // إنشاء فهارس
    await client.query('CREATE INDEX IF NOT EXISTS idx_account_links_main_account ON account_links(main_account_id)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_account_links_table ON account_links(linked_table)');
    await client.query('CREATE INDEX IF NOT EXISTS idx_account_links_record ON account_links(linked_record_id)');
    console.log('✅ تم إنشاء فهارس جدول account_links');

    // إضافة بيانات تجريبية لربط الحسابات
    console.log('🔄 جاري إضافة بيانات تجريبية للربط...');

    // ربط الموكلين بحساب الموكلين
    const clientsAccount = await client.query(
      'SELECT id FROM chart_of_accounts WHERE account_code = $1',
      ['1120']
    );

    if (clientsAccount.rows.length > 0) {
      const clients = await client.query('SELECT id, name FROM clients LIMIT 3');
      
      for (const client_record of clients.rows) {
        const subAccountCode = `1120-${client_record.id.toString().padStart(3, '0')}`;
        
        await client.query(`
          INSERT INTO account_links 
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES ($1, $2, $3, $4, $5, $6)
          ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING
        `, [
          clientsAccount.rows[0].id,
          'clients',
          client_record.id,
          subAccountCode,
          `حساب الموكل: ${client_record.name}`,
          'النظام'
        ]);
      }
      console.log(`   ✅ تم ربط ${clients.rows.length} موكل بحساب الموكلين`);
    }

    // ربط الموظفين بحساب الموظفين
    const employeesAccount = await client.query(
      'SELECT id FROM chart_of_accounts WHERE account_code = $1',
      ['1130']
    );

    if (employeesAccount.rows.length > 0) {
      const employees = await client.query('SELECT id, name FROM employees LIMIT 3');
      
      for (const employee of employees.rows) {
        const subAccountCode = `1130-${employee.id.toString().padStart(3, '0')}`;
        
        await client.query(`
          INSERT INTO account_links 
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES ($1, $2, $3, $4, $5, $6)
          ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING
        `, [
          employeesAccount.rows[0].id,
          'employees',
          employee.id,
          subAccountCode,
          `حساب الموظف: ${employee.name}`,
          'النظام'
        ]);
      }
      console.log(`   ✅ تم ربط ${employees.rows.length} موظف بحساب الموظفين`);
    }

    // عرض ملخص النتائج
    console.log('📋 ملخص دليل الحسابات المحدث:');
    
    const summary = await client.query(`
      SELECT 
        COUNT(*) as total_accounts,
        COUNT(CASE WHEN is_main_account = true THEN 1 END) as main_accounts,
        COUNT(CASE WHEN linked_table IS NOT NULL THEN 1 END) as linked_accounts
      FROM chart_of_accounts
    `);

    const linksSummary = await client.query(`
      SELECT 
        linked_table,
        COUNT(*) as links_count
      FROM account_links 
      GROUP BY linked_table
    `);

    console.log(`   📊 إجمالي الحسابات: ${summary.rows[0].total_accounts}`);
    console.log(`   📊 الحسابات الرئيسية: ${summary.rows[0].main_accounts}`);
    console.log(`   📊 الحسابات المربوطة: ${summary.rows[0].linked_accounts}`);
    
    console.log('📋 الروابط المنشأة:');
    linksSummary.rows.forEach(row => {
      console.log(`   - ${row.linked_table}: ${row.links_count} رابط`);
    });

    console.log('🎉 تم إصلاح وتحديث نظام ربط الحسابات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح القيود:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإصلاح
fixChartConstraints();
