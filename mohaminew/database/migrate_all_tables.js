// نسخ جميع البيانات التجريبية إلى قاعدة البيانات الحقيقية
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: 'yemen123'
};

// البيانات التجريبية لجميع الجداول
const allData = {
  // 1. الفروع
  branches: [
    { name: 'فرع صنعاء الرئيسي', governorate_id: 1, address: 'شارع الزبيري - صنعاء', phone: '01-123456', manager_name: 'أحم<PERSON> محمد' },
    { name: 'فرع عدن', governorate_id: 2, address: 'كريتر - عدن', phone: '02-234567', manager_name: 'محمد علي' },
    { name: 'فرع تعز', governorate_id: 3, address: 'شارع جمال - تعز', phone: '04-345678', manager_name: 'عل<PERSON> أحمد' },
    { name: 'فرع الحديدة', governorate_id: 4, address: 'شارع الكورنيش - الحديدة', phone: '03-456789', manager_name: 'سالم محمد' }
  ],

  // 2. أنواع القضايا
  issue_types: [
    { name: 'قضايا مدنية', description: 'القضايا المدنية والتجارية', is_active: true },
    { name: 'قضايا جنائية', description: 'القضايا الجنائية والجزائية', is_active: true },
    { name: 'قضايا أحوال شخصية', description: 'قضايا الزواج والطلاق والميراث', is_active: true },
    { name: 'قضايا عمالية', description: 'قضايا العمل والعمال', is_active: true },
    { name: 'قضايا إدارية', description: 'القضايا الإدارية والحكومية', is_active: true }
  ],

  // 3. الموظفين
  employees: [
    { name: 'ماجد أحمد علي', position: 'محامي أول', phone: '777123456', email: '<EMAIL>', salary: 150000, hire_date: '2020-01-15' },
    { name: 'يحيى علي محمد', position: 'محامي', phone: '777234567', email: '<EMAIL>', salary: 120000, hire_date: '2021-03-10' },
    { name: 'أحمد صالح حسن', position: 'مساعد قانوني', phone: '777345678', email: '<EMAIL>', salary: 80000, hire_date: '2022-06-20' },
    { name: 'محمد صالح عبدالله', position: 'سكرتير قانوني', phone: '777456789', email: '<EMAIL>', salary: 60000, hire_date: '2023-01-05' }
  ],

  // 4. الموكلين
  clients: [
    { name: 'أحمد محمد سالم', phone: '777111222', email: '<EMAIL>', address: 'صنعاء - شارع الستين', id_number: '12345678901', client_type: 'فرد' },
    { name: 'شركة النور للتجارة', phone: '777222333', email: '<EMAIL>', address: 'عدن - كريتر', id_number: '98765432109', client_type: 'شركة' },
    { name: 'فاطمة علي أحمد', phone: '777333444', email: '<EMAIL>', address: 'تعز - شارع جمال', id_number: '11122233344', client_type: 'فرد' },
    { name: 'مؤسسة الأمل', phone: '777444555', email: '<EMAIL>', address: 'الحديدة - الكورنيش', id_number: '55566677788', client_type: 'مؤسسة' }
  ],

  // 5. القضايا
  issues: [
    { title: 'قضية تجارية - شركة الأمل', case_number: 'C2024001', client_id: 2, issue_type_id: 1, court_id: 1, case_amount: 500000, status: 'جارية', start_date: '2024-01-15' },
    { title: 'قضية عقارية - النزاع العقاري', case_number: 'C2024002', client_id: 1, issue_type_id: 1, court_id: 2, case_amount: 750000, status: 'جارية', start_date: '2024-02-10' },
    { title: 'قضية أحوال شخصية - طلاق', case_number: 'C2024003', client_id: 3, issue_type_id: 3, court_id: 4, case_amount: 50000, status: 'مكتملة', start_date: '2024-01-20' },
    { title: 'قضية عمالية - حقوق عامل', case_number: 'C2024004', client_id: 4, issue_type_id: 4, court_id: 3, case_amount: 100000, status: 'جارية', start_date: '2024-03-05' }
  ],

  // 6. المستخدمين
  users: [
    { username: 'admin', email: '<EMAIL>', full_name: 'مدير النظام', role: 'admin', employee_id: 1, is_active: true },
    { username: 'lawyer1', email: '<EMAIL>', full_name: 'ماجد أحمد علي', role: 'lawyer', employee_id: 1, is_active: true },
    { username: 'lawyer2', email: '<EMAIL>', full_name: 'يحيى علي محمد', role: 'lawyer', employee_id: 2, is_active: true },
    { username: 'assistant', email: '<EMAIL>', full_name: 'أحمد صالح حسن', role: 'assistant', employee_id: 3, is_active: true }
  ],

  // 7. بيانات الشركة
  companies: [
    { name: 'مكتب المحاماة والاستشارات القانونية', address: 'صنعاء - شارع الزبيري', phone: '01-123456', email: '<EMAIL>', website: 'www.legal.com', license_number: 'LAW2020001', established_date: '2020-01-01' }
  ]
};

// استعلامات إنشاء الجداول
const createTableQueries = [
  // جدول الفروع
  `CREATE TABLE IF NOT EXISTS branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    governorate_id INTEGER REFERENCES governorates(id),
    address TEXT,
    phone VARCHAR(50),
    manager_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
  )`,

  // جدول أنواع القضايا
  `CREATE TABLE IF NOT EXISTS issue_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
  )`,

  // جدول الموظفين
  `CREATE TABLE IF NOT EXISTS employees (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    position VARCHAR(255),
    phone VARCHAR(50),
    email VARCHAR(255),
    salary DECIMAL(15,2),
    hire_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
  )`,

  // جدول الموكلين
  `CREATE TABLE IF NOT EXISTS clients (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    address TEXT,
    id_number VARCHAR(50),
    client_type VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
  )`,

  // جدول القضايا
  `CREATE TABLE IF NOT EXISTS issues (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    case_number VARCHAR(100) UNIQUE,
    client_id INTEGER REFERENCES clients(id),
    issue_type_id INTEGER REFERENCES issue_types(id),
    court_id INTEGER REFERENCES courts(id),
    case_amount DECIMAL(15,2),
    status VARCHAR(100),
    start_date DATE,
    end_date DATE,
    created_date DATE DEFAULT CURRENT_DATE
  )`,

  // جدول المستخدمين
  `CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    full_name VARCHAR(255),
    role VARCHAR(100),
    employee_id INTEGER REFERENCES employees(id),
    is_active BOOLEAN DEFAULT true,
    created_date DATE DEFAULT CURRENT_DATE
  )`,

  // جدول بيانات الشركة
  `CREATE TABLE IF NOT EXISTS companies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(255),
    license_number VARCHAR(100),
    established_date DATE,
    created_date DATE DEFAULT CURRENT_DATE
  )`
];

async function migrateAllTables() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إنشاء الجداول
    console.log('🔄 جاري إنشاء الجداول...');
    for (let i = 0; i < createTableQueries.length; i++) {
      try {
        await client.query(createTableQueries[i]);
        console.log(`✅ تم إنشاء الجدول ${i + 1}/${createTableQueries.length}`);
      } catch (error) {
        console.error(`❌ خطأ في إنشاء الجدول ${i + 1}:`, error.message);
      }
    }

    // نسخ البيانات
    console.log('🔄 جاري نسخ البيانات...');

    // 1. الفروع
    await client.query('TRUNCATE TABLE branches RESTART IDENTITY CASCADE');
    for (const branch of allData.branches) {
      await client.query(`
        INSERT INTO branches (name, governorate_id, address, phone, manager_name)
        VALUES ($1, $2, $3, $4, $5)
      `, [branch.name, branch.governorate_id, branch.address, branch.phone, branch.manager_name]);
    }
    console.log(`✅ تم نسخ ${allData.branches.length} فرع`);

    // 2. أنواع القضايا
    await client.query('TRUNCATE TABLE issue_types RESTART IDENTITY CASCADE');
    for (const type of allData.issue_types) {
      await client.query(`
        INSERT INTO issue_types (name, description, is_active)
        VALUES ($1, $2, $3)
      `, [type.name, type.description, type.is_active]);
    }
    console.log(`✅ تم نسخ ${allData.issue_types.length} نوع قضية`);

    // 3. الموظفين
    await client.query('TRUNCATE TABLE employees RESTART IDENTITY CASCADE');
    for (const emp of allData.employees) {
      await client.query(`
        INSERT INTO employees (name, position, phone, email, salary, hire_date)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [emp.name, emp.position, emp.phone, emp.email, emp.salary, emp.hire_date]);
    }
    console.log(`✅ تم نسخ ${allData.employees.length} موظف`);

    // 4. الموكلين
    await client.query('TRUNCATE TABLE clients RESTART IDENTITY CASCADE');
    for (const client_data of allData.clients) {
      await client.query(`
        INSERT INTO clients (name, phone, email, address, id_number, client_type)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [client_data.name, client_data.phone, client_data.email, client_data.address, client_data.id_number, client_data.client_type]);
    }
    console.log(`✅ تم نسخ ${allData.clients.length} موكل`);

    // 5. القضايا
    await client.query('TRUNCATE TABLE issues RESTART IDENTITY CASCADE');
    for (const issue of allData.issues) {
      await client.query(`
        INSERT INTO issues (title, case_number, client_id, issue_type_id, court_id, case_amount, status, start_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [issue.title, issue.case_number, issue.client_id, issue.issue_type_id, issue.court_id, issue.case_amount, issue.status, issue.start_date]);
    }
    console.log(`✅ تم نسخ ${allData.issues.length} قضية`);

    // 6. المستخدمين
    await client.query('TRUNCATE TABLE users RESTART IDENTITY CASCADE');
    for (const user of allData.users) {
      await client.query(`
        INSERT INTO users (username, email, full_name, role, employee_id, is_active)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [user.username, user.email, user.full_name, user.role, user.employee_id, user.is_active]);
    }
    console.log(`✅ تم نسخ ${allData.users.length} مستخدم`);

    // 7. بيانات الشركة
    await client.query('TRUNCATE TABLE companies RESTART IDENTITY CASCADE');
    for (const company of allData.companies) {
      await client.query(`
        INSERT INTO companies (name, address, phone, email, website, license_number, established_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [company.name, company.address, company.phone, company.email, company.website, company.license_number, company.established_date]);
    }
    console.log(`✅ تم نسخ ${allData.companies.length} شركة`);

    // التحقق من النتائج
    console.log('🔄 جاري التحقق من النتائج...');
    
    const results = await Promise.all([
      client.query('SELECT COUNT(*) FROM branches'),
      client.query('SELECT COUNT(*) FROM issue_types'),
      client.query('SELECT COUNT(*) FROM employees'),
      client.query('SELECT COUNT(*) FROM clients'),
      client.query('SELECT COUNT(*) FROM issues'),
      client.query('SELECT COUNT(*) FROM users'),
      client.query('SELECT COUNT(*) FROM companies')
    ]);

    console.log('📋 ملخص البيانات المنسوخة:');
    console.log(`   ✅ الفروع: ${results[0].rows[0].count} سجل`);
    console.log(`   ✅ أنواع القضايا: ${results[1].rows[0].count} سجل`);
    console.log(`   ✅ الموظفين: ${results[2].rows[0].count} سجل`);
    console.log(`   ✅ الموكلين: ${results[3].rows[0].count} سجل`);
    console.log(`   ✅ القضايا: ${results[4].rows[0].count} سجل`);
    console.log(`   ✅ المستخدمين: ${results[5].rows[0].count} سجل`);
    console.log(`   ✅ بيانات الشركة: ${results[6].rows[0].count} سجل`);

    console.log('🎉 تم نسخ جميع البيانات إلى قاعدة البيانات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في نسخ البيانات:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل النسخ
migrateAllTables();
