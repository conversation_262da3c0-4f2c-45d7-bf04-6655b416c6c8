// التحقق من هيكل جدول الموكلين
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkClientsTable() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔍 التحقق من هيكل جدول الموكلين...');
    await client.connect();

    // عرض أعمدة الجدول
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'clients' 
      ORDER BY ordinal_position
    `);

    console.log('📋 أعمدة جدول الموكلين:');
    columns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });

    // عرض عينة من البيانات
    const sampleData = await client.query('SELECT * FROM clients LIMIT 3');
    console.log('\n📊 عينة من البيانات:');
    if (sampleData.rows.length > 0) {
      console.log('الأعمدة المتاحة:', Object.keys(sampleData.rows[0]));
      sampleData.rows.forEach((row, index) => {
        console.log(`   ${index + 1}. ${row.name || row.client_name || 'غير محدد'}`);
      });
    } else {
      console.log('   لا توجد بيانات في الجدول');
    }

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkClientsTable();
