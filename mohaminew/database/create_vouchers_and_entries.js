const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function createVouchersAndEntries() {
  try {
    console.log('🔄 إنشاء جداول السندات والقيود...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // 1. إنشاء جدول سندات الصرف
    console.log('\n💸 إنشاء جدول سندات الصرف...');
    
    await client.query(`
      CREATE TABLE payment_vouchers (
        id SERIAL PRIMARY KEY,
        voucher_number VARCHAR(50) UNIQUE NOT NULL,
        voucher_date DATE NOT NULL,
        
        -- بيانات المستفيد
        beneficiary_name VARCHAR(255) NOT NULL,
        beneficiary_type VARCHAR(50) DEFAULT 'external', -- external, client, employee
        beneficiary_id INTEGER, -- ID من جدول العملاء أو الموظفين
        
        -- بيانات المبلغ والحساب
        account_id INTEGER NOT NULL, -- حساب فرعي من المستوى 4 فقط
        amount DECIMAL(15,2) NOT NULL,
        currency_id INTEGER REFERENCES currencies(id) DEFAULT 1,
        
        -- طريقة الدفع
        payment_method_id INTEGER REFERENCES payment_methods(id),
        
        -- مركز التكلفة
        cost_center_id INTEGER REFERENCES cost_centers(id),
        
        -- الوصف والمراجع
        description TEXT NOT NULL,
        reference_number VARCHAR(100),
        
        -- ربط بالقضية (اختياري)
        case_id INTEGER REFERENCES issues(id),
        case_number VARCHAR(50),
        
        -- بيانات المستخدم
        created_by_user_id INTEGER REFERENCES users(id),
        created_by_name VARCHAR(255),
        
        -- حالة السند
        status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'approved', 'cancelled')),
        
        -- تواريخ
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        approved_date TIMESTAMP,
        approved_by INTEGER REFERENCES users(id),
        
        -- ملاحظات
        notes TEXT,
        
        -- فهارس
        CONSTRAINT fk_payment_account CHECK (account_id IS NOT NULL),
        CONSTRAINT check_amount_positive CHECK (amount > 0)
      );
      
      CREATE INDEX idx_payment_voucher_number ON payment_vouchers(voucher_number);
      CREATE INDEX idx_payment_voucher_date ON payment_vouchers(voucher_date);
      CREATE INDEX idx_payment_beneficiary ON payment_vouchers(beneficiary_type, beneficiary_id);
      CREATE INDEX idx_payment_case ON payment_vouchers(case_id);
      CREATE INDEX idx_payment_account ON payment_vouchers(account_id);
    `);
    console.log('✅ تم إنشاء جدول سندات الصرف');

    // 2. إنشاء جدول سندات القبض
    console.log('\n💰 إنشاء جدول سندات القبض...');
    
    await client.query(`
      CREATE TABLE receipt_vouchers (
        id SERIAL PRIMARY KEY,
        voucher_number VARCHAR(50) UNIQUE NOT NULL,
        voucher_date DATE NOT NULL,
        
        -- بيانات المصدر (الدافع)
        payer_name VARCHAR(255) NOT NULL,
        payer_type VARCHAR(50) DEFAULT 'external', -- external, client, employee
        payer_id INTEGER, -- ID من جدول العملاء أو الموظفين
        
        -- بيانات المبلغ والحساب
        account_id INTEGER NOT NULL, -- حساب فرعي من المستوى 4 فقط
        amount DECIMAL(15,2) NOT NULL,
        currency_id INTEGER REFERENCES currencies(id) DEFAULT 1,
        
        -- طريقة الدفع
        payment_method_id INTEGER REFERENCES payment_methods(id),
        
        -- مركز التكلفة
        cost_center_id INTEGER REFERENCES cost_centers(id),
        
        -- الوصف والمراجع
        description TEXT NOT NULL,
        reference_number VARCHAR(100),
        
        -- ربط بالقضية (اختياري)
        case_id INTEGER REFERENCES issues(id),
        case_number VARCHAR(50),
        
        -- بيانات المستخدم
        created_by_user_id INTEGER REFERENCES users(id),
        created_by_name VARCHAR(255),
        
        -- حالة السند
        status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'approved', 'cancelled')),
        
        -- تواريخ
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        approved_date TIMESTAMP,
        approved_by INTEGER REFERENCES users(id),
        
        -- ملاحظات
        notes TEXT,
        
        -- فهارس
        CONSTRAINT fk_receipt_account CHECK (account_id IS NOT NULL),
        CONSTRAINT check_receipt_amount_positive CHECK (amount > 0)
      );
      
      CREATE INDEX idx_receipt_voucher_number ON receipt_vouchers(voucher_number);
      CREATE INDEX idx_receipt_voucher_date ON receipt_vouchers(voucher_date);
      CREATE INDEX idx_receipt_payer ON receipt_vouchers(payer_type, payer_id);
      CREATE INDEX idx_receipt_case ON receipt_vouchers(case_id);
      CREATE INDEX idx_receipt_account ON receipt_vouchers(account_id);
    `);
    console.log('✅ تم إنشاء جدول سندات القبض');

    // 3. إنشاء جدول القيود اليومية (الرأس)
    console.log('\n📋 إنشاء جدول القيود اليومية...');
    
    await client.query(`
      CREATE TABLE journal_entries (
        id SERIAL PRIMARY KEY,
        entry_number VARCHAR(50) UNIQUE NOT NULL,
        entry_date DATE NOT NULL,
        
        -- وصف عام للقيد
        description TEXT NOT NULL,
        
        -- إجماليات القيد
        total_debit DECIMAL(15,2) DEFAULT 0,
        total_credit DECIMAL(15,2) DEFAULT 0,
        
        -- العملة الأساسية
        currency_id INTEGER REFERENCES currencies(id) DEFAULT 1,
        
        -- مركز التكلفة العام (اختياري)
        cost_center_id INTEGER REFERENCES cost_centers(id),
        
        -- ربط بالقضية (اختياري)
        case_id INTEGER REFERENCES issues(id),
        case_number VARCHAR(50),
        
        -- بيانات المستخدم
        created_by_user_id INTEGER REFERENCES users(id),
        created_by_name VARCHAR(255),
        
        -- حالة القيد
        status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'approved', 'cancelled')),
        
        -- تواريخ
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        approved_date TIMESTAMP,
        approved_by INTEGER REFERENCES users(id),
        
        -- مراجع
        reference_number VARCHAR(100),
        notes TEXT,
        
        -- قيد التوازن
        CONSTRAINT check_balanced_entry CHECK (total_debit = total_credit)
      );
      
      CREATE INDEX idx_journal_entry_number ON journal_entries(entry_number);
      CREATE INDEX idx_journal_entry_date ON journal_entries(entry_date);
      CREATE INDEX idx_journal_case ON journal_entries(case_id);
    `);
    console.log('✅ تم إنشاء جدول القيود اليومية');

    // 4. إنشاء جدول تفاصيل القيود اليومية
    console.log('\n📝 إنشاء جدول تفاصيل القيود...');
    
    await client.query(`
      CREATE TABLE journal_entry_details (
        id SERIAL PRIMARY KEY,
        journal_entry_id INTEGER REFERENCES journal_entries(id) ON DELETE CASCADE,
        line_number INTEGER NOT NULL,
        
        -- الحساب (فقط المستوى 4)
        account_id INTEGER NOT NULL,
        account_type VARCHAR(50), -- client, employee, sub_account
        account_name VARCHAR(255), -- اسم الحساب للعرض
        
        -- المبالغ
        debit_amount DECIMAL(15,2) DEFAULT 0,
        credit_amount DECIMAL(15,2) DEFAULT 0,
        
        -- العملة (يمكن أن تختلف عن عملة القيد الرئيسي)
        currency_id INTEGER REFERENCES currencies(id) DEFAULT 1,
        exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
        
        -- مركز التكلفة لهذا السطر
        cost_center_id INTEGER REFERENCES cost_centers(id),
        
        -- طريقة الدفع (اختياري)
        payment_method_id INTEGER REFERENCES payment_methods(id),
        
        -- وصف خاص بالسطر
        description TEXT,
        
        -- مراجع
        reference_number VARCHAR(100),
        
        -- تواريخ
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        -- قيود
        CONSTRAINT check_debit_or_credit CHECK (
          (debit_amount > 0 AND credit_amount = 0) OR 
          (credit_amount > 0 AND debit_amount = 0)
        ),
        CONSTRAINT unique_journal_line UNIQUE (journal_entry_id, line_number)
      );
      
      CREATE INDEX idx_journal_details_entry ON journal_entry_details(journal_entry_id);
      CREATE INDEX idx_journal_details_account ON journal_entry_details(account_id);
      CREATE INDEX idx_journal_details_line ON journal_entry_details(journal_entry_id, line_number);
    `);
    console.log('✅ تم إنشاء جدول تفاصيل القيود');

    console.log('\n✅ تم إنشاء جميع جداول السندات والقيود بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء جداول السندات والقيود:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  createVouchersAndEntries()
    .then(() => {
      console.log('\n🎉 تم إنشاء جداول السندات والقيود بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل في إنشاء الجداول:', error);
      process.exit(1);
    });
}

module.exports = { createVouchersAndEntries };
