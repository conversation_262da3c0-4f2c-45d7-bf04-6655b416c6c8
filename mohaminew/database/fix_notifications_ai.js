const { Client } = require('pg');

async function fixNotificationsAI() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'moham<PERSON>',
    user: 'postgres',
    password: 'yemen123',
  });

  try {
    await client.connect();
    console.log('🔗 متصل بقاعدة البيانات');

    // تعديل جدول الإشعارات لدعم الذكاء الاصطناعي
    console.log('🤖 إصلاح جدول الإشعارات...');
    
    await client.query(`
      ALTER TABLE notifications 
      DROP CONSTRAINT IF EXISTS notifications_sender_type_check
    `);
    
    await client.query(`
      ALTER TABLE notifications 
      ADD CONSTRAINT notifications_sender_type_check 
      CHECK (sender_type IN ('user', 'client', 'ai'))
    `);
    
    console.log('✅ تم تعديل جدول الإشعارات لدعم sender_type = "ai"');

    // تعديل recipient_type أيضاً
    await client.query(`
      ALTER TABLE notifications 
      DROP CONSTRAINT IF EXISTS notifications_recipient_type_check
    `);
    
    await client.query(`
      ALTER TABLE notifications 
      ADD CONSTRAINT notifications_recipient_type_check 
      CHECK (recipient_type IN ('user', 'client', 'ai'))
    `);
    
    console.log('✅ تم تعديل جدول الإشعارات لدعم recipient_type = "ai"');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

fixNotificationsAI();