const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function testBalanceCalculation() {
  try {
    console.log('🧪 اختبار حساب الأرصدة التلقائي...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إضافة أرصدة تجريبية للحسابات الفرعية
    console.log('\n💰 إضافة أرصدة تجريبية للحسابات الفرعية...');
    
    const testBalances = [
      { account_code: '1111', opening_balance: 50000, current_balance: 75000 }, // الصندوق
      { account_code: '1112', opening_balance: 200000, current_balance: 180000 }, // البنك
      { account_code: '1121', opening_balance: 30000, current_balance: 45000 }, // حسابات الموكلين
      { account_code: '1122', opening_balance: 15000, current_balance: 12000 }, // حسابات الموظفين
      { account_code: '211', opening_balance: 25000, current_balance: 30000 }, // الدائنون
      { account_code: '31', opening_balance: 100000, current_balance: 100000 }, // رأس المال
      { account_code: '411', opening_balance: 0, current_balance: 150000 }, // إيرادات القضايا
      { account_code: '511', opening_balance: 0, current_balance: 20000 }, // مصروفات المحاكم
      { account_code: '512', opening_balance: 0, current_balance: 15000 }, // مصروفات الفروع
      { account_code: '513', opening_balance: 0, current_balance: 10000 } // مراكز التكلفة
    ];

    for (const balance of testBalances) {
      await client.query(`
        UPDATE chart_of_accounts 
        SET 
          opening_balance = $1,
          current_balance = $2,
          updated_date = CURRENT_TIMESTAMP
        WHERE account_code = $3
      `, [balance.opening_balance, balance.current_balance, balance.account_code]);
      
      console.log(`   ✅ ${balance.account_code}: رصيد افتتاحي ${balance.opening_balance.toLocaleString()}, رصيد حالي ${balance.current_balance.toLocaleString()}`);
    }

    // 2. انتظار قليل للمحفزات
    console.log('\n⏳ انتظار تنفيذ المحفزات التلقائية...');
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 3. عرض الأرصدة المحسوبة للحسابات الرئيسية
    console.log('\n📊 الأرصدة المحسوبة للحسابات الرئيسية:');
    const mainAccountBalances = await client.query(`
      SELECT 
        account_code,
        account_name,
        opening_balance,
        current_balance,
        (
          SELECT COUNT(*) 
          FROM chart_of_accounts child 
          WHERE child.parent_id = c.id
        ) as direct_children
      FROM chart_of_accounts c
      WHERE is_main_account = TRUE
      ORDER BY account_code
    `);
    
    let totalOpeningBalance = 0;
    let totalCurrentBalance = 0;
    
    mainAccountBalances.rows.forEach(account => {
      totalOpeningBalance += parseFloat(account.opening_balance);
      totalCurrentBalance += parseFloat(account.current_balance);
      
      console.log(`   ${account.account_code}: ${account.account_name}`);
      console.log(`      └── ${account.direct_children} حساب فرعي مباشر`);
      console.log(`      └── رصيد افتتاحي: ${parseFloat(account.opening_balance).toLocaleString()}`);
      console.log(`      └── رصيد حالي: ${parseFloat(account.current_balance).toLocaleString()}`);
    });

    // 4. عرض تفصيل الحسابات الفرعية لكل حساب رئيسي
    console.log('\n🔍 تفصيل الحسابات الفرعية:');
    
    for (const mainAccount of mainAccountBalances.rows) {
      console.log(`\n   📁 ${mainAccount.account_code}: ${mainAccount.account_name}`);
      
      const subAccounts = await client.query(`
        WITH RECURSIVE sub_accounts AS (
          -- الحسابات الفرعية المباشرة
          SELECT id, account_code, account_name, opening_balance, current_balance, account_level, 1 as depth
          FROM chart_of_accounts 
          WHERE parent_id = $1
          
          UNION ALL
          
          -- الحسابات الفرعية للحسابات الفرعية
          SELECT c.id, c.account_code, c.account_name, c.opening_balance, c.current_balance, c.account_level, sa.depth + 1
          FROM chart_of_accounts c
          INNER JOIN sub_accounts sa ON c.parent_id = sa.id
        )
        SELECT * FROM sub_accounts
        ORDER BY account_code
      `, [mainAccount.id]);
      
      if (subAccounts.rows.length > 0) {
        subAccounts.rows.forEach(subAccount => {
          const indent = '      ' + '  '.repeat(subAccount.depth - 1);
          console.log(`${indent}├── ${subAccount.account_code}: ${subAccount.account_name}`);
          console.log(`${indent}    └── افتتاحي: ${parseFloat(subAccount.opening_balance).toLocaleString()}, حالي: ${parseFloat(subAccount.current_balance).toLocaleString()}`);
        });
      } else {
        console.log('      └── لا توجد حسابات فرعية');
      }
    }

    // 5. التحقق من صحة الحسابات (الميزانية)
    console.log('\n⚖️ التحقق من صحة الميزانية:');
    
    const balanceCheck = await client.query(`
      SELECT 
        SUM(CASE WHEN account_type IN ('أصول') THEN current_balance ELSE 0 END) as total_assets,
        SUM(CASE WHEN account_type IN ('خصوم') THEN current_balance ELSE 0 END) as total_liabilities,
        SUM(CASE WHEN account_type IN ('حقوق ملكية') THEN current_balance ELSE 0 END) as total_equity,
        SUM(CASE WHEN account_type IN ('إيرادات') THEN current_balance ELSE 0 END) as total_revenue,
        SUM(CASE WHEN account_type IN ('مصروفات') THEN current_balance ELSE 0 END) as total_expenses
      FROM chart_of_accounts
      WHERE is_main_account = TRUE
    `);
    
    const check = balanceCheck.rows[0];
    const totalAssets = parseFloat(check.total_assets) || 0;
    const totalLiabilities = parseFloat(check.total_liabilities) || 0;
    const totalEquity = parseFloat(check.total_equity) || 0;
    const totalRevenue = parseFloat(check.total_revenue) || 0;
    const totalExpenses = parseFloat(check.total_expenses) || 0;
    
    console.log(`   📈 إجمالي الأصول: ${totalAssets.toLocaleString()}`);
    console.log(`   📉 إجمالي الخصوم: ${totalLiabilities.toLocaleString()}`);
    console.log(`   💰 إجمالي حقوق الملكية: ${totalEquity.toLocaleString()}`);
    console.log(`   💵 إجمالي الإيرادات: ${totalRevenue.toLocaleString()}`);
    console.log(`   💸 إجمالي المصروفات: ${totalExpenses.toLocaleString()}`);
    
    const balanceEquation = totalAssets - (totalLiabilities + totalEquity);
    console.log(`\n   ⚖️ معادلة الميزانية: ${totalAssets.toLocaleString()} = ${(totalLiabilities + totalEquity).toLocaleString()}`);
    console.log(`   ${Math.abs(balanceEquation) < 0.01 ? '✅' : '❌'} الفرق: ${balanceEquation.toLocaleString()}`);
    
    const netIncome = totalRevenue - totalExpenses;
    console.log(`   💡 صافي الدخل: ${netIncome.toLocaleString()}`);

    // 6. اختبار تحديث رصيد حساب فرعي ومراقبة التأثير على الحساب الرئيسي
    console.log('\n🧪 اختبار التحديث التلقائي...');
    
    // الحصول على الرصيد الحالي للأصول قبل التحديث
    const assetsBefore = await client.query(`
      SELECT current_balance FROM chart_of_accounts WHERE account_code = '1'
    `);
    
    console.log(`   📊 رصيد الأصول قبل التحديث: ${parseFloat(assetsBefore.rows[0].current_balance).toLocaleString()}`);
    
    // تحديث رصيد الصندوق
    await client.query(`
      UPDATE chart_of_accounts 
      SET current_balance = current_balance + 10000
      WHERE account_code = '1111'
    `);
    
    console.log(`   💰 تم إضافة 10,000 لرصيد الصندوق`);
    
    // انتظار قليل للمحفز
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // الحصول على الرصيد الجديد للأصول
    const assetsAfter = await client.query(`
      SELECT current_balance FROM chart_of_accounts WHERE account_code = '1'
    `);
    
    console.log(`   📊 رصيد الأصول بعد التحديث: ${parseFloat(assetsAfter.rows[0].current_balance).toLocaleString()}`);
    
    const difference = parseFloat(assetsAfter.rows[0].current_balance) - parseFloat(assetsBefore.rows[0].current_balance);
    console.log(`   ${difference === 10000 ? '✅' : '❌'} الفرق: ${difference.toLocaleString()} (متوقع: 10,000)`);

    console.log('\n✅ تم اختبار النظام بنجاح!');
    console.log('🎯 النظام يحسب أرصدة الحسابات الرئيسية تلقائياً من الحسابات الفرعية');

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  testBalanceCalculation()
    .then(() => {
      console.log('🎉 تم إنجاز الاختبار بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في الاختبار:', error);
      process.exit(1);
    });
}

module.exports = { testBalanceCalculation };
