const { redesignAccountingSystem } = require('./redesign_accounting_system');
const { createVouchersAndEntries } = require('./create_vouchers_and_entries');
const { insertChartOfAccounts } = require('./insert_chart_of_accounts');

async function setupNewAccountingSystem() {
  try {
    console.log('🚀 بدء إعداد النظام المحاسبي الجديد...');
    console.log('=' * 60);
    
    // 1. إعادة تصميم النظام المحاسبي (حذف القديم وإنشاء الجديد)
    console.log('\n📋 المرحلة 1: إعادة تصميم النظام المحاسبي...');
    await redesignAccountingSystem();
    console.log('✅ تم إكمال المرحلة 1');
    
    // 2. إنشاء جداول السندات والقيود
    console.log('\n📋 المرحلة 2: إنشاء جداول السندات والقيود...');
    await createVouchersAndEntries();
    console.log('✅ تم إكمال المرحلة 2');
    
    // 3. إدراج دليل الحسابات الأساسي
    console.log('\n📋 المرحلة 3: إدراج دليل الحسابات الأساسي...');
    await insertChartOfAccounts();
    console.log('✅ تم إكمال المرحلة 3');
    
    console.log('\n' + '=' * 60);
    console.log('🎉 تم إعداد النظام المحاسبي الجديد بنجاح!');
    console.log('=' * 60);
    
    // عرض ملخص النظام الجديد
    console.log('\n📊 ملخص النظام المحاسبي الجديد:');
    console.log('');
    console.log('🏗️ الهيكل الأساسي:');
    console.log('   ✅ دليل الحسابات (4 مستويات)');
    console.log('   ✅ جدول العملات');
    console.log('   ✅ جدول طرق الدفع');
    console.log('   ✅ جدول مراكز التكلفة');
    console.log('');
    console.log('📄 السندات والقيود:');
    console.log('   ✅ سندات الصرف');
    console.log('   ✅ سندات القبض');
    console.log('   ✅ القيود اليومية');
    console.log('   ✅ تفاصيل القيود');
    console.log('');
    console.log('🔗 الربط والتكامل:');
    console.log('   ✅ ربط مع جدول العملاء');
    console.log('   ✅ ربط مع جدول الموظفين');
    console.log('   ✅ ربط مع جدول القضايا');
    console.log('   ✅ ربط مع جدول المستخدمين');
    console.log('');
    console.log('📋 الميزات الجديدة:');
    console.log('   ✅ نظام 4 مستويات للحسابات');
    console.log('   ✅ عدم السماح بالمعاملات إلا للمستوى 4');
    console.log('   ✅ ربط تلقائي بالجداول الخارجية');
    console.log('   ✅ دعم متعدد العملات');
    console.log('   ✅ مراكز التكلفة');
    console.log('   ✅ طرق دفع متنوعة');
    console.log('   ✅ ربط بالقضايا القانونية');
    console.log('   ✅ تتبع المستخدمين');
    console.log('');
    console.log('🎯 الخطوات التالية:');
    console.log('   1. إنشاء واجهات المستخدم الجديدة');
    console.log('   2. إنشاء APIs للنظام الجديد');
    console.log('   3. إنشاء التقارير المحاسبية');
    console.log('   4. اختبار النظام');
    console.log('');
    
  } catch (error) {
    console.error('\n💥 فشل في إعداد النظام المحاسبي الجديد:', error);
    console.error('\nتفاصيل الخطأ:', error.message);
    throw error;
  }
}

// تشغيل السكريبت
if (require.main === module) {
  setupNewAccountingSystem()
    .then(() => {
      console.log('\n🎊 تم إعداد النظام المحاسبي الجديد بنجاح!');
      console.log('يمكنك الآن البدء في استخدام النظام الجديد.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل في إعداد النظام:', error);
      process.exit(1);
    });
}

module.exports = { setupNewAccountingSystem };
