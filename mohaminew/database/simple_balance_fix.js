const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function simpleBalanceFix() {
  try {
    console.log('🔧 إصلاح بسيط لحساب الأرصدة...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. حذف جميع المحفزات والدوال المعقدة
    console.log('\n🗑️ تنظيف المحفزات والدوال...');
    
    await client.query(`DROP TRIGGER IF EXISTS trigger_update_main_balance_on_insert ON chart_of_accounts;`);
    await client.query(`DROP TRIGGER IF EXISTS trigger_update_main_balance_on_update ON chart_of_accounts;`);
    await client.query(`DROP FUNCTION IF EXISTS trigger_update_main_account_balance();`);
    await client.query(`DROP FUNCTION IF EXISTS calculate_main_account_balance(integer);`);
    await client.query(`DROP FUNCTION IF EXISTS update_all_main_account_balances();`);
    
    console.log('   ✅ تم تنظيف جميع المحفزات والدوال');

    // 2. إضافة بيانات تجريبية للحسابات الفرعية
    console.log('\n💰 إضافة بيانات تجريبية للحسابات الفرعية...');
    
    const testData = [
      { code: '1111', name: 'الصندوق', opening: 50000, current: 75000 },
      { code: '1112', name: 'البنك', opening: 200000, current: 180000 },
      { code: '1121', name: 'حسابات الموكلين', opening: 30000, current: 45000 },
      { code: '1122', name: 'حسابات الموظفين', opening: 15000, current: 12000 },
      { code: '211', name: 'الدائنون', opening: 25000, current: 30000 },
      { code: '31', name: 'رأس المال', opening: 100000, current: 100000 },
      { code: '411', name: 'إيرادات القضايا', opening: 0, current: 150000 },
      { code: '511', name: 'مصروفات المحاكم', opening: 0, current: 20000 },
      { code: '512', name: 'مصروفات الفروع', opening: 0, current: 15000 },
      { code: '513', name: 'مراكز التكلفة', opening: 0, current: 10000 }
    ];

    for (const data of testData) {
      await client.query(`
        UPDATE chart_of_accounts 
        SET opening_balance = $1, current_balance = $2
        WHERE account_code = $3
      `, [data.opening, data.current, data.code]);
      
      console.log(`   ✅ ${data.code}: ${data.name} - افتتاحي: ${data.opening.toLocaleString()}, حالي: ${data.current.toLocaleString()}`);
    }

    // 3. حساب أرصدة الحسابات الرئيسية يدوياً
    console.log('\n🧮 حساب أرصدة الحسابات الرئيسية...');
    
    // الحصول على جميع الحسابات الرئيسية
    const mainAccounts = await client.query(`
      SELECT id, account_code, account_name
      FROM chart_of_accounts 
      WHERE is_main_account = TRUE
      ORDER BY account_code
    `);

    for (const mainAccount of mainAccounts.rows) {
      // حساب مجموع الأرصدة للحسابات الفرعية
      const subAccountsSum = await client.query(`
        WITH RECURSIVE sub_accounts AS (
          -- الحسابات الفرعية المباشرة
          SELECT id, opening_balance, current_balance
          FROM chart_of_accounts 
          WHERE parent_id = $1
          
          UNION ALL
          
          -- الحسابات الفرعية للحسابات الفرعية
          SELECT c.id, c.opening_balance, c.current_balance
          FROM chart_of_accounts c
          INNER JOIN sub_accounts sa ON c.parent_id = sa.id
        )
        SELECT 
          COALESCE(SUM(opening_balance), 0) as total_opening,
          COALESCE(SUM(current_balance), 0) as total_current,
          COUNT(*) as sub_count
        FROM sub_accounts
      `, [mainAccount.id]);

      const totalOpening = parseFloat(subAccountsSum.rows[0].total_opening) || 0;
      const totalCurrent = parseFloat(subAccountsSum.rows[0].total_current) || 0;
      const subCount = parseInt(subAccountsSum.rows[0].sub_count) || 0;

      // تحديث رصيد الحساب الرئيسي
      await client.query(`
        UPDATE chart_of_accounts 
        SET 
          opening_balance = $1,
          current_balance = $2,
          updated_date = CURRENT_TIMESTAMP
        WHERE id = $3
      `, [totalOpening, totalCurrent, mainAccount.id]);

      console.log(`   ✅ ${mainAccount.account_code}: ${mainAccount.account_name}`);
      console.log(`      └── ${subCount} حساب فرعي`);
      console.log(`      └── رصيد افتتاحي: ${totalOpening.toLocaleString()}`);
      console.log(`      └── رصيد حالي: ${totalCurrent.toLocaleString()}`);
    }

    // 4. عرض النتائج النهائية
    console.log('\n📊 النتائج النهائية:');
    
    const finalResults = await client.query(`
      SELECT 
        account_code,
        account_name,
        account_type,
        opening_balance,
        current_balance,
        is_main_account
      FROM chart_of_accounts
      ORDER BY account_code
    `);

    let currentType = '';
    finalResults.rows.forEach(account => {
      if (account.is_main_account && account.account_type !== currentType) {
        currentType = account.account_type;
        console.log(`\n   📁 ${currentType}:`);
      }
      
      const indent = account.is_main_account ? '   ' : '      ';
      const typeIcon = account.is_main_account ? '📊' : '├──';
      
      console.log(`${indent}${typeIcon} ${account.account_code}: ${account.account_name}`);
      console.log(`${indent}    └── افتتاحي: ${parseFloat(account.opening_balance).toLocaleString()}, حالي: ${parseFloat(account.current_balance).toLocaleString()}`);
    });

    // 5. ملخص الميزانية
    console.log('\n⚖️ ملخص الميزانية:');
    
    const balanceSummary = await client.query(`
      SELECT 
        account_type,
        SUM(opening_balance) as total_opening,
        SUM(current_balance) as total_current
      FROM chart_of_accounts
      WHERE is_main_account = TRUE
      GROUP BY account_type
      ORDER BY account_type
    `);

    let totalAssets = 0, totalLiabilities = 0, totalEquity = 0, totalRevenue = 0, totalExpenses = 0;
    
    balanceSummary.rows.forEach(row => {
      const opening = parseFloat(row.total_opening) || 0;
      const current = parseFloat(row.total_current) || 0;
      
      console.log(`   ${row.account_type}:`);
      console.log(`      └── رصيد افتتاحي: ${opening.toLocaleString()}`);
      console.log(`      └── رصيد حالي: ${current.toLocaleString()}`);
      
      switch(row.account_type) {
        case 'أصول': totalAssets = current; break;
        case 'خصوم': totalLiabilities = current; break;
        case 'حقوق ملكية': totalEquity = current; break;
        case 'إيرادات': totalRevenue = current; break;
        case 'مصروفات': totalExpenses = current; break;
      }
    });

    console.log(`\n   📈 معادلة الميزانية:`);
    console.log(`   الأصول: ${totalAssets.toLocaleString()}`);
    console.log(`   الخصوم + حقوق الملكية: ${(totalLiabilities + totalEquity).toLocaleString()}`);
    console.log(`   الفرق: ${(totalAssets - totalLiabilities - totalEquity).toLocaleString()}`);
    console.log(`   صافي الدخل: ${(totalRevenue - totalExpenses).toLocaleString()}`);

    // 6. إنشاء دالة بسيطة لإعادة الحساب عند الحاجة
    console.log('\n🔧 إنشاء دالة بسيطة لإعادة الحساب...');
    
    await client.query(`
      CREATE OR REPLACE FUNCTION recalculate_main_accounts()
      RETURNS TEXT AS $$
      DECLARE
        main_acc RECORD;
        sub_totals RECORD;
        updated_count INTEGER := 0;
      BEGIN
        FOR main_acc IN 
          SELECT id, account_code, account_name
          FROM chart_of_accounts 
          WHERE is_main_account = TRUE
        LOOP
          -- حساب مجموع الحسابات الفرعية
          WITH RECURSIVE sub_accounts AS (
            SELECT id, opening_balance, current_balance
            FROM chart_of_accounts 
            WHERE parent_id = main_acc.id
            
            UNION ALL
            
            SELECT c.id, c.opening_balance, c.current_balance
            FROM chart_of_accounts c
            INNER JOIN sub_accounts sa ON c.parent_id = sa.id
          )
          SELECT 
            COALESCE(SUM(opening_balance), 0) as total_opening,
            COALESCE(SUM(current_balance), 0) as total_current
          INTO sub_totals
          FROM sub_accounts;
          
          -- تحديث الحساب الرئيسي
          UPDATE chart_of_accounts 
          SET 
            opening_balance = sub_totals.total_opening,
            current_balance = sub_totals.total_current,
            updated_date = CURRENT_TIMESTAMP
          WHERE id = main_acc.id;
          
          updated_count := updated_count + 1;
        END LOOP;
        
        RETURN 'تم تحديث ' || updated_count || ' حساب رئيسي';
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    console.log('   ✅ تم إنشاء دالة recalculate_main_accounts()');
    console.log('   📝 لإعادة حساب الأرصدة: SELECT recalculate_main_accounts();');

    console.log('\n✅ تم إصلاح النظام بنجاح!');
    console.log('🎯 الآن الحسابات الرئيسية تعكس مجموع الحسابات الفرعية');
    console.log('📋 يمكن إعادة حساب الأرصدة يدوياً عند الحاجة');

  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  simpleBalanceFix()
    .then(() => {
      console.log('🎉 تم إنجاز الإصلاح بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في الإصلاح:', error);
      process.exit(1);
    });
}

module.exports = { simpleBalanceFix };
