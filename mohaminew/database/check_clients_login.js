// التحقق من بيانات تسجيل دخول العملاء
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkClientsLogin() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔍 التحقق من بيانات تسجيل دخول العملاء...');
    await client.connect();

    // عرض أعمدة جدول العملاء
    const columns = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'clients' 
      ORDER BY ordinal_position
    `);

    console.log('📋 أعمدة جدول العملاء:');
    columns.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });

    // عرض بيانات العملاء مع تسجيل الدخول
    const clients = await client.query(`
      SELECT id, name, username, password_hash, status, login_attempts, is_online
      FROM clients 
      WHERE username IS NOT NULL
      ORDER BY id
    `);

    console.log('\n📊 بيانات العملاء مع تسجيل الدخول:');
    if (clients.rows.length > 0) {
      clients.rows.forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.name || 'غير محدد'}`);
        console.log(`      - Username: ${client.username || 'غير محدد'}`);
        console.log(`      - Password Hash: ${client.password_hash ? 'موجود' : 'غير موجود'}`);
        console.log(`      - Status: ${client.status || 'غير محدد'}`);
        console.log(`      - Login Attempts: ${client.login_attempts || 0}`);
        console.log(`      - Is Online: ${client.is_online || false}`);
        console.log('');
      });
    } else {
      console.log('   لا توجد بيانات عملاء مع تسجيل دخول');
    }

    // اختبار العميل المحدد
    console.log('🔐 اختبار العميل client_8901:');
    const testClient = await client.query(`
      SELECT * FROM clients 
      WHERE username = 'client_8901'
    `);

    if (testClient.rows.length > 0) {
      const clientData = testClient.rows[0];
      console.log(`   ✅ العميل موجود:`);
      console.log(`      - ID: ${clientData.id}`);
      console.log(`      - Name: ${clientData.name}`);
      console.log(`      - Username: ${clientData.username}`);
      console.log(`      - Password Hash: ${clientData.password_hash}`);
      console.log(`      - Status: ${clientData.status}`);
      console.log(`      - Phone: ${clientData.phone}`);
      console.log(`      - Email: ${clientData.email}`);
    } else {
      console.log('   ❌ العميل client_8901 غير موجود');
      
      // إنشاء العميل إذا لم يكن موجوداً
      console.log('🔧 إنشاء العميل client_8901...');
      await client.query(`
        INSERT INTO clients (
          name, username, password_hash, phone, email, address, 
          national_id, status, created_date
        ) VALUES (
          'أحمد محمد سالم',
          'client_8901',
          '8901',
          '0501234567',
          '<EMAIL>',
          'الرياض، المملكة العربية السعودية',
          '1234567890',
          'active',
          CURRENT_DATE
        )
      `);
      console.log('   ✅ تم إنشاء العميل بنجاح');
    }

    // إنشاء العملاء الآخرين إذا لم يكونوا موجودين
    const otherClients = [
      {
        name: 'شركة النور للتجارة',
        username: 'client_2109',
        password: '2109',
        phone: '0502109876',
        email: '<EMAIL>',
        national_id: '2109876543'
      },
      {
        name: 'فاطمة علي أحمد',
        username: 'client_3344',
        password: '3344',
        phone: '0503344567',
        email: '<EMAIL>',
        national_id: '3344567890'
      },
      {
        name: 'مؤسسة الأمل',
        username: 'client_7788',
        password: '7788',
        phone: '0507788123',
        email: '<EMAIL>',
        national_id: '7788123456'
      }
    ];

    for (const clientInfo of otherClients) {
      const existingClient = await client.query(`
        SELECT id FROM clients WHERE username = $1
      `, [clientInfo.username]);

      if (existingClient.rows.length === 0) {
        await client.query(`
          INSERT INTO clients (
            name, username, password_hash, phone, email, address, 
            national_id, status, created_date
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_DATE)
        `, [
          clientInfo.name,
          clientInfo.username,
          clientInfo.password,
          clientInfo.phone,
          clientInfo.email,
          'المملكة العربية السعودية',
          clientInfo.national_id,
          'active'
        ]);
        console.log(`   ✅ تم إنشاء العميل ${clientInfo.name}`);
      }
    }

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkClientsLogin();
