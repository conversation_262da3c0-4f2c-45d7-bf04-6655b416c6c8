// التحقق من جداول المدفوعات
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function checkPaymentTables() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔍 التحقق من جداول المدفوعات...');
    await client.connect();

    // التحقق من وجود الجداول
    const tablesCheck = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('payment_vouchers', 'payments')
      ORDER BY table_name
    `);

    console.log('📋 الجداول الموجودة:');
    tablesCheck.rows.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });

    // فحص جدول payment_vouchers
    if (tablesCheck.rows.some(t => t.table_name === 'payment_vouchers')) {
      console.log('\n📊 هيكل جدول payment_vouchers:');
      const paymentVouchersColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'payment_vouchers' 
        ORDER BY ordinal_position
      `);

      paymentVouchersColumns.rows.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });

      // عرض عينة من البيانات
      const samplePaymentVouchers = await client.query('SELECT * FROM payment_vouchers LIMIT 3');
      console.log('\n📋 عينة من بيانات payment_vouchers:');
      if (samplePaymentVouchers.rows.length > 0) {
        samplePaymentVouchers.rows.forEach((row, index) => {
          console.log(`   ${index + 1}. ID: ${row.id}, Amount: ${row.amount || 'N/A'}, Date: ${row.voucher_date || 'N/A'}`);
        });
      } else {
        console.log('   لا توجد بيانات');
      }
    } else {
      console.log('\n❌ جدول payment_vouchers غير موجود');
    }

    // فحص جدول payments
    if (tablesCheck.rows.some(t => t.table_name === 'payments')) {
      console.log('\n📊 هيكل جدول payments:');
      const paymentsColumns = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'payments' 
        ORDER BY ordinal_position
      `);

      paymentsColumns.rows.forEach(col => {
        console.log(`   - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });

      // عرض عينة من البيانات
      const samplePayments = await client.query('SELECT * FROM payments LIMIT 3');
      console.log('\n📋 عينة من بيانات payments:');
      if (samplePayments.rows.length > 0) {
        samplePayments.rows.forEach((row, index) => {
          console.log(`   ${index + 1}. ID: ${row.id}, Amount: ${row.amount || 'N/A'}, Date: ${row.payment_date || row.created_date || 'N/A'}`);
        });
      } else {
        console.log('   لا توجد بيانات');
      }
    } else {
      console.log('\n❌ جدول payments غير موجود');
    }

    // البحث عن جداول أخرى متعلقة بالمدفوعات
    console.log('\n🔍 البحث عن جداول أخرى متعلقة بالمدفوعات:');
    const relatedTables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND (table_name LIKE '%payment%' OR table_name LIKE '%voucher%' OR table_name LIKE '%receipt%')
      ORDER BY table_name
    `);

    relatedTables.rows.forEach(table => {
      console.log(`   - ${table.table_name}`);
    });

    // فحص العلاقات بين الجداول
    console.log('\n🔗 فحص العلاقات (Foreign Keys):');
    const foreignKeys = await client.query(`
      SELECT 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name 
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE constraint_type = 'FOREIGN KEY' 
      AND (tc.table_name IN ('payment_vouchers', 'payments') 
           OR ccu.table_name IN ('payment_vouchers', 'payments'))
    `);

    if (foreignKeys.rows.length > 0) {
      foreignKeys.rows.forEach(fk => {
        console.log(`   - ${fk.table_name}.${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`);
      });
    } else {
      console.log('   لا توجد علاقات مُعرَّفة');
    }

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await client.end();
  }
}

checkPaymentTables();
