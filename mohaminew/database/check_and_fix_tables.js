const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function checkAndFixTables() {
  try {
    console.log('🔍 فحص وإصلاح الجداول الموجودة...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. فحص جدول مراكز التكلفة
    console.log('\n📊 فحص جدول مراكز التكلفة...');
    
    try {
      const costCenterStructure = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'cost_centers'
        ORDER BY ordinal_position
      `);
      
      if (costCenterStructure.rows.length > 0) {
        console.log('   📋 هيكل جدول cost_centers الحالي:');
        costCenterStructure.rows.forEach(col => {
          console.log(`      ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`);
        });
        
        // إضافة الأعمدة المفقودة
        const requiredColumns = [
          { name: 'center_code', type: 'VARCHAR(20)', constraint: 'UNIQUE' },
          { name: 'center_name', type: 'VARCHAR(255)' },
          { name: 'parent_id', type: 'INTEGER' },
          { name: 'center_level', type: 'INTEGER DEFAULT 1' },
          { name: 'is_active', type: 'BOOLEAN DEFAULT TRUE' },
          { name: 'description', type: 'TEXT' }
        ];
        
        for (const col of requiredColumns) {
          try {
            await client.query(`
              ALTER TABLE cost_centers 
              ADD COLUMN IF NOT EXISTS ${col.name} ${col.type}
            `);
            console.log(`   ✅ تم إضافة العمود ${col.name}`);
          } catch (error) {
            console.log(`   ⚠️ العمود ${col.name} موجود بالفعل`);
          }
        }
        
        // إضافة القيود
        try {
          await client.query(`
            ALTER TABLE cost_centers 
            ADD CONSTRAINT IF NOT EXISTS cost_centers_center_code_unique 
            UNIQUE (center_code)
          `);
        } catch (error) {
          console.log('   ⚠️ قيد center_code موجود بالفعل');
        }
        
      } else {
        // إنشاء الجدول من الصفر
        await client.query(`
          CREATE TABLE cost_centers (
            id SERIAL PRIMARY KEY,
            center_code VARCHAR(20) UNIQUE NOT NULL,
            center_name VARCHAR(255) NOT NULL,
            parent_id INTEGER REFERENCES cost_centers(id),
            center_level INTEGER DEFAULT 1,
            is_active BOOLEAN DEFAULT TRUE,
            description TEXT,
            created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
        `);
        console.log('   ✅ تم إنشاء جدول cost_centers جديد');
      }
    } catch (error) {
      console.log('   ❌ خطأ في فحص جدول مراكز التكلفة:', error.message);
    }

    // 2. إضافة بيانات تجريبية لمراكز التكلفة
    console.log('\n📝 إضافة بيانات تجريبية لمراكز التكلفة...');
    
    try {
      // التحقق من وجود بيانات
      const existingData = await client.query(`SELECT COUNT(*) as count FROM cost_centers`);
      
      if (parseInt(existingData.rows[0].count) === 0) {
        await client.query(`
          INSERT INTO cost_centers (center_code, center_name, center_level, description) 
          VALUES 
            ('CC001', 'الإدارة العامة', 1, 'مركز تكلفة الإدارة العامة'),
            ('CC002', 'القسم القانوني', 1, 'مركز تكلفة القسم القانوني'),
            ('CC003', 'المحاسبة والمالية', 1, 'مركز تكلفة المحاسبة والمالية'),
            ('CC004', 'الموارد البشرية', 1, 'مركز تكلفة الموارد البشرية')
        `);
        console.log('   ✅ تم إضافة بيانات تجريبية');
      } else {
        console.log('   ⚠️ البيانات موجودة بالفعل');
      }
    } catch (error) {
      console.log('   ❌ خطأ في إضافة البيانات:', error.message);
    }

    // 3. إنشاء الجدول الأب للسندات والقيود
    console.log('\n📋 إنشاء الجدول الأب للسندات والقيود...');
    
    await client.query(`
      DROP TABLE IF EXISTS financial_transactions CASCADE;
      DROP TABLE IF EXISTS vouchers_master CASCADE;
      
      CREATE TABLE vouchers_master (
        id SERIAL PRIMARY KEY,
        voucher_number VARCHAR(50) UNIQUE NOT NULL,
        voucher_date DATE NOT NULL,
        voucher_type VARCHAR(20) NOT NULL CHECK (voucher_type IN ('سند صرف', 'سند قبض', 'قيد يومي')),
        cost_center_id INTEGER REFERENCES cost_centers(id),
        user_id INTEGER,
        description TEXT,
        total_amount DECIMAL(15,2) DEFAULT 0,
        status VARCHAR(20) DEFAULT 'مسودة' CHECK (status IN ('مسودة', 'معتمد', 'ملغي')),
        reference_number VARCHAR(100),
        notes TEXT,
        created_by INTEGER,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        approved_by INTEGER,
        approved_date TIMESTAMP
      );
    `);
    console.log('   ✅ تم إنشاء جدول vouchers_master');

    // 4. إنشاء جدول العمليات المالية
    console.log('\n💰 إنشاء جدول العمليات المالية...');
    
    await client.query(`
      CREATE TABLE financial_transactions (
        id SERIAL PRIMARY KEY,
        voucher_id INTEGER NOT NULL REFERENCES vouchers_master(id) ON DELETE CASCADE,
        debit_amount DECIMAL(15,2) DEFAULT 0,
        credit_amount DECIMAL(15,2) DEFAULT 0,
        debit_account_id INTEGER REFERENCES chart_of_accounts(id),
        credit_account_id INTEGER REFERENCES chart_of_accounts(id),
        debit_account_code VARCHAR(20),
        credit_account_code VARCHAR(20),
        debit_description TEXT,
        credit_description TEXT,
        transaction_date DATE,
        cost_center_id INTEGER REFERENCES cost_centers(id),
        line_number INTEGER DEFAULT 1,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('   ✅ تم إنشاء جدول financial_transactions');

    // 5. إنشاء الفهارس
    console.log('\n🔍 إنشاء الفهارس...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_vouchers_master_date ON vouchers_master(voucher_date);',
      'CREATE INDEX IF NOT EXISTS idx_vouchers_master_type ON vouchers_master(voucher_type);',
      'CREATE INDEX IF NOT EXISTS idx_vouchers_master_status ON vouchers_master(status);',
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_voucher ON financial_transactions(voucher_id);',
      'CREATE INDEX IF NOT EXISTS idx_financial_transactions_date ON financial_transactions(transaction_date);'
    ];

    for (const indexQuery of indexes) {
      await client.query(indexQuery);
    }
    console.log('   ✅ تم إنشاء جميع الفهارس');

    // 6. إنشاء الدوال المساعدة
    console.log('\n⚙️ إنشاء الدوال المساعدة...');
    
    await client.query(`
      CREATE OR REPLACE FUNCTION generate_voucher_number(v_type VARCHAR(20))
      RETURNS VARCHAR(50) AS $$
      DECLARE
        prefix VARCHAR(10);
        next_number INTEGER;
        current_year VARCHAR(4);
      BEGIN
        current_year := EXTRACT(YEAR FROM CURRENT_DATE)::VARCHAR;
        
        CASE v_type
          WHEN 'سند صرف' THEN prefix := 'PAY';
          WHEN 'سند قبض' THEN prefix := 'REC';
          WHEN 'قيد يومي' THEN prefix := 'JE';
          ELSE prefix := 'VOC';
        END CASE;
        
        SELECT COALESCE(MAX(
          CAST(SUBSTRING(voucher_number FROM LENGTH(prefix || current_year) + 1) AS INTEGER)
        ), 0) + 1
        INTO next_number
        FROM vouchers_master 
        WHERE voucher_type = v_type 
          AND voucher_number LIKE prefix || current_year || '%';
        
        RETURN prefix || current_year || LPAD(next_number::VARCHAR, 4, '0');
      END;
      $$ LANGUAGE plpgsql;
    `);
    console.log('   ✅ تم إنشاء دالة generate_voucher_number');

    // 7. إضافة بيانات تجريبية
    console.log('\n📝 إضافة بيانات تجريبية للسندات...');
    
    // إضافة سند قبض تجريبي
    const recVoucherNumber = await client.query(`SELECT generate_voucher_number('سند قبض') as number`);
    const recVoucher = await client.query(`
      INSERT INTO vouchers_master (
        voucher_number, voucher_date, voucher_type, 
        cost_center_id, description, created_by
      ) VALUES ($1, CURRENT_DATE, 'سند قبض', 2, 'قبض أتعاب قضية رقم 2024/001', 1)
      RETURNING id
    `, [recVoucherNumber.rows[0].number]);

    await client.query(`
      INSERT INTO financial_transactions (
        voucher_id, debit_amount, credit_amount,
        debit_account_code, credit_account_code,
        debit_description, credit_description,
        transaction_date, line_number
      ) VALUES 
      ($1, 5000, 0, '1111', '', 'قبض نقدي من العميل', '', CURRENT_DATE, 1),
      ($1, 0, 5000, '', '411', '', 'أتعاب قانونية', CURRENT_DATE, 2)
    `, [recVoucher.rows[0].id]);

    console.log(`   ✅ تم إضافة سند قبض: ${recVoucherNumber.rows[0].number}`);

    // إضافة سند صرف تجريبي
    const payVoucherNumber = await client.query(`SELECT generate_voucher_number('سند صرف') as number`);
    const payVoucher = await client.query(`
      INSERT INTO vouchers_master (
        voucher_number, voucher_date, voucher_type, 
        cost_center_id, description, created_by
      ) VALUES ($1, CURRENT_DATE, 'سند صرف', 2, 'صرف مصروفات محكمة', 1)
      RETURNING id
    `, [payVoucherNumber.rows[0].number]);

    await client.query(`
      INSERT INTO financial_transactions (
        voucher_id, debit_amount, credit_amount,
        debit_account_code, credit_account_code,
        debit_description, credit_description,
        transaction_date, line_number
      ) VALUES 
      ($1, 500, 0, '511', '', 'مصروفات محكمة', '', CURRENT_DATE, 1),
      ($1, 0, 500, '', '1111', '', 'دفع نقدي', CURRENT_DATE, 2)
    `, [payVoucher.rows[0].id]);

    console.log(`   ✅ تم إضافة سند صرف: ${payVoucherNumber.rows[0].number}`);

    // 8. عرض ملخص النظام
    console.log('\n📊 ملخص النظام الجديد:');
    
    const voucherCount = await client.query(`SELECT COUNT(*) as count FROM vouchers_master`);
    const transactionCount = await client.query(`SELECT COUNT(*) as count FROM financial_transactions`);
    const costCenterCount = await client.query(`SELECT COUNT(*) as count FROM cost_centers`);
    
    console.log(`   📋 السندات والقيود: ${voucherCount.rows[0].count}`);
    console.log(`   💰 العمليات المالية: ${transactionCount.rows[0].count}`);
    console.log(`   🏢 مراكز التكلفة: ${costCenterCount.rows[0].count}`);

    console.log('\n✅ تم إنشاء وإصلاح النظام بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  checkAndFixTables()
    .then(() => {
      console.log('🎉 تم إنجاز الإصلاح بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في الإصلاح:', error);
      process.exit(1);
    });
}

module.exports = { checkAndFixTables };
