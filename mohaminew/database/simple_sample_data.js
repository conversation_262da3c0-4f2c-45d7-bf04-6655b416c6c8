const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123',
  ssl: false
});

async function insertSimpleSampleData() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 بدء إدراج البيانات التجريبية المبسطة...');

    // إدراج بيانات تجريبية للوثائق (بدون ربط بالقضايا)
    await client.query(`
      INSERT INTO documents (
        title, description, file_name, file_path, file_size, file_type, mime_type,
        category, subcategory, tags, access_level, is_confidential
      ) VALUES 
      ('دليل الإجراءات القانونية', 'دليل شامل للإجراءات القانونية في المملكة', 'legal_guide.pdf', '/uploads/documents/legal_guide.pdf', 1024000, 'pdf', 'application/pdf', 'guide', 'legal', ARRAY['دليل', 'إجراءات', 'قانون'], 'public', false),
      ('نموذج عقد استشارة', 'نموذج عقد استشارة قانونية', 'contract_template.pdf', '/uploads/documents/contract_template.pdf', 512000, 'pdf', 'application/pdf', 'template', 'contract', ARRAY['نموذج', 'عقد', 'استشارة'], 'public', false),
      ('وثيقة سرية', 'وثيقة سرية للاستخدام الداخلي', 'confidential_doc.pdf', '/uploads/documents/confidential_doc.pdf', 256000, 'pdf', 'application/pdf', 'internal', 'confidential', ARRAY['سري', 'داخلي'], 'restricted', true),
      ('تقرير مالي', 'تقرير مالي ربع سنوي', 'financial_report.pdf', '/uploads/documents/financial_report.pdf', 2048000, 'pdf', 'application/pdf', 'financial', 'report', ARRAY['مالي', 'تقرير', 'ربع سنوي'], 'private', false)
      ON CONFLICT DO NOTHING
    `);

    // إدراج بيانات تجريبية لتسجيلات الوقت (بدون ربط بالقضايا)
    await client.query(`
      INSERT INTO time_entries (
        employee_id, start_time, end_time, duration_minutes,
        task_description, task_category, hourly_rate, billable_amount, is_billable, status
      ) VALUES 
      (1, CURRENT_TIMESTAMP - INTERVAL '3 hours', CURRENT_TIMESTAMP - INTERVAL '2 hours', 60, 'مراجعة الوثائق القانونية', 'documentation', 200.00, 200.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '2 days', CURRENT_TIMESTAMP - INTERVAL '2 days' + INTERVAL '90 minutes', 90, 'اجتماع مع العميل', 'meeting', 200.00, 300.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day' + INTERVAL '45 minutes', 45, 'بحث قانوني', 'research', 200.00, 150.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '4 hours', CURRENT_TIMESTAMP - INTERVAL '3 hours', 60, 'إعداد التقارير', 'documentation', 200.00, 200.00, true, 'completed'),
      (1, CURRENT_TIMESTAMP - INTERVAL '6 hours', CURRENT_TIMESTAMP - INTERVAL '5 hours', 60, 'مراسلات قانونية', 'correspondence', 200.00, 200.00, true, 'completed')
      ON CONFLICT DO NOTHING
    `);

    // إدراج فاتورة تجريبية
    await client.query(`
      INSERT INTO invoices (
        invoice_number, client_id, client_name, client_address,
        invoice_date, due_date, subtotal, tax_rate, tax_amount,
        discount_amount, total_amount, status, payment_status, created_by
      ) VALUES 
      ('INV000001', 1, 'عميل تجريبي', 'الرياض، المملكة العربية السعودية',
       CURRENT_DATE - INTERVAL '5 days', CURRENT_DATE + INTERVAL '25 days', 1050.00, 15.00, 157.50,
       0.00, 1207.50, 'sent', 'unpaid', 1)
      ON CONFLICT (invoice_number) DO NOTHING
    `);

    // إدراج عناصر الفاتورة
    await client.query(`
      INSERT INTO invoice_items (
        invoice_id, description, quantity, unit_price, total_price, item_type
      ) VALUES 
      (1, 'مراجعة الوثائق القانونية', 1.00, 200.00, 200.00, 'service'),
      (1, 'اجتماع مع العميل', 1.50, 200.00, 300.00, 'service'),
      (1, 'بحث قانوني', 0.75, 200.00, 150.00, 'service'),
      (1, 'إعداد التقارير', 1.00, 200.00, 200.00, 'service'),
      (1, 'مراسلات قانونية', 1.00, 200.00, 200.00, 'service')
      ON CONFLICT DO NOTHING
    `);

    // إدراج حساب عميل تجريبي (بدون ربط بعميل محدد)
    await client.query(`
      INSERT INTO client_portal_accounts (
        client_id, username, email, password_hash, is_active, is_verified,
        language, timezone, notification_preferences
      ) VALUES 
      (1, 'demo_client', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Txjyvq', true, true,
       'ar', 'Asia/Riyadh', '{"email_notifications": true, "case_updates": true, "document_uploads": true}')
      ON CONFLICT (client_id) DO NOTHING
    `);

    // إدراج إشعارات تجريبية
    await client.query(`
      INSERT INTO client_notifications (
        client_id, title, message, type, is_read
      ) VALUES 
      (1, 'مرحباً بك في بوابة العملاء', 'تم إنشاء حسابك بنجاح في بوابة العملاء.', 'success', false),
      (1, 'وثيقة جديدة', 'تم إضافة وثيقة جديدة لحسابك', 'info', false),
      (1, 'فاتورة جديدة', 'تم إنشاء فاتورة جديدة رقم INV000001', 'info', true),
      (1, 'تذكير', 'لديك موعد قادم', 'warning', false)
      ON CONFLICT DO NOTHING
    `);

    // إدراج طلبات تجريبية
    await client.query(`
      INSERT INTO client_requests (
        client_id, request_type, title, description, priority, status
      ) VALUES 
      (1, 'document_request', 'طلب وثيقة', 'أحتاج نسخة من الوثيقة', 'medium', 'pending'),
      (1, 'meeting_request', 'طلب موعد', 'أرغب في حجز موعد', 'high', 'in_progress'),
      (1, 'status_inquiry', 'استفسار', 'استفسار عام', 'low', 'completed')
      ON CONFLICT DO NOTHING
    `);

    console.log('✅ تم إدراج جميع البيانات التجريبية بنجاح!');
    console.log('📋 البيانات المُدرجة:');
    console.log('   - 4 وثائق تجريبية');
    console.log('   - 5 تسجيلات وقت');
    console.log('   - فاتورة واحدة مع 5 عناصر');
    console.log('   - حساب عميل واحد');
    console.log('   - 4 إشعارات');
    console.log('   - 3 طلبات');

  } catch (error) {
    console.error('❌ خطأ في إدراج البيانات التجريبية:', error);
    throw error;
  } finally {
    client.release();
  }
}

// تشغيل الدالة
insertSimpleSampleData()
  .then(() => {
    console.log('🎉 تم الانتهاء من إدراج البيانات التجريبية!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 فشل في إدراج البيانات التجريبية:', error);
    process.exit(1);
  });