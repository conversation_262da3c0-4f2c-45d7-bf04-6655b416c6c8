// إضافة حقول تسجيل الدخول لجدول الموكلين
const { Client } = require('pg');
const bcrypt = require('bcrypt');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function addLoginFieldsToClients() {
  const client = new Client(dbConfig);

  try {
    console.log('🔄 جاري إضافة حقول تسجيل الدخول لجدول الموكلين...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إضافة أعمدة تسجيل الدخول
    try {
      await client.query(`
        ALTER TABLE clients
        ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE,
        ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255),
        ADD COLUMN IF NOT EXISTS last_login TIMESTAMP,
        ADD COLUMN IF NOT EXISTS is_online BOOLEAN DEFAULT FALSE,
        ADD COLUMN IF NOT EXISTS login_attempts INTEGER DEFAULT 0,
        ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP
      `);
      console.log('✅ تم إضافة أعمدة تسجيل الدخول');
    } catch (error) {
      console.log('⚠️  أعمدة تسجيل الدخول موجودة بالفعل');
    }

    // إنشاء أسماء مستخدمين افتراضية للموكلين الموجودين
    const existingClients = await client.query(`
      SELECT id, name, id_number
      FROM clients
      WHERE username IS NULL
    `);

    console.log(`📋 تحديث ${existingClients.rows.length} موكل بأسماء مستخدمين افتراضية...`);

    for (const clientRow of existingClients.rows) {
      // إنشاء اسم مستخدم من الاسم ورقم الهوية
      let username = `client_${clientRow.id}`;
      if (clientRow.id_number) {
        username = `client_${clientRow.id_number.slice(-4)}`;
      }

      // كلمة مرور افتراضية (رقم الهوية أو 123456)
      const defaultPassword = clientRow.id_number || '123456';
      const hashedPassword = await bcrypt.hash(defaultPassword, 10);

      try {
        await client.query(`
          UPDATE clients
          SET username = $1, password_hash = $2
          WHERE id = $3
        `, [username, hashedPassword, clientRow.id]);

        console.log(`   ✅ تم تحديث الموكل: ${clientRow.name} - اسم المستخدم: ${username}`);
      } catch (error) {
        // في حالة تكرار اسم المستخدم
        const uniqueUsername = `${username}_${clientRow.id}`;
        await client.query(`
          UPDATE clients
          SET username = $1, password_hash = $2
          WHERE id = $3
        `, [uniqueUsername, hashedPassword, clientRow.id]);

        console.log(`   ✅ تم تحديث الموكل: ${clientRow.name} - اسم المستخدم: ${uniqueUsername}`);
      }
    }

    // إضافة فهارس للأداء
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_clients_username ON clients(username)',
      'CREATE INDEX IF NOT EXISTS idx_clients_online ON clients(is_online)',
      'CREATE INDEX IF NOT EXISTS idx_clients_last_login ON clients(last_login)'
    ];

    for (const index of indexes) {
      await client.query(index);
    }
    console.log('✅ تم إضافة الفهارس');

    // عرض النتائج
    const summary = await client.query(`
      SELECT
        COUNT(*) as total_clients,
        COUNT(CASE WHEN username IS NOT NULL THEN 1 END) as clients_with_login,
        COUNT(CASE WHEN is_online = true THEN 1 END) as online_clients
      FROM clients
    `);

    console.log('📊 ملخص الموكلين:');
    console.log(`   - إجمالي الموكلين: ${summary.rows[0].total_clients}`);
    console.log(`   - الموكلين مع تسجيل دخول: ${summary.rows[0].clients_with_login}`);
    console.log(`   - الموكلين المتصلين: ${summary.rows[0].online_clients}`);

    // عرض عينة من أسماء المستخدمين
    const sampleUsers = await client.query(`
      SELECT name, username
      FROM clients
      WHERE username IS NOT NULL
      LIMIT 5
    `);

    console.log('👥 عينة من أسماء المستخدمين:');
    sampleUsers.rows.forEach(row => {
      console.log(`   - ${row.name}: ${row.username}`);
    });

    console.log('🎉 تم إضافة حقول تسجيل الدخول للموكلين بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إضافة الحقول:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

addLoginFieldsToClients();
