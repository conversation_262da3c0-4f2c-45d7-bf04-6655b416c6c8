const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function addContractDateToIssues() {
  
  try {
    console.log('🔄 جاري إضافة عمود تاريخ التعاقد لجدول القضايا...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من الأعمدة الموجودة
    const existingColumns = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'issues'
    `);
    
    const columnNames = existingColumns.rows.map(row => row.column_name);
    console.log('📋 الأعمدة الموجودة في جدول القضايا:', columnNames);

    // إضافة عمود تاريخ التعاقد
    if (!columnNames.includes('contract_date')) {
      await client.query(`
        ALTER TABLE issues 
        ADD COLUMN contract_date DATE DEFAULT CURRENT_DATE
      `);
      console.log('✅ تم إضافة عمود contract_date');
    } else {
      console.log('⚠️  عمود contract_date موجود بالفعل');
    }

    // تحديث القضايا الموجودة بتاريخ اليوم إذا كانت فارغة
    const updateResult = await client.query(`
      UPDATE issues 
      SET contract_date = CURRENT_DATE 
      WHERE contract_date IS NULL
    `);
    console.log(`✅ تم تحديث ${updateResult.rowCount} قضية بتاريخ التعاقد الافتراضي`);

    // التحقق من النتائج
    const issuesCount = await client.query('SELECT COUNT(*) as count FROM issues');
    const contractDates = await client.query(`
      SELECT 
        contract_date,
        COUNT(*) as count
      FROM issues 
      WHERE contract_date IS NOT NULL
      GROUP BY contract_date
      ORDER BY contract_date DESC
      LIMIT 5
    `);

    console.log('📋 ملخص القضايا:');
    console.log(`   - إجمالي القضايا: ${issuesCount.rows[0].count}`);
    console.log('   - أحدث تواريخ التعاقد:');
    contractDates.rows.forEach(row => {
      console.log(`     * ${row.contract_date}: ${row.count} قضية`);
    });

    // عرض هيكل الجدول المحدث
    const updatedStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'issues'
      ORDER BY ordinal_position
    `);

    console.log('\n📊 هيكل جدول القضايا المحدث:');
    updatedStructure.rows.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'NO' ? '(مطلوب)' : '(اختياري)'}`);
    });

    console.log('\n🎉 تم إضافة عمود تاريخ التعاقد بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إضافة عمود تاريخ التعاقد:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
addContractDateToIssues();
