-- تحديث قاعدة البيانات للنظام القانوني
-- Database: mohammi
-- User: postgres
-- Password: yemen123

-- ===================================
-- 1. تحديث جدول النسب المالية (lineages)
-- ===================================

-- حذف الأعمدة غير المطلوبة من جدول النسب المالية
ALTER TABLE IF EXISTS lineages 
DROP COLUMN IF EXISTS management_percentage,
DROP COLUMN IF EXISTS court_percentage,
DROP COLUMN IF EXISTS commission_percentage,
DROP COLUMN IF EXISTS other_percentage;

-- إعادة تسمية العمود group_name إلى name
ALTER TABLE IF EXISTS lineages 
RENAME COLUMN group_name TO name;

-- التأكد من وجود عمود admin_percentage
ALTER TABLE IF EXISTS lineages 
ADD COLUMN IF NOT EXISTS admin_percentage DECIMAL(5,2) DEFAULT 0;

-- إنشاء جدول النسب المالية إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS lineages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    admin_percentage DECIMAL(5,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);

-- إدراج البيانات النموذجية للنسب المالية
INSERT INTO lineages (name, admin_percentage, created_date) VALUES
('نسب القضايا المدنية', 15.0, '2024-01-01'),
('نسب القضايا التجارية', 20.0, '2024-01-02'),
('نسب القضايا الجنائية', 10.0, '2024-01-03'),
('نسب القضايا العمالية', 25.0, '2024-01-04'),
('نسب القضايا العقارية', 18.0, '2024-01-05'),
('نسب القضايا الإدارية', 12.0, '2024-01-06'),
('نسب القضايا الأسرية', 8.0, '2024-01-07'),
('نسب القضايا الضريبية', 22.0, '2024-01-08')
ON CONFLICT (id) DO NOTHING;

-- ===================================
-- 2. إنشاء جدول الخدمات (services)
-- ===================================

-- إنشاء جدول الخدمات
CREATE TABLE IF NOT EXISTS services (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    created_date DATE DEFAULT CURRENT_DATE
);

-- إدراج البيانات النموذجية للخدمات
INSERT INTO services (name, created_date) VALUES
('اعداد', '2024-01-01'),
('جلسة', '2024-01-01'),
('متابعة', '2024-01-01'),
('اشراف', '2024-01-01'),
('مصروفات قضائية', '2024-01-01'),
('استشارات قانونية', '2024-01-01'),
('صياغة عقود', '2024-01-01'),
('تمثيل قانوني', '2024-01-01')
ON CONFLICT (name) DO NOTHING;

-- ===================================
-- 3. إنشاء جدول توزيع القضايا (case_distribution)
-- ===================================

-- إنشاء جدول توزيع القضايا
CREATE TABLE IF NOT EXISTS case_distribution (
    id SERIAL PRIMARY KEY,
    issue_id INTEGER REFERENCES issues(id) ON DELETE CASCADE,
    lineage_id INTEGER REFERENCES lineages(id) ON DELETE SET NULL,
    admin_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) DEFAULT 0,
    created_date DATE DEFAULT CURRENT_DATE
);

-- ===================================
-- 4. إنشاء جدول تفاصيل توزيع الخدمات (service_distributions)
-- ===================================

-- إنشاء جدول تفاصيل توزيع الخدمات
CREATE TABLE IF NOT EXISTS service_distributions (
    id SERIAL PRIMARY KEY,
    case_distribution_id INTEGER REFERENCES case_distribution(id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
    percentage DECIMAL(5,2) DEFAULT 0,
    amount DECIMAL(15,2) DEFAULT 0,
    lawyer_id INTEGER REFERENCES employees(id) ON DELETE SET NULL,
    created_date DATE DEFAULT CURRENT_DATE
);

-- ===================================
-- 5. إنشاء الفهارس لتحسين الأداء
-- ===================================

-- فهارس جدول النسب المالية
CREATE INDEX IF NOT EXISTS idx_lineages_name ON lineages(name);
CREATE INDEX IF NOT EXISTS idx_lineages_created_date ON lineages(created_date);

-- فهارس جدول الخدمات
CREATE INDEX IF NOT EXISTS idx_services_name ON services(name);
CREATE INDEX IF NOT EXISTS idx_services_created_date ON services(created_date);

-- فهارس جدول توزيع القضايا
CREATE INDEX IF NOT EXISTS idx_case_distribution_issue_id ON case_distribution(issue_id);
CREATE INDEX IF NOT EXISTS idx_case_distribution_lineage_id ON case_distribution(lineage_id);
CREATE INDEX IF NOT EXISTS idx_case_distribution_created_date ON case_distribution(created_date);

-- فهارس جدول تفاصيل توزيع الخدمات
CREATE INDEX IF NOT EXISTS idx_service_distributions_case_id ON service_distributions(case_distribution_id);
CREATE INDEX IF NOT EXISTS idx_service_distributions_service_id ON service_distributions(service_id);
CREATE INDEX IF NOT EXISTS idx_service_distributions_lawyer_id ON service_distributions(lawyer_id);

-- ===================================
-- 6. إنشاء Views مفيدة
-- ===================================

-- View لعرض توزيع القضايا مع التفاصيل
CREATE OR REPLACE VIEW case_distribution_details AS
SELECT 
    cd.id,
    cd.issue_id,
    i.title as issue_title,
    i.case_number,
    i.amount as case_amount,
    cd.lineage_id,
    l.name as lineage_name,
    l.admin_percentage,
    cd.admin_amount,
    cd.remaining_amount,
    cd.created_date
FROM case_distribution cd
LEFT JOIN issues i ON cd.issue_id = i.id
LEFT JOIN lineages l ON cd.lineage_id = l.id;

-- View لعرض تفاصيل توزيع الخدمات
CREATE OR REPLACE VIEW service_distribution_details AS
SELECT 
    sd.id,
    sd.case_distribution_id,
    cd.issue_id,
    i.title as issue_title,
    sd.service_id,
    s.name as service_name,
    sd.percentage,
    sd.amount,
    sd.lawyer_id,
    e.name as lawyer_name,
    sd.created_date
FROM service_distributions sd
LEFT JOIN case_distribution cd ON sd.case_distribution_id = cd.id
LEFT JOIN issues i ON cd.issue_id = i.id
LEFT JOIN services s ON sd.service_id = s.id
LEFT JOIN employees e ON sd.lawyer_id = e.id;

-- ===================================
-- 7. منح الصلاحيات
-- ===================================

-- منح جميع الصلاحيات للمستخدم postgres
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- ===================================
-- تم الانتهاء من تحديث قاعدة البيانات
-- ===================================

-- عرض ملخص الجداول المحدثة
SELECT 
    'lineages' as table_name,
    COUNT(*) as record_count
FROM lineages
UNION ALL
SELECT 
    'services' as table_name,
    COUNT(*) as record_count
FROM services
UNION ALL
SELECT 
    'case_distribution' as table_name,
    COUNT(*) as record_count
FROM case_distribution
UNION ALL
SELECT 
    'service_distributions' as table_name,
    COUNT(*) as record_count
FROM service_distributions;
