const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123',
  ssl: false
});

async function createAdvancedFeaturesTables() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 بدء إنشاء جداول الميزات المتقدمة...');

    // 1. جدول إدارة الوثائق
    await client.query(`
      CREATE TABLE IF NOT EXISTS documents (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT,
        file_type VARCHAR(100),
        mime_type VARCHAR(100),
        
        -- ربط بالجداول الأخرى
        case_id INTEGER REFERENCES issues(id),
        client_id INTEGER REFERENCES clients(id),
        employee_id INTEGER REFERENCES employees(id),
        
        -- تصنيف الوثائق
        category VARCHAR(100), -- contract, evidence, correspondence, etc.
        subcategory VARCHAR(100),
        tags TEXT[], -- مصفوفة من العلامات
        
        -- البحث في المحتوى
        content_text TEXT, -- النص المستخرج من الملف
        keywords TEXT[], -- كلمات مفتاحية
        
        -- الأمان والصلاحيات
        access_level VARCHAR(50) DEFAULT 'private', -- public, private, restricted
        is_confidential BOOLEAN DEFAULT false,
        
        -- معلومات الإنشاء والتعديل
        uploaded_by INTEGER REFERENCES users(id),
        version_number INTEGER DEFAULT 1,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 2. جدول إصدارات الوثائق
    await client.query(`
      CREATE TABLE IF NOT EXISTS document_versions (
        id SERIAL PRIMARY KEY,
        document_id INTEGER REFERENCES documents(id) ON DELETE CASCADE,
        version_number INTEGER NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT,
        changes_description TEXT,
        uploaded_by INTEGER REFERENCES users(id),
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 3. جدول مشاركة الوثائق
    await client.query(`
      CREATE TABLE IF NOT EXISTS document_shares (
        id SERIAL PRIMARY KEY,
        document_id INTEGER REFERENCES documents(id) ON DELETE CASCADE,
        shared_with_user INTEGER REFERENCES users(id),
        shared_with_client INTEGER REFERENCES clients(id),
        permission_level VARCHAR(50) DEFAULT 'read', -- read, write, admin
        shared_by INTEGER REFERENCES users(id),
        expires_at TIMESTAMP,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 4. جدول تتبع الوقت
    await client.query(`
      CREATE TABLE IF NOT EXISTS time_entries (
        id SERIAL PRIMARY KEY,
        
        -- ربط بالقضايا والمشاريع
        case_id INTEGER REFERENCES issues(id),
        client_id INTEGER REFERENCES clients(id),
        employee_id INTEGER REFERENCES employees(id) NOT NULL,
        
        -- تفاصيل الوقت
        start_time TIMESTAMP NOT NULL,
        end_time TIMESTAMP,
        duration_minutes INTEGER, -- المدة بالدقائق
        
        -- وصف العمل
        task_description TEXT NOT NULL,
        task_category VARCHAR(100), -- research, meeting, court, documentation
        
        -- الفوترة
        hourly_rate DECIMAL(10,2),
        billable_amount DECIMAL(12,2),
        is_billable BOOLEAN DEFAULT true,
        is_billed BOOLEAN DEFAULT false,
        
        -- الحالة
        status VARCHAR(50) DEFAULT 'active', -- active, paused, completed
        
        -- معلومات إضافية
        notes TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 5. جدول الفواتير
    await client.query(`
      CREATE TABLE IF NOT EXISTS invoices (
        id SERIAL PRIMARY KEY,
        invoice_number VARCHAR(50) UNIQUE NOT NULL,
        
        -- معلومات العميل
        client_id INTEGER REFERENCES clients(id) NOT NULL,
        client_name VARCHAR(255),
        client_address TEXT,
        
        -- تفاصيل الفاتورة
        invoice_date DATE DEFAULT CURRENT_DATE,
        due_date DATE,
        
        -- المبالغ
        subtotal DECIMAL(12,2) DEFAULT 0,
        tax_rate DECIMAL(5,2) DEFAULT 0,
        tax_amount DECIMAL(12,2) DEFAULT 0,
        discount_amount DECIMAL(12,2) DEFAULT 0,
        total_amount DECIMAL(12,2) DEFAULT 0,
        
        -- الحالة والدفع
        status VARCHAR(50) DEFAULT 'draft', -- draft, sent, paid, overdue, cancelled
        payment_status VARCHAR(50) DEFAULT 'unpaid', -- unpaid, partial, paid
        paid_amount DECIMAL(12,2) DEFAULT 0,
        payment_date DATE,
        
        -- معلومات إضافية
        notes TEXT,
        terms_conditions TEXT,
        
        -- معلومات الإنشاء
        created_by INTEGER REFERENCES users(id),
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 6. جدول عناصر الفاتورة
    await client.query(`
      CREATE TABLE IF NOT EXISTS invoice_items (
        id SERIAL PRIMARY KEY,
        invoice_id INTEGER REFERENCES invoices(id) ON DELETE CASCADE,
        
        -- تفاصيل العنصر
        description TEXT NOT NULL,
        quantity DECIMAL(10,2) DEFAULT 1,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(12,2) NOT NULL,
        
        -- ربط بتسجيلات الوقت
        time_entry_id INTEGER REFERENCES time_entries(id),
        case_id INTEGER REFERENCES issues(id),
        
        -- معلومات إضافية
        item_type VARCHAR(50) DEFAULT 'service', -- service, expense, product
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 7. جدول حسابات العملاء للبوابة
    await client.query(`
      CREATE TABLE IF NOT EXISTS client_portal_accounts (
        id SERIAL PRIMARY KEY,
        client_id INTEGER REFERENCES clients(id) UNIQUE NOT NULL,
        
        -- معلومات تسجيل الدخول
        username VARCHAR(100) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        
        -- الحالة والصلاحيات
        is_active BOOLEAN DEFAULT true,
        is_verified BOOLEAN DEFAULT false,
        verification_token VARCHAR(255),
        reset_token VARCHAR(255),
        reset_token_expires TIMESTAMP,
        
        -- إعدادات البوابة
        language VARCHAR(10) DEFAULT 'ar',
        timezone VARCHAR(50) DEFAULT 'Asia/Riyadh',
        notification_preferences JSONB DEFAULT '{}',
        
        -- معلومات الدخول
        last_login TIMESTAMP,
        login_attempts INTEGER DEFAULT 0,
        locked_until TIMESTAMP,
        
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 8. جدول إشعارات العملاء
    await client.query(`
      CREATE TABLE IF NOT EXISTS client_notifications (
        id SERIAL PRIMARY KEY,
        client_id INTEGER REFERENCES clients(id) NOT NULL,
        
        -- محتوى الإشعار
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type VARCHAR(50) DEFAULT 'info', -- info, warning, success, error
        
        -- ربط بالقضايا أو الوثائق
        case_id INTEGER REFERENCES issues(id),
        document_id INTEGER REFERENCES documents(id),
        
        -- حالة الإشعار
        is_read BOOLEAN DEFAULT false,
        read_at TIMESTAMP,
        
        -- معلومات الإرسال
        sent_via VARCHAR(50) DEFAULT 'portal', -- portal, email, sms
        sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 9. جدول طلبات العملاء
    await client.query(`
      CREATE TABLE IF NOT EXISTS client_requests (
        id SERIAL PRIMARY KEY,
        client_id INTEGER REFERENCES clients(id) NOT NULL,
        case_id INTEGER REFERENCES issues(id),
        
        -- تفاصيل الطلب
        request_type VARCHAR(100) NOT NULL, -- document_request, meeting_request, status_inquiry
        title VARCHAR(255) NOT NULL,
        description TEXT,
        priority VARCHAR(20) DEFAULT 'medium', -- low, medium, high, urgent
        
        -- الحالة والمعالجة
        status VARCHAR(50) DEFAULT 'pending', -- pending, in_progress, completed, rejected
        assigned_to INTEGER REFERENCES employees(id),
        response TEXT,
        
        -- التواريخ
        due_date DATE,
        completed_date DATE,
        
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 10. جدول جلسات العملاء
    await client.query(`
      CREATE TABLE IF NOT EXISTS client_sessions (
        id SERIAL PRIMARY KEY,
        client_id INTEGER REFERENCES clients(id) NOT NULL,
        session_token VARCHAR(255) UNIQUE NOT NULL,
        ip_address INET,
        user_agent TEXT,
        expires_at TIMESTAMP NOT NULL,
        is_active BOOLEAN DEFAULT true,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إنشاء الفهارس لتحسين الأداء
    console.log('📊 إنشاء الفهارس...');

    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_documents_case_id ON documents(case_id);
      CREATE INDEX IF NOT EXISTS idx_documents_client_id ON documents(client_id);
      CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category);
      CREATE INDEX IF NOT EXISTS idx_documents_tags ON documents USING GIN(tags);
      CREATE INDEX IF NOT EXISTS idx_documents_content_text ON documents USING GIN(to_tsvector('arabic', content_text));
      
      CREATE INDEX IF NOT EXISTS idx_time_entries_case_id ON time_entries(case_id);
      CREATE INDEX IF NOT EXISTS idx_time_entries_employee_id ON time_entries(employee_id);
      CREATE INDEX IF NOT EXISTS idx_time_entries_date ON time_entries(start_time);
      
      CREATE INDEX IF NOT EXISTS idx_invoices_client_id ON invoices(client_id);
      CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
      CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(invoice_date);
      
      CREATE INDEX IF NOT EXISTS idx_client_notifications_client_id ON client_notifications(client_id);
      CREATE INDEX IF NOT EXISTS idx_client_notifications_read ON client_notifications(is_read);
    `);

    // إدراج بيانات تجريبية
    console.log('📝 إدراج بيانات تجريبية...');

    // إدراج فئات الوثائق الأساسية
    await client.query(`
      INSERT INTO documents (title, description, file_name, file_path, category, tags, content_text, uploaded_by)
      VALUES 
      ('عقد توكيل نموذجي', 'نموذج عقد توكيل قانوني', 'power_of_attorney_template.pdf', '/documents/templates/power_of_attorney_template.pdf', 'template', ARRAY['عقد', 'توكيل', 'نموذج'], 'نموذج عقد توكيل قانوني للاستخدام في القضايا المختلفة', 1),
      ('دليل الإجراءات القانونية', 'دليل شامل للإجراءات القانونية', 'legal_procedures_guide.pdf', '/documents/guides/legal_procedures_guide.pdf', 'guide', ARRAY['دليل', 'إجراءات', 'قانون'], 'دليل شامل يوضح الإجراءات القانونية المختلفة', 1)
      ON CONFLICT DO NOTHING
    `);

    // إدراج بيانات تجريبية للعملاء والقضايا أولاً
    await client.query(`
      INSERT INTO clients (name, phone, email, address, id_number) 
      VALUES ('عميل تجريبي', '0501234567', '<EMAIL>', 'الرياض', '1234567890')
      ON CONFLICT (id_number) DO NOTHING
    `)

    await client.query(`
      INSERT INTO employees (name, position, phone, email, id_number, salary) 
      VALUES ('محامي تجريبي', 'محامي', '0509876543', '<EMAIL>', '0987654321', 10000.00)
      ON CONFLICT (id_number) DO NOTHING
    `)

    await client.query(`
      INSERT INTO issue_types (name, description, color) 
      VALUES ('قضية تجريبية', 'نوع قضية للاختبار', '#3B82F6')
      ON CONFLICT DO NOTHING
    `)

    await client.query(`
      INSERT INTO issues (case_number, title, description, client_id, issue_type_id, status, amount) 
      VALUES ('CASE-001', 'قضية تجريبية', 'قضية للاختبار', 1, 1, 'active', 50000.00)
      ON CONFLICT (case_number) DO NOTHING
    `)

    // إدراج أسعار الخدمات الأساسية
    await client.query(`
      INSERT INTO time_entries (case_id, client_id, employee_id, start_time, end_time, duration_minutes, task_description, task_category, hourly_rate, billable_amount, is_billable)
      VALUES 
      (1, 1, 1, CURRENT_TIMESTAMP - INTERVAL '2 hours', CURRENT_TIMESTAMP - INTERVAL '1 hour', 60, 'مراجعة ملف القضية وإعداد المرافعة', 'documentation', 200.00, 200.00, true),
      (1, 1, 1, CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day' + INTERVAL '30 minutes', 30, 'اجتماع مع الموكل', 'meeting', 200.00, 100.00, true)
      ON CONFLICT DO NOTHING
    `);

    console.log('✅ تم إنشاء جميع جداول الميزات المتقدمة بنجاح!');
    console.log('📋 الجداول المُنشأة:');
    console.log('   - documents (إدارة الوثائق)');
    console.log('   - document_versions (إصدارات الوثائق)');
    console.log('   - document_shares (مشاركة الوثائق)');
    console.log('   - time_entries (تسجيل الوقت)');
    console.log('   - invoices (الفواتير)');
    console.log('   - invoice_items (عناصر الفاتورة)');
    console.log('   - client_portal_accounts (حسابات بوابة العملاء)');
    console.log('   - client_notifications (إشعارات العملاء)');
    console.log('   - client_requests (طلبات العملاء)');
    console.log('   - client_sessions (جلسات العملاء)');

  } catch (error) {
    console.error('❌ خطأ في إنشاء الجداول:', error);
    throw error;
  } finally {
    client.release();
  }
}

// تشغيل الدالة
createAdvancedFeaturesTables()
  .then(() => {
    console.log('🎉 تم الانتهاء من إعداد قاعدة البيانات للميزات المتقدمة!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 فشل في إعداد قاعدة البيانات:', error);
    process.exit(1);
  });