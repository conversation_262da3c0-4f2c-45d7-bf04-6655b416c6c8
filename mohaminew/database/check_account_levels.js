const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function checkAccountLevels() {
  try {
    console.log('🔍 فحص مستويات دليل الحسابات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إحصائيات المستويات الموجودة
    console.log('\n📊 إحصائيات المستويات:');
    const levelStats = await client.query(`
      SELECT
        account_level,
        COUNT(*) as account_count,
        COUNT(CASE WHEN is_main_account = true THEN 1 END) as main_accounts,
        COUNT(CASE WHEN is_sub_account = true THEN 1 END) as sub_accounts
      FROM chart_of_accounts
      GROUP BY account_level
      ORDER BY account_level
    `);

    if (levelStats.rows.length > 0) {
      console.log('   المستوى | عدد الحسابات | حسابات رئيسية | حسابات فرعية');
      console.log('   -------|-------------|-------------|-------------');
      levelStats.rows.forEach(level => {
        console.log(`   ${level.account_level.toString().padStart(6)} | ${level.account_count.toString().padStart(11)} | ${level.main_accounts.toString().padStart(11)} | ${level.sub_accounts.toString().padStart(11)}`);
      });

      const maxLevel = Math.max(...levelStats.rows.map(row => row.account_level));
      console.log(`\n   📏 أقصى مستوى موجود: ${maxLevel}`);
      console.log(`   📈 إجمالي المستويات: ${levelStats.rows.length}`);
    } else {
      console.log('   ❌ لا توجد حسابات في دليل الحسابات');
    }

    // 2. عرض الحسابات حسب المستوى
    console.log('\n🌳 هيكل دليل الحسابات:');
    const accountsHierarchy = await client.query(`
      SELECT
        account_level,
        account_code,
        account_name,
        parent_id,
        (SELECT account_name FROM chart_of_accounts p WHERE p.id = c.parent_id) as parent_name
      FROM chart_of_accounts c
      ORDER BY account_level, account_code
    `);

    if (accountsHierarchy.rows.length > 0) {
      let currentLevel = 0;
      accountsHierarchy.rows.forEach(account => {
        if (account.account_level !== currentLevel) {
          currentLevel = account.account_level;
          console.log(`\n   📁 المستوى ${currentLevel}:`);
        }
        const indent = '   ' + '  '.repeat(account.account_level - 1);
        const parentInfo = account.parent_name ? ` (تحت: ${account.parent_name})` : '';
        console.log(`${indent}├── ${account.account_code}: ${account.account_name}${parentInfo}`);
      });
    }

    // 3. تحليل عمق الشجرة
    console.log('\n🔍 تحليل عمق الشجرة:');
    const depthAnalysis = await client.query(`
      SELECT
        MAX(account_level) as max_depth,
        MIN(account_level) as min_depth,
        AVG(account_level::numeric) as avg_depth,
        COUNT(DISTINCT account_level) as total_levels
      FROM chart_of_accounts
    `);

    if (depthAnalysis.rows.length > 0) {
      const analysis = depthAnalysis.rows[0];
      console.log(`   📏 أقصى عمق: ${analysis.max_depth} مستوى`);
      console.log(`   📐 أقل عمق: ${analysis.min_depth} مستوى`);
      console.log(`   📊 متوسط العمق: ${parseFloat(analysis.avg_depth).toFixed(2)} مستوى`);
      console.log(`   📈 إجمالي المستويات المستخدمة: ${analysis.total_levels}`);
    }

    // 4. الحسابات المربوطة بجداول أخرى
    console.log('\n🔗 الحسابات المربوطة:');
    const linkedAccounts = await client.query(`
      SELECT
        account_level,
        account_code,
        account_name,
        linked_table,
        auto_create_sub_accounts,
        (
          SELECT COUNT(*)
          FROM account_sub_links asl
          WHERE asl.main_account_id = c.id
        ) as sub_links_count
      FROM chart_of_accounts c
      WHERE linked_table IS NOT NULL
      ORDER BY account_level, account_code
    `);

    if (linkedAccounts.rows.length > 0) {
      linkedAccounts.rows.forEach(account => {
        console.log(`   📎 ${account.account_code}: ${account.account_name}`);
        console.log(`      └── مربوط بجدول: ${account.linked_table}`);
        console.log(`      └── إنشاء تلقائي: ${account.auto_create_sub_accounts ? 'نعم' : 'لا'}`);
        console.log(`      └── روابط فرعية: ${account.sub_links_count}`);
        console.log(`      └── المستوى: ${account.account_level}`);
      });
    } else {
      console.log('   ❌ لا توجد حسابات مربوطة');
    }

    // 5. توصيات للمستويات
    console.log('\n💡 توصيات المستويات:');
    const maxCurrentLevel = levelStats.rows.length > 0 ?
      Math.max(...levelStats.rows.map(row => row.account_level)) : 0;

    console.log(`   📋 المستويات المُوصى بها للنظام المحاسبي:`);
    console.log(`   ├── المستوى 1: الحسابات الرئيسية (الأصول، الخصوم، حقوق الملكية، الإيرادات، المصروفات)`);
    console.log(`   ├── المستوى 2: التصنيفات الفرعية (الأصول المتداولة، الخصوم المتداولة، إلخ)`);
    console.log(`   ├── المستوى 3: الحسابات التفصيلية (النقدية، البنوك، الموكلين، إلخ)`);
    console.log(`   ├── المستوى 4: الحسابات الفرعية التلقائية (حسابات العملاء الفردية)`);
    console.log(`   └── المستوى 5: حسابات تفصيلية إضافية (حسب الحاجة)`);

    console.log(`\n   📊 الوضع الحالي:`);
    console.log(`   ├── أقصى مستوى مستخدم: ${maxCurrentLevel}`);
    console.log(`   ├── عدد المستويات النشطة: ${levelStats.rows.length}`);

    if (maxCurrentLevel <= 3) {
      console.log(`   ✅ النظام يستخدم عدد مناسب من المستويات`);
    } else if (maxCurrentLevel <= 5) {
      console.log(`   ⚠️ النظام يستخدم مستويات متقدمة - مناسب للأنظمة المعقدة`);
    } else {
      console.log(`   ❌ النظام يستخدم مستويات كثيرة جداً - قد يحتاج إعادة تنظيم`);
    }

    console.log('\n✅ تم فحص مستويات دليل الحسابات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في فحص المستويات:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  checkAccountLevels()
    .then(() => {
      console.log('🎉 تم إنجاز فحص المستويات بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في فحص المستويات:', error);
      process.exit(1);
    });
}

module.exports = { checkAccountLevels };
