// تحديث وإضافة صفحات التنقل في النظام
const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function updateNavigationPages() {
  try {
    await client.connect();
    console.log('🔗 تم الاتصال بقاعدة البيانات');

    // حذف البيانات القديمة
    await client.query('DELETE FROM navigation_pages');
    console.log('🗑️ تم حذف البيانات القديمة');

    // إضافة جميع صفحات النظام
    const pages = [
      // الصفحات الرئيسية
      {
        title: 'لوحة التحكم',
        url: '/',
        description: 'الصفحة الرئيسية للنظام وإحصائيات عامة',
        category: 'رئيسي',
        keywords: 'لوحة,تحكم,رئيسي,dashboard,الرئيسية,إحصائيات'
      },

      // إدارة البيانات الأساسية
      {
        title: 'بيانات الشركة',
        url: '/company',
        description: 'إدارة بيانات الشركة والمعلومات الأساسية',
        category: 'إدارة',
        keywords: 'شركة,company,بيانات,معلومات,أساسية'
      },
      {
        title: 'الموكلين',
        url: '/clients',
        description: 'إدارة بيانات العملاء والموكلين',
        category: 'إدارة',
        keywords: 'عملاء,موكلين,clients,زبائن,عميل'
      },
      {
        title: 'الموظفين',
        url: '/employees',
        description: 'إدارة بيانات الموظفين والعاملين',
        category: 'إدارة',
        keywords: 'موظفين,employees,عمال,موظف,عامل'
      },
      {
        title: 'المستخدمين',
        url: '/users',
        description: 'إدارة مستخدمي النظام والصلاحيات',
        category: 'إدارة',
        keywords: 'مستخدمين,users,صلاحيات,مستخدم'
      },

      // إدارة القضايا
      {
        title: 'أنواع القضايا',
        url: '/issue-types',
        description: 'إدارة أنواع القضايا والدعاوى',
        category: 'قضايا',
        keywords: 'أنواع,قضايا,types,نوع,قضية'
      },
      {
        title: 'القضايا',
        url: '/issues',
        description: 'إدارة القضايا والدعاوى القانونية',
        category: 'قضايا',
        keywords: 'قضايا,دعاوى,issues,cases,قضية,دعوى'
      },
      {
        title: 'المتابعات',
        url: '/follows',
        description: 'متابعة القضايا والمهام والجلسات',
        category: 'قضايا',
        keywords: 'متابعات,follows,مهام,جلسات,متابعة'
      },

      // النظام المحاسبي
      {
        title: 'النظام المحاسبي',
        url: '/accounting',
        description: 'الصفحة الرئيسية للنظام المحاسبي',
        category: 'محاسبة',
        keywords: 'محاسبة,accounting,نظام,محاسبي'
      },
      {
        title: 'دليل الحسابات',
        url: '/accounting/chart-of-accounts',
        description: 'دليل الحسابات المحاسبي (4 مستويات)',
        category: 'محاسبة',
        keywords: 'دليل,حسابات,chart,accounts,محاسبي'
      },
      {
        title: 'سندات الصرف',
        url: '/accounting/payment-vouchers',
        description: 'إدارة سندات الصرف والمدفوعات',
        category: 'محاسبة',
        keywords: 'سندات,صرف,payment,vouchers,مدفوعات,سند'
      },
      {
        title: 'سندات القبض',
        url: '/accounting/receipt-vouchers',
        description: 'إدارة سندات القبض والمقبوضات',
        category: 'محاسبة',
        keywords: 'سندات,قبض,receipt,vouchers,مقبوضات,سند'
      },
      {
        title: 'القيود اليومية',
        url: '/accounting/journal-entries',
        description: 'إدارة القيود المحاسبية اليومية',
        category: 'محاسبة',
        keywords: 'قيود,يومية,journal,entries,محاسبية,قيد'
      },
      {
        title: 'الأرصدة الافتتاحية',
        url: '/accounting/opening-balances',
        description: 'إدارة الأرصدة الافتتاحية للحسابات',
        category: 'محاسبة',
        keywords: 'أرصدة,افتتاحية,opening,balances,رصيد'
      },

      // التقارير المحاسبية
      {
        title: 'التقارير المحاسبية',
        url: '/accounting/reports',
        description: 'جميع التقارير المحاسبية والمالية',
        category: 'تقارير',
        keywords: 'تقارير,محاسبية,reports,مالية,تقرير'
      },
      {
        title: 'كشف حساب',
        url: '/accounting/reports/account-statement',
        description: 'كشف حساب تفصيلي للحسابات',
        category: 'تقارير',
        keywords: 'كشف,حساب,statement,تفصيلي'
      },
      {
        title: 'ميزان المراجعة',
        url: '/accounting/reports/trial-balance',
        description: 'ميزان المراجعة للحسابات',
        category: 'تقارير',
        keywords: 'ميزان,مراجعة,trial,balance'
      },
      {
        title: 'قائمة الدخل',
        url: '/accounting/reports/income-statement',
        description: 'قائمة الدخل والأرباح والخسائر',
        category: 'تقارير',
        keywords: 'قائمة,دخل,income,statement,أرباح,خسائر'
      },
      {
        title: 'الميزانية العمومية',
        url: '/accounting/reports/balance-sheet',
        description: 'الميزانية العمومية للشركة',
        category: 'تقارير',
        keywords: 'ميزانية,عمومية,balance,sheet'
      },

      // تقارير القضايا
      {
        title: 'تقارير القضايا',
        url: '/case-reports',
        description: 'تقارير القضايا والدعاوى',
        category: 'تقارير',
        keywords: 'تقارير,قضايا,cases,دعاوى'
      },
      {
        title: 'التقارير المالية',
        url: '/financial-reports',
        description: 'التقارير المالية للقضايا',
        category: 'تقارير',
        keywords: 'تقارير,مالية,financial,قضايا'
      },
      {
        title: 'تقارير الموظفين',
        url: '/employee-reports',
        description: 'تقارير أداء الموظفين',
        category: 'تقارير',
        keywords: 'تقارير,موظفين,employees,أداء'
      },

      // الحركات المالية
      {
        title: 'الحركات المالية',
        url: '/movements',
        description: 'إدارة الحركات المالية للقضايا',
        category: 'مالية',
        keywords: 'حركات,مالية,movements,مالي'
      },

      // الإعدادات العامة
      {
        title: 'المحافظات',
        url: '/governorates',
        description: 'إدارة المحافظات والمناطق',
        category: 'إعدادات',
        keywords: 'محافظات,governorates,مناطق,محافظة'
      },
      {
        title: 'الفروع',
        url: '/branches',
        description: 'إدارة فروع الشركة',
        category: 'إعدادات',
        keywords: 'فروع,branches,فرع'
      },
      {
        title: 'المحاكم',
        url: '/courts',
        description: 'إدارة المحاكم والجهات القضائية',
        category: 'إعدادات',
        keywords: 'محاكم,courts,قضائية,محكمة'
      },
      {
        title: 'النسب المالية',
        url: '/percentages',
        description: 'إدارة النسب المالية للقضايا',
        category: 'إعدادات',
        keywords: 'نسب,مالية,percentages,نسبة'
      },
      {
        title: 'الخدمات',
        url: '/services',
        description: 'إدارة خدمات المكتب القانوني',
        category: 'إعدادات',
        keywords: 'خدمات,services,خدمة'
      },

      // إعدادات النظام المحاسبي
      {
        title: 'مراكز التكلفة',
        url: '/settings/cost-centers',
        description: 'إدارة مراكز التكلفة المحاسبية',
        category: 'إعدادات',
        keywords: 'مراكز,تكلفة,cost,centers,مركز'
      },
      {
        title: 'العملات',
        url: '/settings/currencies',
        description: 'إدارة العملات وأسعار الصرف',
        category: 'إعدادات',
        keywords: 'عملات,currencies,صرف,عملة'
      },
      {
        title: 'طرق الدفع',
        url: '/settings/payment-methods',
        description: 'إدارة طرق الدفع والسداد',
        category: 'إعدادات',
        keywords: 'طرق,دفع,payment,methods,سداد'
      },
      {
        title: 'الإعلانات',
        url: '/settings/announcements',
        description: 'إدارة إعلانات النظام',
        category: 'إعدادات',
        keywords: 'إعلانات,announcements,إعلان'
      },

      // صفحات إضافية
      {
        title: 'النسب المالية المتقدمة',
        url: '/lineages',
        description: 'إدارة النسب المالية المتقدمة',
        category: 'مالية',
        keywords: 'نسب,مالية,lineages,متقدمة'
      },
      {
        title: 'القيود اليومية الجديدة',
        url: '/journal-entries-new',
        description: 'واجهة جديدة للقيود اليومية',
        category: 'محاسبة',
        keywords: 'قيود,يومية,جديدة,journal,entries'
      }
    ];

    // إدراج البيانات الجديدة
    for (const page of pages) {
      await client.query(`
        INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords)
        VALUES ($1, $2, $3, $4, $5)
      `, [page.title, page.url, page.description, page.category, page.keywords]);
    }

    console.log(`✅ تم إضافة ${pages.length} صفحة بنجاح`);

    // عرض الصفحات المضافة
    const result = await client.query('SELECT * FROM navigation_pages ORDER BY category, page_title');
    console.log('\n📋 الصفحات المضافة:');
    result.rows.forEach(row => {
      console.log(`- ${row.page_title} (${row.category}): ${row.page_url}`);
    });

  } catch (error) {
    console.error('❌ خطأ في تحديث صفحات التنقل:', error);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل التحديث
updateNavigationPages();
