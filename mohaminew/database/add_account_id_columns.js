const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function addAccountIdColumns() {
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. إضافة عمود account_id لجدول العملاء (clients)
    console.log('🔄 جاري إضافة عمود account_id لجدول العملاء...');

    await client.query(`
      ALTER TABLE clients
      ADD COLUMN IF NOT EXISTS account_id INTEGER REFERENCES account_linking_settings(id) ON DELETE SET NULL
    `);

    // إضافة فهرس للأداء
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_clients_account_id ON clients(account_id)
    `);

    console.log('✅ تم إضافة عمود account_id لجدول العملاء');

    // 2. إضافة عمود account_id لجدول الموظفين (employees)
    console.log('🔄 جاري إضافة عمود account_id لجدول الموظفين...');

    await client.query(`
      ALTER TABLE employees
      ADD COLUMN IF NOT EXISTS account_id INTEGER REFERENCES account_linking_settings(id) ON DELETE SET NULL
    `);

    // إضافة فهرس للأداء
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_employees_account_id ON employees(account_id)
    `);

    console.log('✅ تم إضافة عمود account_id لجدول الموظفين');

    // 3. ربط العملاء الموجودين بإعدادات الربط المناسبة
    console.log('🔄 جاري ربط العملاء الموجودين بإعدادات الربط...');

    // البحث عن إعدادات ربط العملاء
    const clientsLinkingSettings = await client.query(`
      SELECT id FROM account_linking_settings
      WHERE table_name = 'clients'
      LIMIT 1
    `);

    if (clientsLinkingSettings.rows.length > 0) {
      const settingsId = clientsLinkingSettings.rows[0].id;

      // تحديث جميع العملاء الموجودين
      const updateResult = await client.query(`
        UPDATE clients
        SET account_id = $1
        WHERE account_id IS NULL
      `, [settingsId]);

      console.log(`   ✅ تم ربط ${updateResult.rowCount} عميل بإعدادات الربط`);
    } else {
      console.log('   ⚠️ لم يتم العثور على إعدادات ربط للعملاء');
    }

    // 4. ربط الموظفين الموجودين بإعدادات الربط المناسبة
    console.log('🔄 جاري ربط الموظفين الموجودين بإعدادات الربط...');

    // البحث عن إعدادات ربط الموظفين
    const employeesLinkingSettings = await client.query(`
      SELECT id FROM account_linking_settings
      WHERE table_name = 'employees'
      LIMIT 1
    `);

    if (employeesLinkingSettings.rows.length > 0) {
      const settingsId = employeesLinkingSettings.rows[0].id;

      // تحديث جميع الموظفين الموجودين
      const updateResult = await client.query(`
        UPDATE employees
        SET account_id = $1
        WHERE account_id IS NULL
      `, [settingsId]);

      console.log(`   ✅ تم ربط ${updateResult.rowCount} موظف بإعدادات الربط`);
    } else {
      console.log('   ⚠️ لم يتم العثور على إعدادات ربط للموظفين');
    }

    // 5. إنشاء دالة لربط السجلات الجديدة تلقائياً
    console.log('🔄 جاري إنشاء دوال الربط التلقائي...');

    // دالة لربط العملاء الجدد تلقائياً
    await client.query(`
      CREATE OR REPLACE FUNCTION auto_link_client_account()
      RETURNS TRIGGER AS $$
      DECLARE
        settings_id INTEGER;
      BEGIN
        -- البحث عن إعدادات ربط العملاء
        SELECT id INTO settings_id
        FROM account_linking_settings
        WHERE table_name = 'clients' AND is_enabled = true
        LIMIT 1;

        -- إذا تم العثور على الإعدادات، قم بالربط
        IF settings_id IS NOT NULL THEN
          NEW.account_id = settings_id;
        END IF;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // إنشاء المحفز للعملاء
    await client.query(`
      DROP TRIGGER IF EXISTS trigger_auto_link_client_account ON clients;
      CREATE TRIGGER trigger_auto_link_client_account
        BEFORE INSERT ON clients
        FOR EACH ROW
        EXECUTE FUNCTION auto_link_client_account();
    `);

    // دالة لربط الموظفين الجدد تلقائياً
    await client.query(`
      CREATE OR REPLACE FUNCTION auto_link_employee_account()
      RETURNS TRIGGER AS $$
      DECLARE
        settings_id INTEGER;
      BEGIN
        -- البحث عن إعدادات ربط الموظفين
        SELECT id INTO settings_id
        FROM account_linking_settings
        WHERE table_name = 'employees' AND is_enabled = true
        LIMIT 1;

        -- إذا تم العثور على الإعدادات، قم بالربط
        IF settings_id IS NOT NULL THEN
          NEW.account_id = settings_id;
        END IF;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // إنشاء المحفز للموظفين
    await client.query(`
      DROP TRIGGER IF EXISTS trigger_auto_link_employee_account ON employees;
      CREATE TRIGGER trigger_auto_link_employee_account
        BEFORE INSERT ON employees
        FOR EACH ROW
        EXECUTE FUNCTION auto_link_employee_account();
    `);

    console.log('✅ تم إنشاء دوال ومحفزات الربط التلقائي');

    // 6. عرض ملخص النتائج
    console.log('📊 ملخص العملية:');

    const clientsCount = await client.query(`
      SELECT
        COUNT(*) as total_clients,
        COUNT(account_id) as linked_clients
      FROM clients
    `);

    const employeesCount = await client.query(`
      SELECT
        COUNT(*) as total_employees,
        COUNT(account_id) as linked_employees
      FROM employees
    `);

    const settingsCount = await client.query(`
      SELECT COUNT(*) as total_settings FROM account_linking_settings
    `);

    console.log(`   📋 إجمالي العملاء: ${clientsCount.rows[0].total_clients}`);
    console.log(`   🔗 العملاء المربوطين: ${clientsCount.rows[0].linked_clients}`);
    console.log(`   📋 إجمالي الموظفين: ${employeesCount.rows[0].total_employees}`);
    console.log(`   🔗 الموظفين المربوطين: ${employeesCount.rows[0].linked_employees}`);
    console.log(`   ⚙️ إعدادات الربط المتاحة: ${settingsCount.rows[0].total_settings}`);

    console.log('✅ تم إكمال عملية إضافة أعمدة account_id وربط الجداول بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في العملية:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  addAccountIdColumns()
    .then(() => {
      console.log('🎉 تم إنجاز المهمة بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في تنفيذ المهمة:', error);
      process.exit(1);
    });
}

module.exports = { addAccountIdColumns };
