// إصلاح وإنشاء جدول الأرصدة الافتتاحية
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function fixOpeningBalances() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري الاتصال بقاعدة البيانات...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من الجدول الموجود
    const tableCheck = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'opening_balances'
      ORDER BY ordinal_position
    `);

    if (tableCheck.rows.length > 0) {
      console.log('📋 هيكل جدول الأرصدة الافتتاحية الحالي:');
      tableCheck.rows.forEach(row => {
        console.log(`   - ${row.column_name}: ${row.data_type}`);
      });

      // حذف الجدول وإعادة إنشاؤه
      console.log('🔄 جاري إعادة إنشاء جدول الأرصدة الافتتاحية...');
      await client.query('DROP TABLE IF EXISTS opening_balances CASCADE');
    }

    // إنشاء الجدول بالهيكل الصحيح
    await client.query(`
      CREATE TABLE opening_balances (
        id SERIAL PRIMARY KEY,
        account_name VARCHAR(255) NOT NULL,
        account_code VARCHAR(50) NOT NULL,
        debit_amount DECIMAL(15,2) DEFAULT 0,
        credit_amount DECIMAL(15,2) DEFAULT 0,
        balance_type VARCHAR(20) NOT NULL,
        created_date DATE DEFAULT CURRENT_DATE
      )
    `);
    console.log('✅ تم إنشاء جدول الأرصدة الافتتاحية بنجاح');

    // إدراج البيانات التجريبية
    const openingBalances = [
      { account_name: 'النقدية بالصندوق', account_code: '1001', debit_amount: 50000, credit_amount: 0, balance_type: 'مدين' },
      { account_name: 'البنك الأهلي', account_code: '1002', debit_amount: 150000, credit_amount: 0, balance_type: 'مدين' },
      { account_name: 'العملاء', account_code: '1101', debit_amount: 75000, credit_amount: 0, balance_type: 'مدين' },
      { account_name: 'المخزون', account_code: '1201', debit_amount: 25000, credit_amount: 0, balance_type: 'مدين' },
      { account_name: 'رأس المال', account_code: '3001', debit_amount: 0, credit_amount: 200000, balance_type: 'دائن' },
      { account_name: 'الموردين', account_code: '2001', debit_amount: 0, credit_amount: 50000, balance_type: 'دائن' },
      { account_name: 'مصروفات التأسيس', account_code: '1301', debit_amount: 15000, credit_amount: 0, balance_type: 'مدين' },
      { account_name: 'إيرادات الخدمات', account_code: '4001', debit_amount: 0, credit_amount: 65000, balance_type: 'دائن' }
    ];

    console.log('🔄 جاري إدراج البيانات التجريبية...');
    for (const balance of openingBalances) {
      await client.query(`
        INSERT INTO opening_balances (account_name, account_code, debit_amount, credit_amount, balance_type)
        VALUES ($1, $2, $3, $4, $5)
      `, [balance.account_name, balance.account_code, balance.debit_amount, balance.credit_amount, balance.balance_type]);
    }

    // التحقق من النتائج
    const result = await client.query('SELECT COUNT(*) FROM opening_balances');
    console.log(`✅ تم إدراج ${result.rows[0].count} رصيد افتتاحي`);

    // عرض البيانات
    const data = await client.query('SELECT * FROM opening_balances ORDER BY id');
    console.log('📋 الأرصدة الافتتاحية:');
    data.rows.forEach(row => {
      console.log(`   - ${row.account_name} (${row.account_code}): مدين ${row.debit_amount} - دائن ${row.credit_amount}`);
    });

    console.log('🎉 تم إصلاح جدول الأرصدة الافتتاحية بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح الجدول:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الإصلاح
fixOpeningBalances();
