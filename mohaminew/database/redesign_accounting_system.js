const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function redesignAccountingSystem() {
  try {
    console.log('🔄 بدء إعادة تصميم النظام المحاسبي...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // 1. حذف الجداول المحاسبية القديمة
    console.log('\n🗑️ حذف الجداول المحاسبية القديمة...');

    const oldTables = [
      'financial_transactions',
      'vouchers_master',
      'voucher_details',
      'journal_entries',
      'journal_entry_details',
      'payment_vouchers',
      'receipt_vouchers',
      'account_sub_links',
      'account_linking_settings',
      'chart_of_accounts'
    ];

    for (const table of oldTables) {
      try {
        await client.query(`DROP TABLE IF EXISTS ${table} CASCADE`);
        console.log(`   ✅ تم حذف جدول ${table}`);
      } catch (error) {
        console.log(`   ⚠️ لم يتم العثور على جدول ${table}`);
      }
    }

    // 2. إنشاء دليل الحسابات الجديد (4 مستويات)
    console.log('\n📊 إنشاء دليل الحسابات الجديد...');

    await client.query(`
      CREATE TABLE chart_of_accounts (
        id SERIAL PRIMARY KEY,
        account_code VARCHAR(20) UNIQUE NOT NULL,
        account_name VARCHAR(255) NOT NULL,
        account_name_en VARCHAR(255),

        -- نظام المستويات الأربعة
        level_1_code VARCHAR(2), -- 01, 02, 03, 04, 05
        level_2_code VARCHAR(4), -- 0101, 0102, 0201, etc.
        level_3_code VARCHAR(6), -- 010101, 010102, etc.
        level_4_code VARCHAR(8), -- ********, ********, etc.

        account_level INTEGER NOT NULL CHECK (account_level BETWEEN 1 AND 4),
        parent_id INTEGER REFERENCES chart_of_accounts(id),

        -- نوع الحساب
        account_type VARCHAR(50) NOT NULL, -- أصول، خصوم، حقوق ملكية، إيرادات، مصروفات
        account_nature VARCHAR(20) DEFAULT 'مدين', -- مدين، دائن

        -- إعدادات الحساب
        is_active BOOLEAN DEFAULT TRUE,
        allow_transactions BOOLEAN DEFAULT FALSE, -- فقط المستوى 4 يسمح بالمعاملات

        -- ربط بالجداول الخارجية (للمستوى 4 فقط)
        linked_table VARCHAR(100), -- clients, employees, null
        auto_create_sub_accounts BOOLEAN DEFAULT FALSE,

        -- الأرصدة
        opening_balance DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,

        -- معلومات إضافية
        description TEXT,
        notes TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

        -- فهارس
        CONSTRAINT unique_level_code UNIQUE (account_code),
        CONSTRAINT check_transaction_level CHECK (
          (account_level = 4 AND allow_transactions = TRUE) OR
          (account_level < 4 AND allow_transactions = FALSE)
        )
      );

      CREATE INDEX idx_chart_account_code ON chart_of_accounts(account_code);
      CREATE INDEX idx_chart_account_level ON chart_of_accounts(account_level);
      CREATE INDEX idx_chart_parent_id ON chart_of_accounts(parent_id);
      CREATE INDEX idx_chart_linked_table ON chart_of_accounts(linked_table);
    `);
    console.log('✅ تم إنشاء جدول دليل الحسابات');

    // 3. إنشاء جدول العملات
    console.log('\n💱 إنشاء جدول العملات...');

    await client.query(`
      CREATE TABLE IF NOT EXISTS currencies (
        id SERIAL PRIMARY KEY,
        currency_code VARCHAR(3) UNIQUE NOT NULL, -- USD, EUR, SAR, YER
        currency_name VARCHAR(100) NOT NULL,
        symbol VARCHAR(10),
        exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
        is_base_currency BOOLEAN DEFAULT FALSE,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- إدراج العملات الأساسية إذا لم تكن موجودة
      INSERT INTO currencies (currency_code, currency_name, symbol, exchange_rate, is_base_currency)
      SELECT * FROM (VALUES
        ('YER', 'ريال يمني', 'ر.ي', 1.0000, TRUE),
        ('USD', 'دولار أمريكي', '$', 1500.0000, FALSE),
        ('SAR', 'ريال سعودي', 'ر.س', 400.0000, FALSE),
        ('EUR', 'يورو', '€', 1600.0000, FALSE)
      ) AS v(currency_code, currency_name, symbol, exchange_rate, is_base_currency)
      WHERE NOT EXISTS (SELECT 1 FROM currencies WHERE currency_code = v.currency_code);
    `);
    console.log('✅ تم إنشاء جدول العملات');

    // 4. إنشاء جدول طرق الدفع
    console.log('\n💳 إنشاء جدول طرق الدفع...');

    await client.query(`
      CREATE TABLE IF NOT EXISTS payment_methods (
        id SERIAL PRIMARY KEY,
        method_name VARCHAR(100) NOT NULL,
        method_code VARCHAR(20) UNIQUE NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- إدراج طرق الدفع إذا لم تكن موجودة
      INSERT INTO payment_methods (method_name, method_code, description)
      SELECT * FROM (VALUES
        ('نقداً', 'CASH', 'دفع نقدي مباشر'),
        ('آجل', 'CREDIT', 'دفع آجل أو بالذمة'),
        ('حوالة بنكية', 'TRANSFER', 'حوالة بنكية أو تحويل إلكتروني'),
        ('شيك', 'CHECK', 'دفع بالشيك'),
        ('بطاقة ائتمان', 'CARD', 'دفع بالبطاقة الائتمانية')
      ) AS v(method_name, method_code, description)
      WHERE NOT EXISTS (SELECT 1 FROM payment_methods WHERE method_code = v.method_code);
    `);
    console.log('✅ تم إنشاء جدول طرق الدفع');

    // 5. إنشاء جدول مراكز التكلفة (إذا لم يكن موجوداً)
    console.log('\n🏢 التحقق من جدول مراكز التكلفة...');

    await client.query(`
      CREATE TABLE IF NOT EXISTS cost_centers (
        id SERIAL PRIMARY KEY,
        center_code VARCHAR(20) UNIQUE NOT NULL,
        center_name VARCHAR(255) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- إدراج مراكز تكلفة أساسية إذا لم تكن موجودة
      INSERT INTO cost_centers (center_code, center_name, description)
      SELECT * FROM (VALUES
        ('CC001', 'الإدارة العامة', 'مركز تكلفة الإدارة العامة'),
        ('CC002', 'القضايا المدنية', 'مركز تكلفة القضايا المدنية'),
        ('CC003', 'القضايا التجارية', 'مركز تكلفة القضايا التجارية'),
        ('CC004', 'القضايا الجنائية', 'مركز تكلفة القضايا الجنائية')
      ) AS v(center_code, center_name, description)
      WHERE NOT EXISTS (SELECT 1 FROM cost_centers WHERE center_code = v.center_code);
    `);
    console.log('✅ تم التحقق من جدول مراكز التكلفة');

    console.log('\n✅ تم إنشاء الهيكل الأساسي للنظام المحاسبي الجديد');

  } catch (error) {
    console.error('❌ خطأ في إعادة تصميم النظام المحاسبي:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  redesignAccountingSystem()
    .then(() => {
      console.log('\n🎉 تم إعادة تصميم النظام المحاسبي بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 فشل في إعادة تصميم النظام:', error);
      process.exit(1);
    });
}

module.exports = { redesignAccountingSystem };
