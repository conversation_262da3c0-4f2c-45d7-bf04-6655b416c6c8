const { Client } = require('pg');

// إعدادات الاتصال بقاعدة البيانات
const client = new Client({
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
});

async function calculateMainAccountBalances() {
  try {
    console.log('🧮 حساب أرصدة الحسابات الرئيسية تلقائياً...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // 1. عرض الأرصدة الحالية قبل الحساب
    console.log('\n📊 الأرصدة الحالية قبل الحساب:');
    const currentBalances = await client.query(`
      SELECT 
        account_code,
        account_name,
        account_level,
        is_main_account,
        opening_balance,
        current_balance
      FROM chart_of_accounts
      WHERE is_main_account = TRUE
      ORDER BY account_code
    `);
    
    currentBalances.rows.forEach(account => {
      console.log(`   ${account.account_code}: ${account.account_name} - رصيد افتتاحي: ${account.opening_balance}, رصيد حالي: ${account.current_balance}`);
    });

    // 2. حساب الأرصدة الافتتاحية للحسابات الرئيسية
    console.log('\n🔄 جاري حساب الأرصدة الافتتاحية...');
    
    const mainAccounts = await client.query(`
      SELECT id, account_code, account_name
      FROM chart_of_accounts 
      WHERE is_main_account = TRUE
      ORDER BY account_code
    `);

    for (const mainAccount of mainAccounts.rows) {
      // حساب مجموع الأرصدة الافتتاحية للحسابات الفرعية
      const subAccountsBalance = await client.query(`
        WITH RECURSIVE sub_accounts AS (
          -- الحسابات الفرعية المباشرة
          SELECT id, account_code, account_name, opening_balance, current_balance
          FROM chart_of_accounts 
          WHERE parent_id = $1
          
          UNION ALL
          
          -- الحسابات الفرعية للحسابات الفرعية
          SELECT c.id, c.account_code, c.account_name, c.opening_balance, c.current_balance
          FROM chart_of_accounts c
          INNER JOIN sub_accounts sa ON c.parent_id = sa.id
        )
        SELECT 
          COALESCE(SUM(opening_balance), 0) as total_opening_balance,
          COALESCE(SUM(current_balance), 0) as total_current_balance,
          COUNT(*) as sub_accounts_count
        FROM sub_accounts
      `, [mainAccount.id]);

      const totalOpeningBalance = parseFloat(subAccountsBalance.rows[0].total_opening_balance) || 0;
      const totalCurrentBalance = parseFloat(subAccountsBalance.rows[0].total_current_balance) || 0;
      const subAccountsCount = parseInt(subAccountsBalance.rows[0].sub_accounts_count) || 0;

      // تحديث رصيد الحساب الرئيسي
      await client.query(`
        UPDATE chart_of_accounts 
        SET 
          opening_balance = $1,
          current_balance = $2,
          updated_date = CURRENT_TIMESTAMP
        WHERE id = $3
      `, [totalOpeningBalance, totalCurrentBalance, mainAccount.id]);

      console.log(`   ✅ ${mainAccount.account_code}: ${mainAccount.account_name}`);
      console.log(`      └── ${subAccountsCount} حساب فرعي`);
      console.log(`      └── رصيد افتتاحي: ${totalOpeningBalance.toLocaleString()}`);
      console.log(`      └── رصيد حالي: ${totalCurrentBalance.toLocaleString()}`);
    }

    // 3. عرض الأرصدة بعد الحساب
    console.log('\n📊 الأرصدة بعد الحساب:');
    const updatedBalances = await client.query(`
      SELECT 
        account_code,
        account_name,
        account_level,
        is_main_account,
        opening_balance,
        current_balance,
        (
          SELECT COUNT(*) 
          FROM chart_of_accounts child 
          WHERE child.parent_id = c.id
        ) as direct_children
      FROM chart_of_accounts c
      WHERE is_main_account = TRUE
      ORDER BY account_code
    `);
    
    updatedBalances.rows.forEach(account => {
      console.log(`   ${account.account_code}: ${account.account_name}`);
      console.log(`      └── ${account.direct_children} حساب فرعي مباشر`);
      console.log(`      └── رصيد افتتاحي: ${parseFloat(account.opening_balance).toLocaleString()}`);
      console.log(`      └── رصيد حالي: ${parseFloat(account.current_balance).toLocaleString()}`);
    });

    // 4. إنشاء دالة تلقائية لحساب الأرصدة
    console.log('\n🔧 إنشاء دالة تلقائية لحساب الأرصدة...');
    
    await client.query(`
      CREATE OR REPLACE FUNCTION calculate_main_account_balance(main_account_id INTEGER)
      RETURNS VOID AS $$
      DECLARE
        total_opening DECIMAL(15,2) := 0;
        total_current DECIMAL(15,2) := 0;
      BEGIN
        -- حساب مجموع الأرصدة من جميع الحسابات الفرعية
        WITH RECURSIVE sub_accounts AS (
          -- الحسابات الفرعية المباشرة
          SELECT id, opening_balance, current_balance
          FROM chart_of_accounts 
          WHERE parent_id = main_account_id
          
          UNION ALL
          
          -- الحسابات الفرعية للحسابات الفرعية
          SELECT c.id, c.opening_balance, c.current_balance
          FROM chart_of_accounts c
          INNER JOIN sub_accounts sa ON c.parent_id = sa.id
        )
        SELECT 
          COALESCE(SUM(opening_balance), 0),
          COALESCE(SUM(current_balance), 0)
        INTO total_opening, total_current
        FROM sub_accounts;
        
        -- تحديث رصيد الحساب الرئيسي
        UPDATE chart_of_accounts 
        SET 
          opening_balance = total_opening,
          current_balance = total_current,
          updated_date = CURRENT_TIMESTAMP
        WHERE id = main_account_id;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    console.log('   ✅ تم إنشاء دالة calculate_main_account_balance');

    // 5. إنشاء محفز لحساب الأرصدة تلقائياً عند تغيير الحسابات الفرعية
    await client.query(`
      CREATE OR REPLACE FUNCTION trigger_update_main_account_balance()
      RETURNS TRIGGER AS $$
      DECLARE
        main_acc_id INTEGER;
      BEGIN
        -- العثور على الحساب الرئيسي للحساب المُحدث
        WITH RECURSIVE parent_chain AS (
          SELECT id, parent_id, is_main_account
          FROM chart_of_accounts 
          WHERE id = COALESCE(NEW.id, OLD.id)
          
          UNION ALL
          
          SELECT c.id, c.parent_id, c.is_main_account
          FROM chart_of_accounts c
          INNER JOIN parent_chain pc ON c.id = pc.parent_id
        )
        SELECT id INTO main_acc_id
        FROM parent_chain 
        WHERE is_main_account = TRUE
        LIMIT 1;
        
        -- تحديث رصيد الحساب الرئيسي إذا تم العثور عليه
        IF main_acc_id IS NOT NULL THEN
          PERFORM calculate_main_account_balance(main_acc_id);
        END IF;
        
        RETURN COALESCE(NEW, OLD);
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    // إنشاء المحفزات
    await client.query(`
      DROP TRIGGER IF EXISTS trigger_update_main_balance_on_insert ON chart_of_accounts;
      CREATE TRIGGER trigger_update_main_balance_on_insert
        AFTER INSERT ON chart_of_accounts
        FOR EACH ROW
        EXECUTE FUNCTION trigger_update_main_account_balance();
    `);
    
    await client.query(`
      DROP TRIGGER IF EXISTS trigger_update_main_balance_on_update ON chart_of_accounts;
      CREATE TRIGGER trigger_update_main_balance_on_update
        AFTER UPDATE OF opening_balance, current_balance ON chart_of_accounts
        FOR EACH ROW
        EXECUTE FUNCTION trigger_update_main_account_balance();
    `);
    
    console.log('   ✅ تم إنشاء المحفزات التلقائية');

    // 6. إحصائيات نهائية
    console.log('\n📈 إحصائيات نهائية:');
    const finalStats = await client.query(`
      SELECT 
        'الحسابات الرئيسية' as type,
        COUNT(*) as count,
        SUM(opening_balance) as total_opening,
        SUM(current_balance) as total_current
      FROM chart_of_accounts 
      WHERE is_main_account = TRUE
      
      UNION ALL
      
      SELECT 
        'الحسابات الفرعية' as type,
        COUNT(*) as count,
        SUM(opening_balance) as total_opening,
        SUM(current_balance) as total_current
      FROM chart_of_accounts 
      WHERE is_sub_account = TRUE
    `);
    
    finalStats.rows.forEach(stat => {
      console.log(`   ${stat.type}: ${stat.count} حساب`);
      console.log(`      └── إجمالي الرصيد الافتتاحي: ${parseFloat(stat.total_opening || 0).toLocaleString()}`);
      console.log(`      └── إجمالي الرصيد الحالي: ${parseFloat(stat.total_current || 0).toLocaleString()}`);
    });

    console.log('\n✅ تم حساب أرصدة الحسابات الرئيسية بنجاح!');
    console.log('🔄 الآن ستُحدث الأرصدة تلقائياً عند تغيير الحسابات الفرعية');

  } catch (error) {
    console.error('❌ خطأ في حساب الأرصدة:', error);
    throw error;
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

// تشغيل الدالة
if (require.main === module) {
  calculateMainAccountBalances()
    .then(() => {
      console.log('🎉 تم إنجاز حساب الأرصدة بنجاح!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 فشل في حساب الأرصدة:', error);
      process.exit(1);
    });
}

module.exports = { calculateMainAccountBalances };
