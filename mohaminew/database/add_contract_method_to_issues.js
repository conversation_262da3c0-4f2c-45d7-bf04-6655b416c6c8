// إضافة عمود طريقة التعاقد لجدول القضايا
const { Client } = require('pg');

const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'moham<PERSON>',
  user: 'postgres',
  password: 'yemen123'
};

async function addContractMethodToIssues() {
  const client = new Client(dbConfig);
  
  try {
    console.log('🔄 جاري إضافة عمود طريقة التعاقد لجدول القضايا...');
    await client.connect();
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إضافة عمود طريقة التعاقد
    try {
      await client.query(`
        ALTER TABLE issues 
        ADD COLUMN IF NOT EXISTS contract_method VARCHAR(20) DEFAULT 'بالجلسة'
      `);
      console.log('✅ تم إضافة عمود contract_method');
    } catch (error) {
      console.log('⚠️  عمود contract_method موجود بالفعل');
    }

    // إضافة قيد للتأكد من القيم المسموحة
    try {
      await client.query(`
        ALTER TABLE issues 
        ADD CONSTRAINT check_contract_method 
        CHECK (contract_method IN ('بالجلسة', 'بالعقد'))
      `);
      console.log('✅ تم إضافة قيد contract_method');
    } catch (error) {
      console.log('⚠️  قيد contract_method موجود بالفعل');
    }

    // تحديث القضايا الموجودة بقيم افتراضية
    const updateResult = await client.query(`
      UPDATE issues 
      SET contract_method = 'بالجلسة' 
      WHERE contract_method IS NULL
    `);
    console.log(`✅ تم تحديث ${updateResult.rowCount} قضية بطريقة التعاقد الافتراضية`);

    // التحقق من النتائج
    const issuesCount = await client.query('SELECT COUNT(*) as count FROM issues');
    const contractMethods = await client.query(`
      SELECT 
        contract_method,
        COUNT(*) as count
      FROM issues 
      GROUP BY contract_method
    `);

    console.log('📋 ملخص القضايا:');
    console.log(`   - إجمالي القضايا: ${issuesCount.rows[0].count}`);
    console.log('   - توزيع طرق التعاقد:');
    contractMethods.rows.forEach(row => {
      console.log(`     * ${row.contract_method}: ${row.count} قضية`);
    });

    console.log('🎉 تم إضافة عمود طريقة التعاقد بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إضافة العمود:', error.message);
  } finally {
    await client.end();
    console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
  }
}

addContractMethodToIssues();
