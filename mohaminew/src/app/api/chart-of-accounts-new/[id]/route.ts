import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب حساب محدد مع تفاصيله
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const accountId = parseInt(params.id)

    const result = await query(`
      SELECT 
        *,
        get_account_path(id) as full_path
      FROM chart_of_accounts_new 
      WHERE id = $1
    `, [accountId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    const account = result.rows[0]

    // جلب الحسابات الفرعية إذا كان الحساب رئيسياً
    let children = []
    if (account.account_level < 4) {
      const childrenResult = await query(`
        SELECT 
          id,
          account_code,
          account_name,
          account_level,
          current_balance,
          is_active
        FROM chart_of_accounts_new 
        WHERE CASE 
          WHEN $2 = 1 THEN level_1_id = $1
          WHEN $2 = 2 THEN level_2_id = $1
          WHEN $2 = 3 THEN level_3_id = $1
        END
        AND is_active = true
        ORDER BY account_code
      `, [accountId, account.account_level])
      
      children = childrenResult.rows
    }

    return NextResponse.json({
      success: true,
      data: {
        ...account,
        children
      }
    })
  } catch (error) {
    console.error('Error fetching account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الحساب' },
      { status: 500 }
    )
  }
}

// DELETE - حذف حساب (مع التحقق من الأمان)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const accountId = parseInt(params.id)

    // التحقق من وجود الحساب
    const accountResult = await query(
      'SELECT * FROM chart_of_accounts_new WHERE id = $1',
      [accountId]
    )

    if (accountResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    const account = accountResult.rows[0]

    // منع حذف الحسابات النظام
    if (account.is_system_account) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف حسابات النظام' },
        { status: 400 }
      )
    }

    // التحقق من وجود حسابات فرعية
    const childrenResult = await query(`
      SELECT COUNT(*) as count
      FROM chart_of_accounts_new 
      WHERE CASE 
        WHEN $2 = 1 THEN level_1_id = $1
        WHEN $2 = 2 THEN level_2_id = $1
        WHEN $2 = 3 THEN level_3_id = $1
        ELSE false
      END
      AND is_active = true
    `, [accountId, account.account_level])

    if (parseInt(childrenResult.rows[0].count) > 0) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف حساب يحتوي على حسابات فرعية' },
        { status: 400 }
      )
    }

    // التحقق من وجود معاملات على الحساب (إذا كان فرعياً)
    if (account.allow_transactions) {
      // هنا يمكن إضافة فحص المعاملات من جداول السندات
      // const transactionsResult = await query(
      //   'SELECT COUNT(*) as count FROM voucher_transactions WHERE account_id = $1',
      //   [accountId]
      // )
      // if (parseInt(transactionsResult.rows[0].count) > 0) {
      //   return NextResponse.json(
      //     { success: false, error: 'لا يمكن حذف حساب يحتوي على معاملات' },
      //     { status: 400 }
      //   )
      // }
    }

    // حذف الحساب (أو تعطيله)
    await query(
      'UPDATE chart_of_accounts_new SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [accountId]
    )

    return NextResponse.json({
      success: true,
      message: 'تم حذف الحساب بنجاح'
    })
  } catch (error) {
    console.error('Error deleting account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الحساب' },
      { status: 500 }
    )
  }
}
