import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب القضايا المرتبطة بالمستخدم
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'معرف المستخدم مطلوب' },
        { status: 400 }
      )
    }
    
    // جلب القضايا المرتبطة بالمستخدم من خلال توزيع القضايا
    // أولاً، جلب employee_id للمستخدم
    const userResult = await query(`
      SELECT employee_id FROM users WHERE id = $1
    `, [userId])

    if (userResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    const employeeId = userResult.rows[0].employee_id

    // جلب القضايا المرتبطة بالموظف من خلال توزيع القضايا
    const result = await query(`
      SELECT DISTINCT
        i.id,
        i.case_number,
        i.title,
        i.client_name,
        i.court_name,
        i.issue_type,
        i.status,
        i.amount
      FROM issues i
      JOIN case_distribution cd ON i.id = cd.issue_id
      JOIN service_distributions sd ON cd.id = sd.case_distribution_id
      WHERE sd.lawyer_id = $1
      ORDER BY i.case_number
    `, [employeeId])
    
    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching user issues:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب قضايا المستخدم' },
      { status: 500 }
    )
  }
}
