import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب الجداول المتاحة للربط
export async function GET() {
  try {
    const availableTables = [
      {
        table_name: 'clients',
        display_name: 'الموكلين',
        description: 'ربط الحساب بجدول الموكلين',
        fields: ['id', 'name', 'phone', 'email', 'address']
      },
      {
        table_name: 'employees',
        display_name: 'الموظفين',
        description: 'ربط الحساب بجدول الموظفين',
        fields: ['id', 'name', 'position', 'phone', 'email']
      },
      {
        table_name: 'cost_centers',
        display_name: 'مراكز التكلفة',
        description: 'ربط الحساب بمراكز التكلفة',
        fields: ['id', 'name', 'description']
      }
    ]

    // جلب الحسابات المرتبطة حالياً
    const linkedAccountsResult = await query(`
      SELECT 
        id,
        account_code,
        account_name,
        linked_table
      FROM chart_of_accounts 
      WHERE linked_table IS NOT NULL
      ORDER BY account_code
    `)

    return NextResponse.json({
      success: true,
      data: {
        available_tables: availableTables,
        linked_accounts: linkedAccountsResult.rows
      }
    })
  } catch (error) {
    console.error('Error fetching linkable tables:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الجداول المتاحة' },
      { status: 500 }
    )
  }
}

// POST - ربط حساب بجدول
export async function POST(request: NextRequest) {
  try {
    const { account_id, table_name } = await request.json()

    if (!account_id || !table_name) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب واسم الجدول مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود الحساب
    const accountResult = await query(
      'SELECT * FROM chart_of_accounts WHERE id = $1',
      [account_id]
    )

    if (accountResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من صحة اسم الجدول
    const validTables = ['clients', 'employees', 'cost_centers']
    if (!validTables.includes(table_name)) {
      return NextResponse.json(
        { success: false, error: 'اسم الجدول غير صحيح' },
        { status: 400 }
      )
    }

    // ربط الحساب بالجدول
    const result = await query(`
      UPDATE chart_of_accounts 
      SET 
        linked_table = $2,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [account_id, table_name])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: `تم ربط الحساب بجدول ${table_name} بنجاح`
    })
  } catch (error) {
    console.error('Error linking account to table:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في ربط الحساب بالجدول' },
      { status: 500 }
    )
  }
}

// DELETE - إلغاء ربط حساب بجدول
export async function DELETE(request: NextRequest) {
  try {
    const { account_id } = await request.json()

    if (!account_id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب مطلوب' },
        { status: 400 }
      )
    }

    // إلغاء الربط
    const result = await query(`
      UPDATE chart_of_accounts 
      SET 
        linked_table = NULL,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [account_id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إلغاء ربط الحساب بنجاح'
    })
  } catch (error) {
    console.error('Error unlinking account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إلغاء ربط الحساب' },
      { status: 500 }
    )
  }
}
