import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب الحسابات القديمة مع حالة الترحيل
export async function GET() {
  try {
    // جلب الحسابات من الجدول القديم
    const oldAccounts = await query(`
      SELECT 
        id,
        account_code,
        account_name,
        account_type,
        parent_id,
        is_active,
        created_date
      FROM chart_of_accounts
      WHERE is_active = true
      ORDER BY account_code
    `)

    // فحص أي من الحسابات تم ترحيلها بالفعل
    const migratedAccounts = await query(`
      SELECT account_code
      FROM chart_of_accounts_new
    `)

    const migratedCodes = new Set(migratedAccounts.rows.map(acc => acc.account_code))

    // إضافة معلومات الترحيل لكل حساب
    const accountsWithMigrationStatus = oldAccounts.rows.map(account => ({
      ...account,
      is_migrated: migratedCodes.has(account.account_code),
      suggested_level: suggestAccountLevel(account),
      suggested_type: mapAccountType(account.account_type),
      suggested_balance: account.account_type === 'أصول' || account.account_type === 'مصروفات' ? 'مدين' : 'دائن'
    }))

    return NextResponse.json({
      success: true,
      data: accountsWithMigrationStatus
    })
  } catch (error) {
    console.error('Error fetching old accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الحسابات القديمة' },
      { status: 500 }
    )
  }
}

// POST - ترحيل حساب واحد أو مجموعة حسابات
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { accounts } = body // مصفوفة من الحسابات للترحيل

    if (!accounts || !Array.isArray(accounts)) {
      return NextResponse.json(
        { success: false, error: 'بيانات الحسابات مطلوبة' },
        { status: 400 }
      )
    }

    const results = []

    for (const accountData of accounts) {
      try {
        const {
          old_id,
          account_code,
          account_name,
          account_level,
          account_type,
          normal_balance,
          level_1_id,
          level_2_id,
          level_3_id,
          opening_balance = 0
        } = accountData

        // التحقق من عدم وجود الحساب مسبقاً
        const existing = await query(
          'SELECT id FROM chart_of_accounts_new WHERE account_code = $1',
          [account_code]
        )

        if (existing.rows.length > 0) {
          results.push({
            account_code,
            status: 'skipped',
            message: 'الحساب موجود مسبقاً'
          })
          continue
        }

        // إدراج الحساب في الجدول الجديد
        const allowTransactions = account_level === 4
        
        await query(`
          INSERT INTO chart_of_accounts_new (
            account_code, account_name, account_level, account_type, normal_balance,
            level_1_id, level_2_id, level_3_id,
            opening_balance, current_balance, allow_transactions,
            is_active, description
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $9, $10, true, $11)
        `, [
          account_code, account_name, account_level, account_type, normal_balance,
          level_1_id, level_2_id, level_3_id,
          opening_balance, allowTransactions,
          `تم ترحيله من النظام القديم - ID: ${old_id}`
        ])

        results.push({
          account_code,
          status: 'success',
          message: 'تم الترحيل بنجاح'
        })

      } catch (error) {
        console.error(`Error migrating account ${accountData.account_code}:`, error)
        results.push({
          account_code: accountData.account_code,
          status: 'error',
          message: error.message
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      message: `تم ترحيل ${results.filter(r => r.status === 'success').length} حساب من أصل ${accounts.length}`
    })
  } catch (error) {
    console.error('Error migrating accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في ترحيل الحسابات' },
      { status: 500 }
    )
  }
}

// دالة لاقتراح مستوى الحساب بناءً على رقم الحساب
function suggestAccountLevel(account: any): number {
  const code = account.account_code
  
  // إذا كان الحساب له parent_id، فهو على الأقل مستوى 2
  if (account.parent_id) {
    // حساب طول رقم الحساب لتحديد المستوى
    if (code.length <= 2) return 2
    if (code.length <= 4) return 3
    return 4
  }
  
  // إذا لم يكن له parent_id، فهو مستوى 1 أو 2
  if (code.length <= 2) return 1
  return 2
}

// دالة لتحويل نوع الحساب من النظام القديم للجديد
function mapAccountType(oldType: string): string {
  const typeMapping: { [key: string]: string } = {
    'أصول': 'أصول',
    'خصوم': 'خصوم', 
    'حقوق ملكية': 'حقوق ملكية',
    'إيرادات': 'إيرادات',
    'مصروفات': 'مصروفات',
    'مصروف': 'مصروفات',
    'إيراد': 'إيرادات'
  }
  
  return typeMapping[oldType] || 'أصول'
}
