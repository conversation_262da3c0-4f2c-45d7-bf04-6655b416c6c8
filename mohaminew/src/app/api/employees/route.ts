import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الموظفين مع الربط
export async function GET(request: NextRequest) {
  
  /*
  const authHeader = request.headers.get('authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return NextResponse.json(
      { success: false, error: 'رمز المصادقة مطلوب' },
      { status: 401 }
    )
  }
  */
  try {
    const result = await query(`
      SELECT
        e.*,
        -- الحساب الأب (حسابات الموظفين)
        parent_coa.account_name as parent_account_name,
        parent_coa.account_code as parent_account_code,
        parent_coa.current_balance as parent_account_balance,
        -- الحساب الفردي للموظف (إذا وجد)
        individual_coa.id as individual_account_id,
        individual_coa.account_name as individual_account_name,
        individual_coa.account_code as individual_account_code,
        individual_coa.current_balance as individual_balance
      FROM employees e
      LEFT JOIN chart_of_accounts parent_coa ON e.account_id = parent_coa.id
      LEFT JOIN chart_of_accounts individual_coa ON individual_coa.linked_record_id = e.id
        AND individual_coa.original_table = 'employees'
      ORDER BY e.id
    `)

    return NextResponse.json({
      success: true,
      employees: result.rows
    })
  } catch (error) {
    console.error('Error fetching employees:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الموظفين' },
      { status: 500 }
    )
  }
}

// POST - إضافة موظف جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name, position, department_id, branch_id, governorate_id, court_id,
      phone, email, salary, hire_date, status = 'نشط'
    } = body

    if (!name || !position) {
      return NextResponse.json(
        { success: false, error: 'الاسم والمنصب مطلوبان' },
        { status: 400 }
      )
    }

    // إنشاء رقم موظف تلقائي
    const employeeNumberResult = await query('SELECT COUNT(*) + 1 as next_number FROM employees')
    const employee_number = `EMP${employeeNumberResult.rows[0].next_number.toString().padStart(4, '0')}`

    // إدراج الموظف الجديد أولاً
    const result = await query(`
      INSERT INTO employees (
        employee_number, name, position, department_id, branch_id,
        governorate_id, court_id, phone, email, salary, hire_date, status, is_active
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `, [
      employee_number, name, position,
      department_id || null, branch_id || null, governorate_id || null, court_id || null,
      phone, email, salary || null, hire_date, status, true
    ])

    const newEmployee = result.rows[0]

    // إنشاء حساب مرتبط في دليل الحسابات
    const employeeAccountCode = `1202${String(newEmployee.id).padStart(4, '0')}`
    const accountResult = await query(`
      INSERT INTO chart_of_accounts (
        account_code, account_name, account_level, parent_id, account_type, 
        account_nature, allow_transactions, is_linked_record, original_table, 
        linked_record_id, opening_balance, current_balance
      ) VALUES (
        $1, $2, 4, 
        (SELECT id FROM chart_of_accounts WHERE account_code = '1202'), 
        'أصول', 'مدين', true, true, 'employees', $3, 0, 0
      ) RETURNING id
    `, [employeeAccountCode, `حساب الموظف: ${name}`, newEmployee.id])

    // تحديث الموظف بمعرف الحساب
    await query(`
      UPDATE employees SET account_id = $1, account_code = $2 WHERE id = $3
    `, [accountResult.rows[0].id, employeeAccountCode, newEmployee.id])

    // تحديث بيانات الموظف مع معرف الحساب
    newEmployee.account_id = accountResult.rows[0].id
    newEmployee.account_code = employeeAccountCode

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الموظف بنجاح',
      data: newEmployee
    })
  } catch (error) {
    console.error('Error creating employee:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الموظف' },
      { status: 500 }
    )
  }
}

// PUT - تحديث موظف
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, name, position, department, phone, email, address,
      id_number, salary, hire_date, status
    } = body

    if (!id || !name || !id_number) {
      return NextResponse.json(
        { success: false, error: 'المعرف والاسم ورقم الهوية مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE employees
      SET name = $1, position = $2, department = $3, phone = $4,
          email = $5, address = $6, id_number = $7, salary = $8,
          hire_date = $9, status = $10, updated_at = CURRENT_TIMESTAMP
      WHERE id = $11
      RETURNING *
    `, [name, position, department, phone, email, address, id_number, salary, hire_date, status, id])

    const updatedEmployee = result.rows[0]

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الموظف بنجاح',
      data: updatedEmployee
    })
  } catch (error) {
    console.error('Error updating employee:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات الموظف' },
      { status: 500 }
    )
  }
}

// DELETE - حذف موظف
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الموظف مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM employees WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الموظف غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الموظف بنجاح'
    })
  } catch (error) {
    console.error('Error deleting employee:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الموظف' },
      { status: 500 }
    )
  }
}
