import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الفروع من قاعدة البيانات
export async function GET() {
  try {
    // جلب البيانات من قاعدة البيانات مع ربط المحافظات
    const result = await query(`
      SELECT b.*, g.name as governorate_name
      FROM branches b
      LEFT JOIN governorates g ON b.governorate_id = g.id
      ORDER BY b.id
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching branches:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الفروع' },
      { status: 500 }
    )
  }
}

// POST - إضافة فرع جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name, governorate_id, address, phone, manager_name, is_active = true
    } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم الفرع مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO branches (name, governorate_id, address, phone, manager_name, is_active)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [name, governorate_id, address, phone, manager_name, is_active])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الفرع بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating branch:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الفرع' },
      { status: 500 }
    )
  }
}

// PUT - تحديث فرع
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, name, governorate_id, address, phone, manager_name, is_active
    } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم الفرع مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE branches
      SET name = $1, governorate_id = $2, address = $3, phone = $4,
          manager_name = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING *
    `, [name, governorate_id, address, phone, manager_name, is_active, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الفرع بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating branch:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات الفرع' },
      { status: 500 }
    )
  }
}

// DELETE - حذف فرع
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الفرع مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM branches WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الفرع غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الفرع بنجاح'
    })
  } catch (error) {
    console.error('Error deleting branch:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الفرع' },
      { status: 500 }
    )
  }
}
