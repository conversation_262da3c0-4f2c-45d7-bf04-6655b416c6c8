import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الحسابات من دليل الحسابات مع معلومات الربط
export async function GET() {
  try {
    const result = await query(`
      SELECT
        c.*,
        p.account_name as parent_name,
        CASE
          WHEN c.linked_table IS NOT NULL THEN (
            SELECT COUNT(*)
            FROM account_sub_links asl
            WHERE asl.main_account_id = c.id
          )
          ELSE 0
        END as linked_count,
        (
          SELECT COUNT(*)
          FROM chart_of_accounts child
          WHERE child.parent_id = c.id
        ) as children_count
      FROM chart_of_accounts c
      LEFT JOIN chart_of_accounts p ON c.parent_id = p.id
      ORDER BY c.account_code
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الحسابات' },
      { status: 500 }
    )
  }
}

// POST - ربط حساب بجدول
export async function POST(request: NextRequest) {
  try {
    const { account_id, table_name } = await request.json()

    if (!account_id || !table_name) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب واسم الجدول مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود الحساب
    const accountResult = await query(
      'SELECT * FROM chart_of_accounts WHERE id = $1',
      [account_id]
    )

    if (accountResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    // تحديث الحساب لربطه بالجدول
    await query(`
      UPDATE chart_of_accounts
      SET linked_table = $1, auto_create_sub_accounts = true
      WHERE id = $2
    `, [table_name, account_id])

    // إنشاء حسابات فرعية للسجلات الموجودة
    await createSubAccountsForTable(account_id, table_name)

    return NextResponse.json({
      success: true,
      message: 'تم ربط الحساب بنجاح'
    })
  } catch (error) {
    console.error('Error linking account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في ربط الحساب' },
      { status: 500 }
    )
  }
}

// DELETE - إلغاء ربط حساب
export async function DELETE(request: NextRequest) {
  try {
    const { account_id } = await request.json()

    if (!account_id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب مطلوب' },
        { status: 400 }
      )
    }

    // إلغاء ربط الحساب
    await query(`
      UPDATE chart_of_accounts
      SET linked_table = NULL, auto_create_sub_accounts = false
      WHERE id = $1
    `, [account_id])

    // حذف الروابط الفرعية
    await query(
      'DELETE FROM account_sub_links WHERE main_account_id = $1',
      [account_id]
    )

    return NextResponse.json({
      success: true,
      message: 'تم إلغاء ربط الحساب بنجاح'
    })
  } catch (error) {
    console.error('Error unlinking account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إلغاء ربط الحساب' },
      { status: 500 }
    )
  }
}

// دالة مساعدة لإنشاء حسابات فرعية
async function createSubAccountsForTable(accountId: number, tableName: string) {
  try {
    // الحصول على معلومات الحساب الرئيسي
    const accountResult = await query(
      'SELECT account_code, sub_account_prefix FROM chart_of_accounts WHERE id = $1',
      [accountId]
    )

    if (accountResult.rows.length === 0) return

    const { account_code, sub_account_prefix } = accountResult.rows[0]

    // تحديد الاستعلام حسب نوع الجدول
    let dataQuery = ''
    let nameField = 'name'

    switch (tableName) {
      case 'clients':
        dataQuery = 'SELECT id, name FROM clients WHERE is_active = true'
        break
      case 'employees':
        dataQuery = 'SELECT id, name FROM employees WHERE is_active = true'
        break
      case 'courts':
        dataQuery = 'SELECT id, name FROM courts'
        break
      case 'branches':
        dataQuery = 'SELECT id, name FROM branches'
        break
      case 'cost_centers':
        dataQuery = 'SELECT id, name FROM cost_centers'
        break
      case 'issues':
        dataQuery = 'SELECT id, title as name FROM issues'
        nameField = 'title'
        break
      default:
        return
    }

    // جلب البيانات من الجدول المحدد
    const dataResult = await query(dataQuery)

    // إنشاء حساب فرعي لكل سجل
    for (const record of dataResult.rows) {
      const subAccountCode = `${account_code}-${record.id.toString().padStart(3, '0')}`
      const subAccountName = `${getTableDisplayName(tableName)}: ${record.name}`

      // التحقق من عدم وجود الرابط مسبقاً
      const existingLink = await query(
        'SELECT id FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2 AND linked_record_id = $3',
        [accountId, tableName, record.id]
      )

      if (existingLink.rows.length === 0) {
        await query(`
          INSERT INTO account_sub_links
          (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
          VALUES ($1, $2, $3, $4, $5, $6)
        `, [
          accountId,
          tableName,
          record.id,
          subAccountCode,
          subAccountName,
          'النظام'
        ])
      }
    }
  } catch (error) {
    console.error('Error creating sub accounts:', error)
  }
}

// دالة مساعدة للحصول على اسم الجدول بالعربية
function getTableDisplayName(tableName: string): string {
  const tableNames: { [key: string]: string } = {
    'clients': 'حساب الموكل',
    'employees': 'حساب الموظف',
    'courts': 'حساب المحكمة',
    'branches': 'حساب الفرع',
    'cost_centers': 'مركز التكلفة',
    'issues': 'حساب القضية'
  }

  return tableNames[tableName] || 'حساب'
}
