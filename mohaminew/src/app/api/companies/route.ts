import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الشركات
export async function GET() {
  try {
    const result = await query(`
      SELECT * FROM companies
      ORDER BY created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching companies:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات الشركات' },
      { status: 500 }
    )
  }
}

// POST - إضافة شركة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name, legal_name, registration_number, address, city, country,
      phone, email, website, tax_number, commercial_register,
      logo_url, logo_right_text, logo_left_text, logo_image_url,
      established_date, legal_form, capital, description, is_active
    } = body

    if (!name) {
      return NextResponse.json(
        { success: false, error: 'اسم الشركة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO companies (
        name, legal_name, registration_number, address, city, country,
        phone, email, website, tax_number, commercial_register,
        logo_url, logo_right_text, logo_left_text, logo_image_url,
        established_date, legal_form, capital, description, is_active
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
      RETURNING *
    `, [name, legal_name, registration_number, address, city, country,
        phone, email, website, tax_number, commercial_register,
        logo_url, logo_right_text, logo_left_text, logo_image_url,
        established_date, legal_form, capital, description, is_active])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الشركة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating company:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الشركة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث شركة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id, name, legal_name, registration_number, address, city, country,
      phone, email, website, tax_number, commercial_register,
      logo_url, logo_right_text, logo_left_text, logo_image_url,
      established_date, legal_form, capital, description, is_active
    } = body

    if (!id || !name) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم الشركة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE companies
      SET name = $1, legal_name = $2, registration_number = $3, address = $4,
          city = $5, country = $6, phone = $7, email = $8, website = $9,
          tax_number = $10, commercial_register = $11, logo_url = $12,
          logo_right_text = $13, logo_left_text = $14, logo_image_url = $15,
          established_date = $16, legal_form = $17, capital = $18,
          description = $19, is_active = $20, updated_at = CURRENT_TIMESTAMP
      WHERE id = $21
      RETURNING *
    `, [name, legal_name, registration_number, address, city, country,
        phone, email, website, tax_number, commercial_register, logo_url,
        logo_right_text, logo_left_text, logo_image_url, established_date,
        legal_form, capital, description, is_active, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الشركة بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating company:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث بيانات الشركة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف شركة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الشركة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(
      'DELETE FROM companies WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الشركة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الشركة بنجاح'
    })
  } catch (error) {
    console.error('Error deleting company:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الشركة' },
      { status: 500 }
    )
  }
}
