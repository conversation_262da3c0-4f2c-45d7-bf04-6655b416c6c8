import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع العملات
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        id,
        currency_code,
        currency_name,
        symbol,
        is_active,
        exchange_rate,
        is_base_currency,
        created_date
      FROM currencies 
      WHERE is_active = true
      ORDER BY 
        is_base_currency DESC,
        currency_name ASC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching currencies:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب العملات' },
      { status: 500 }
    )
  }
}

// POST - إضافة عملة جديدة
export async function POST(request: NextRequest) {
  try {
    const { currency_code, currency_name, symbol, exchange_rate } = await request.json()

    if (!currency_code || !currency_name) {
      return NextResponse.json(
        { success: false, error: 'رمز العملة واسم العملة مطلوبان' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO currencies (currency_code, currency_name, symbol, exchange_rate)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `, [currency_code.toUpperCase(), currency_name, symbol, exchange_rate || 1.0000])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة العملة بنجاح'
    })
  } catch (error) {
    console.error('Error adding currency:', error)
    if (error instanceof Error && error.message.includes('duplicate key')) {
      return NextResponse.json(
        { success: false, error: 'رمز العملة موجود مسبقاً' },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة العملة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث أسعار الصرف
export async function PUT(request: NextRequest) {
  try {
    const { currencies } = await request.json()

    if (!currencies || !Array.isArray(currencies)) {
      return NextResponse.json(
        { success: false, error: 'بيانات العملات مطلوبة' },
        { status: 400 }
      )
    }

    await query('BEGIN')

    try {
      for (const currency of currencies) {
        if (currency.id && currency.exchange_rate) {
          await query(`
            UPDATE currencies 
            SET 
              exchange_rate = $2,
              updated_date = CURRENT_TIMESTAMP
            WHERE id = $1
          `, [currency.id, currency.exchange_rate])
        }
      }

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم تحديث أسعار الصرف بنجاح'
      })
    } catch (error) {
      await query('ROLLBACK')
      throw error
    }
  } catch (error) {
    console.error('Error updating exchange rates:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث أسعار الصرف' },
      { status: 500 }
    )
  }
}
