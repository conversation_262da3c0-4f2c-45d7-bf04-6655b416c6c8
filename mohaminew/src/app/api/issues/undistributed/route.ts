import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب القضايا غير الموزعة
export async function GET(request: NextRequest) {
  try {
    // جلب القضايا التي لم يتم توزيعها بعد
    const result = await query(`
      SELECT DISTINCT
        i.id,
        i.case_number,
        i.title,
        i.client_name,
        i.court_name,
        i.status,
        i.issue_type,
        i.amount,
        i.created_date
      FROM issues i
      LEFT JOIN case_distribution cd ON i.id = cd.issue_id
      WHERE cd.issue_id IS NULL
        AND i.status IN ('new', 'active', 'pending', 'in_progress', 'open')
      ORDER BY i.created_date DESC
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching undistributed issues:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب القضايا غير الموزعة' },
      { status: 500 }
    )
  }
}