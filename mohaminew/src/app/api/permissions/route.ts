import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع الصلاحيات
export async function GET() {
  try {
    console.log('🔄 API Permissions - بداية جلب الصلاحيات')
    
    const result = await query(`
      SELECT 
        permission_key,
        permission_name,
        category,
        description,
        is_active
      FROM permissions
      WHERE is_active = true
      ORDER BY category, permission_name
    `)

    // تجميع الصلاحيات حسب الفئة
    const permissionsByCategory = result.rows.reduce((acc: any, permission: any) => {
      if (!acc[permission.category]) {
        acc[permission.category] = []
      }
      acc[permission.category].push({
        key: permission.permission_key,
        name: permission.permission_name,
        description: permission.description
      })
      return acc
    }, {})

    console.log(`📊 تم جلب ${result.rows.length} صلاحية من ${Object.keys(permissionsByCategory).length} فئة`)

    return NextResponse.json({
      success: true,
      data: {
        permissions: result.rows,
        permissionsByCategory
      }
    })
  } catch (error) {
    console.error('❌ خطأ في جلب الصلاحيات:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الصلاحيات' },
      { status: 500 }
    )
  }
}
