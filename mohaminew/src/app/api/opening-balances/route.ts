import { NextRequest, NextResponse } from 'next/server'

// GET - جلب الأرصدة الافتتاحية من قاعدة البيانات الحقيقية
export async function GET() {
  try {
    // هنا يجب الاتصال بقاعدة البيانات الحقيقية
    // لكن سنستخدم البيانات المنسوخة كمحاكاة
    const sampleData = [
      {
        id: 1,
        account_name: 'النقدية بالصندوق',
        account_code: '1001',
        debit_amount: 50000.00,
        credit_amount: 0.00,
        balance_type: 'مدين',
        created_date: '2024-01-01'
      },
      {
        id: 2,
        account_name: 'البنك الأهلي',
        account_code: '1002',
        debit_amount: 150000.00,
        credit_amount: 0.00,
        balance_type: 'مدين',
        created_date: '2024-01-01'
      },
      {
        id: 3,
        account_name: 'العملاء',
        account_code: '1101',
        debit_amount: 75000.00,
        credit_amount: 0.00,
        balance_type: 'مدين',
        created_date: '2024-01-01'
      },
      {
        id: 4,
        account_name: 'المخزون',
        account_code: '1201',
        debit_amount: 25000.00,
        credit_amount: 0.00,
        balance_type: 'مدين',
        created_date: '2024-01-01'
      },
      {
        id: 5,
        account_name: 'رأس المال',
        account_code: '3001',
        debit_amount: 0.00,
        credit_amount: 200000.00,
        balance_type: 'دائن',
        created_date: '2024-01-01'
      },
      {
        id: 6,
        account_name: 'الموردين',
        account_code: '2001',
        debit_amount: 0.00,
        credit_amount: 50000.00,
        balance_type: 'دائن',
        created_date: '2024-01-01'
      },
      {
        id: 7,
        account_name: 'مصروفات التأسيس',
        account_code: '1301',
        debit_amount: 15000.00,
        credit_amount: 0.00,
        balance_type: 'مدين',
        created_date: '2024-01-01'
      },
      {
        id: 8,
        account_name: 'إيرادات الخدمات',
        account_code: '4001',
        debit_amount: 0.00,
        credit_amount: 65000.00,
        balance_type: 'دائن',
        created_date: '2024-01-01'
      }
    ]
    
    return NextResponse.json({
      success: true,
      data: sampleData
    })
  } catch (error) {
    console.error('Error fetching opening balances:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الأرصدة الافتتاحية' },
      { status: 500 }
    )
  }
}

// POST - إضافة رصيد افتتاحي جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      account_name,
      account_code,
      debit_amount,
      credit_amount,
      balance_type
    } = body

    if (!account_name || !account_code || !balance_type) {
      return NextResponse.json(
        { success: false, error: 'اسم الحساب ورمز الحساب ونوع الرصيد مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من أن أحد المبلغين فقط يحتوي على قيمة
    if ((debit_amount > 0 && credit_amount > 0) || (debit_amount === 0 && credit_amount === 0)) {
      return NextResponse.json(
        { success: false, error: 'يجب إدخال مبلغ في جانب واحد فقط (مدين أو دائن)' },
        { status: 400 }
      )
    }

    // محاكاة إضافة رصيد جديد
    const newBalance = {
      id: Date.now(),
      account_name,
      account_code,
      debit_amount: Number(debit_amount) || 0,
      credit_amount: Number(credit_amount) || 0,
      balance_type,
      created_date: new Date().toISOString().split('T')[0]
    }

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الرصيد الافتتاحي بنجاح',
      data: newBalance
    })
  } catch (error) {
    console.error('Error creating opening balance:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الرصيد الافتتاحي' },
      { status: 500 }
    )
  }
}

// PUT - تحديث رصيد افتتاحي
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      id,
      account_name,
      account_code,
      debit_amount,
      credit_amount,
      balance_type
    } = body

    if (!id || !account_name || !account_code || !balance_type) {
      return NextResponse.json(
        { success: false, error: 'المعرف واسم الحساب ورمز الحساب ونوع الرصيد مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من أن أحد المبلغين فقط يحتوي على قيمة
    if ((debit_amount > 0 && credit_amount > 0) || (debit_amount === 0 && credit_amount === 0)) {
      return NextResponse.json(
        { success: false, error: 'يجب إدخال مبلغ في جانب واحد فقط (مدين أو دائن)' },
        { status: 400 }
      )
    }

    // محاكاة تحديث الرصيد
    return NextResponse.json({
      success: true,
      message: 'تم تحديث الرصيد الافتتاحي بنجاح'
    })
  } catch (error) {
    console.error('Error updating opening balance:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الرصيد الافتتاحي' },
      { status: 500 }
    )
  }
}

// DELETE - حذف رصيد افتتاحي
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الرصيد الافتتاحي مطلوب' },
        { status: 400 }
      )
    }

    // محاكاة حذف الرصيد
    return NextResponse.json({
      success: true,
      message: 'تم حذف الرصيد الافتتاحي بنجاح'
    })
  } catch (error) {
    console.error('Error deleting opening balance:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الرصيد الافتتاحي' },
      { status: 500 }
    )
  }
}
