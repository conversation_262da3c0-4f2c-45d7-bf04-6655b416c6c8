import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب إعدادات ربط الحسابات
export async function GET() {
  try {
    // البيانات الافتراضية للحسابات الأساسية
    const defaultSettings = [
      {
        id: 1,
        setting_name: 'حساب الإيرادات الرئيسي',
        setting_description: 'لتسجيل جميع إيرادات المكتب من القضايا والخدمات القانونية',
        account_code: '4000',
        account_name: 'الإيرادات الرئيسية',
        account_type: 'I',
        is_enabled: true,
        is_created: false,
        current_balance: 0,
        icon_name: 'TrendingUp',
        color_class: 'border-l-green-500'
      },
      {
        id: 2,
        setting_name: 'حساب المصروفات الرئيسي',
        setting_description: 'لتسجيل جميع مصروفات المكتب التشغيلية والإدارية',
        account_code: '5000',
        account_name: 'المصروفات الرئيسية',
        account_type: 'E',
        is_enabled: true,
        is_created: false,
        current_balance: 0,
        icon_name: 'TrendingDown',
        color_class: 'border-l-red-500'
      },
      {
        id: 3,
        setting_name: 'حساب رأس المال',
        setting_description: 'لرأس المال المستثمر في المكتب',
        account_code: '3000',
        account_name: 'رأس المال',
        account_type: 'E',
        is_enabled: true,
        is_created: false,
        current_balance: 0,
        icon_name: 'Building',
        color_class: 'border-l-purple-500'
      },
      {
        id: 4,
        setting_name: 'الصندوق الرئيسي',
        setting_description: 'صندوق النقدية الرئيسي للمكتب',
        account_code: '1111',
        account_name: 'الصندوق الرئيسي',
        account_type: 'A',
        is_enabled: true,
        is_created: false,
        current_balance: 0,
        icon_name: 'Wallet',
        color_class: 'border-l-blue-500'
      },
      {
        id: 5,
        setting_name: 'الحساب الرئيسي للعملاء',
        setting_description: 'الحساب المراقب لجميع حسابات العملاء الفردية',
        account_code: '1121',
        account_name: 'حسابات العملاء',
        account_type: 'A',
        is_enabled: true,
        is_created: false,
        current_balance: 0,
        linked_table: 'clients',
        auto_create_sub_accounts: true,
        icon_name: 'Users',
        color_class: 'border-l-indigo-500'
      },
      {
        id: 6,
        setting_name: 'الحساب الرئيسي للموظفين',
        setting_description: 'الحساب المراقب لجميع حسابات الموظفين الفردية',
        account_code: '1122',
        account_name: 'حسابات الموظفين',
        account_type: 'A',
        is_enabled: true,
        is_created: false,
        current_balance: 0,
        linked_table: 'employees',
        auto_create_sub_accounts: true,
        icon_name: 'UserCheck',
        color_class: 'border-l-orange-500'
      },
      {
        id: 7,
        setting_name: 'الحسابات الوسيطة',
        setting_description: 'حسابات وسيطة للعمليات المحاسبية المؤقتة',
        account_code: '2500',
        account_name: 'الحسابات الوسيطة',
        account_type: 'L',
        is_enabled: true,
        is_created: false,
        current_balance: 0,
        icon_name: 'DollarSign',
        color_class: 'border-l-teal-500'
      }
    ]

    // التحقق من وجود الحسابات في قاعدة البيانات
    for (let setting of defaultSettings) {
      try {
        const result = await query(
          'SELECT id, account_balance FROM chart_of_accounts WHERE account_code = $1',
          [setting.account_code]
        )
        
        if (result.rows.length > 0) {
          setting.is_created = true
          setting.current_balance = parseFloat(result.rows[0].account_balance) || 0
        }
      } catch (error) {
        console.error(`Error checking account ${setting.account_code}:`, error)
      }
    }

    return NextResponse.json({
      success: true,
      data: defaultSettings
    })
  } catch (error) {
    console.error('Error fetching account linking settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب إعدادات الربط' },
      { status: 500 }
    )
  }
}

// POST - حفظ أو تحديث إعدادات الربط
export async function POST(request: NextRequest) {
  try {
    const { settings } = await request.json()

    if (!settings || !Array.isArray(settings)) {
      return NextResponse.json(
        { success: false, error: 'بيانات الإعدادات مطلوبة' },
        { status: 400 }
      )
    }

    // معالجة كل إعداد
    for (const setting of settings) {
      const {
        account_code,
        account_name,
        account_type,
        is_enabled,
        linked_table,
        auto_create_sub_accounts
      } = setting

      // التحقق من وجود الحساب
      const existingAccount = await query(
        'SELECT id FROM chart_of_accounts WHERE account_code = $1',
        [account_code]
      )

      if (existingAccount.rows.length === 0 && is_enabled) {
        // إنشاء الحساب إذا لم يكن موجوداً
        await query(`
          INSERT INTO chart_of_accounts 
          (account_code, account_name, account_type, account_level, parent_id, 
           is_active, accepts_transactions, linked_table, auto_create_sub_accounts)
          VALUES ($1, $2, $3, 1, NULL, true, true, $4, $5)
        `, [account_code, account_name, account_type, linked_table, auto_create_sub_accounts])
      } else if (existingAccount.rows.length > 0) {
        // تحديث الحساب الموجود
        await query(`
          UPDATE chart_of_accounts 
          SET account_name = $1, is_active = $2, linked_table = $3, 
              auto_create_sub_accounts = $4
          WHERE account_code = $5
        `, [account_name, is_enabled, linked_table, auto_create_sub_accounts, account_code])
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم حفظ إعدادات الربط بنجاح'
    })
  } catch (error) {
    console.error('Error saving account linking settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حفظ إعدادات الربط' },
      { status: 500 }
    )
  }
}