import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب جميع طرق الدفع
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        id,
        method_code,
        method_name,
        description,
        is_active,
        created_date
      FROM payment_methods
      WHERE is_active = true
      ORDER BY method_name
    `)

    return NextResponse.json({
      success: true,
      methods: result.rows,
      total: result.rows.length,
      message: 'تم جلب طرق الدفع بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب طرق الدفع:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب طرق الدفع',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة طريقة دفع جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      method_code,
      method_name,
      description
    } = body

    // التحقق من صحة البيانات
    if (!method_code || !method_name) {
      return NextResponse.json({
        success: false,
        error: 'رمز طريقة الدفع واسم طريقة الدفع مطلوبان'
      }, { status: 400 })
    }

    // التحقق من عدم تكرار رمز طريقة الدفع
    const existingMethod = await query(
      'SELECT id FROM payment_methods WHERE method_code = $1',
      [method_code]
    )

    if (existingMethod.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رمز طريقة الدفع موجود مسبقاً'
      }, { status: 400 })
    }

    const result = await query(`
      INSERT INTO payment_methods (method_code, method_name, description)
      VALUES ($1, $2, $3)
      RETURNING *
    `, [method_code, method_name, description])

    return NextResponse.json({
      success: true,
      method: result.rows[0],
      message: 'تم إضافة طريقة الدفع بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة طريقة الدفع:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة طريقة الدفع',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
