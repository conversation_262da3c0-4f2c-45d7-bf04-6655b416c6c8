import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'mohammi',
  password: process.env.DB_PASSWORD || 'your_password_here',
  port: 5432,
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const asOfDate = searchParams.get('as_of_date') || new Date().toISOString().split('T')[0]

    const client = await pool.connect()

    try {
      // استعلام للحصول على أرصدة جميع الحسابات التي تقبل معاملات
      const balancesQuery = `
        WITH account_balances AS (
          SELECT 
            coa.id,
            coa.account_code,
            coa.account_name,
            coa.account_type,
            coa.account_nature,
            coa.is_linked_record,
            coa.original_table,
            
            -- حساب الأرصدة
            COALESCE(SUM(
              CASE 
                WHEN jed.debit_amount > 0 THEN jed.debit_amount 
                ELSE 0 
              END
            ), 0) as debit_balance,
            
            COALESCE(SUM(
              CASE 
                WHEN jed.credit_amount > 0 THEN jed.credit_amount 
                ELSE 0 
              END
            ), 0) as credit_balance,
            
            -- عدد المعاملات
            COUNT(jed.id) as transactions_count,
            
            -- آخر معاملة
            MAX(je.entry_date) as last_transaction_date,
            
            -- معلومات إضافية للحسابات المرتبطة
            CASE 
              WHEN coa.original_table = 'clients' THEN c.name
              WHEN coa.original_table = 'employees' THEN e.name
              WHEN coa.original_table = 'suppliers' THEN s.name
              ELSE NULL
            END as linked_entity_name,
            
            CASE 
              WHEN coa.original_table = 'clients' THEN c.phone
              WHEN coa.original_table = 'employees' THEN e.phone
              WHEN coa.original_table = 'suppliers' THEN s.phone
              ELSE NULL
            END as linked_entity_phone,
            
            CASE 
              WHEN coa.original_table = 'clients' THEN c.email
              WHEN coa.original_table = 'employees' THEN e.email
              WHEN coa.original_table = 'suppliers' THEN s.email
              ELSE NULL
            END as linked_entity_email
            
          FROM chart_of_accounts coa
          LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
          LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id 
            AND je.entry_date <= $1
            AND je.status = 'approved'
          LEFT JOIN clients c ON coa.original_table = 'clients' 
            AND coa.linked_record_id = c.id
          LEFT JOIN employees e ON coa.original_table = 'employees' 
            AND coa.linked_record_id = e.id
          LEFT JOIN suppliers s ON coa.original_table = 'suppliers' 
            AND coa.linked_record_id = s.id
          
          WHERE coa.allow_transactions = true
          GROUP BY 
            coa.id, coa.account_code, coa.account_name, coa.account_type, 
            coa.account_nature, coa.is_linked_record, coa.original_table,
            c.name, c.phone, c.email,
            e.name, e.phone, e.email,
            s.name, s.phone, s.email
        )
        SELECT 
          *,
          -- حساب صافي الرصيد
          (debit_balance - credit_balance) as net_balance,
          
          -- تحديد نوع الرصيد
          CASE 
            WHEN (debit_balance - credit_balance) > 0 THEN 'مدين'
            WHEN (debit_balance - credit_balance) < 0 THEN 'دائن'
            ELSE 'متوازن'
          END as balance_type
          
        FROM account_balances
        WHERE debit_balance > 0 OR credit_balance > 0 OR transactions_count > 0
        ORDER BY 
          account_type,
          CASE WHEN is_linked_record THEN 1 ELSE 0 END,
          account_code
      `

      const balancesResult = await client.query(balancesQuery, [asOfDate])

      // حساب الملخص
      const summaryQuery = `
        WITH account_balances AS (
          SELECT 
            coa.id,
            coa.account_type,
            COALESCE(SUM(
              CASE 
                WHEN jed.debit_amount > 0 THEN jed.debit_amount 
                ELSE 0 
              END
            ), 0) as debit_balance,
            COALESCE(SUM(
              CASE 
                WHEN jed.credit_amount > 0 THEN jed.credit_amount 
                ELSE 0 
              END
            ), 0) as credit_balance
            
          FROM chart_of_accounts coa
          LEFT JOIN journal_entry_details jed ON coa.id = jed.account_id
          LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id 
            AND je.entry_date <= $1
            AND je.status = 'approved'
          
          WHERE coa.allow_transactions = true
          GROUP BY coa.id, coa.account_type
        ),
        balance_summary AS (
          SELECT 
            *,
            (debit_balance - credit_balance) as net_balance
          FROM account_balances
          WHERE debit_balance > 0 OR credit_balance > 0
        )
        SELECT 
          COALESCE(SUM(CASE WHEN net_balance > 0 THEN net_balance ELSE 0 END), 0) as total_debtors,
          COALESCE(SUM(CASE WHEN net_balance < 0 THEN ABS(net_balance) ELSE 0 END), 0) as total_creditors,
          COUNT(CASE WHEN net_balance > 0 THEN 1 END) as debtors_count,
          COUNT(CASE WHEN net_balance < 0 THEN 1 END) as creditors_count,
          COALESCE(SUM(net_balance), 0) as net_position
        FROM balance_summary
      `

      const summaryResult = await client.query(summaryQuery, [asOfDate])

      return NextResponse.json({
        success: true,
        balances: balancesResult.rows,
        summary: summaryResult.rows[0] || {
          total_debtors: 0,
          total_creditors: 0,
          debtors_count: 0,
          creditors_count: 0,
          net_position: 0
        },
        as_of_date: asOfDate
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('خطأ في جلب قائمة الدائنين والمدينين:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب قائمة الدائنين والمدينين',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}