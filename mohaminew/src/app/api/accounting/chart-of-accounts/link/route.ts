import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const { account_id, link_type } = await request.json()

    if (!account_id || !link_type) {
      return NextResponse.json({
        success: false,
        error: 'معرف الحساب ونوع الربط مطلوبان'
      }, { status: 400 })
    }

    // جلب بيانات الحساب
    const accountResult = await query(
      'SELECT id, account_code, account_name FROM chart_of_accounts WHERE id = $1',
      [account_id]
    )

    if (accountResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    const account = accountResult.rows[0]
    let results = { created_accounts: 0, linked_records: 0 }

    switch (link_type) {
      case 'clients':
        results = await linkClients(account)
        break
      case 'employees':
        results = await linkEmployees(account)
        break
      case 'suppliers':
        results = await linkSuppliers(account)
        break
      default:
        return NextResponse.json({
          success: false,
          error: 'نوع ربط غير صحيح'
        }, { status: 400 })
    }

    return NextResponse.json({
      success: true,
      message: `تم ربط ${results.linked_records} ${getLinkTypeLabel(link_type)} بالحساب ${account.account_code} مباشرة`,
      details: results
    })

  } catch (error) {
    console.error('Error in chart accounts linking:', error)
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في تطبيق الربط'
    }, { status: 500 })
  }
}

async function linkClients(parentAccount: any) {
  try {
    // جلب جميع العملاء النشطين
    const clients = await query(
      'SELECT id, name FROM clients WHERE status = $1 ORDER BY id',
      ['active']
    )

    let createdAccounts = 0
    let linkedRecords = 0

    // إنشاء حساب فرعي لكل عميل
    for (const client of clients.rows) {
      try {
        // إنشاء كود الحساب الفرعي
        const subAccountCode = `${parentAccount.account_code}${String(client.id).padStart(3, '0')}`
        const subAccountName = `عميل: ${client.name}`

        // التحقق من عدم وجود الحساب مسبقاً
        const existingAccount = await query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [subAccountCode]
        )

        let subAccountId

        if (existingAccount.rows.length > 0) {
          // استخدام الحساب الموجود
          subAccountId = existingAccount.rows[0].id
        } else {
          // إنشاء حساب فرعي جديد
          const newAccount = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `, [
            subAccountCode,
            subAccountName,
            parentAccount.account_type,
            parentAccount.id,
            (parentAccount.account_level || 1) + 1
          ])

          subAccountId = newAccount.rows[0].id
          createdAccounts++
        }

        // ربط العميل بالحساب الفرعي
        await query(
          'UPDATE clients SET account_id = $1 WHERE id = $2',
          [subAccountId, client.id]
        )

        linkedRecords++

      } catch (clientError) {
        console.error(`خطأ في ربط العميل ${client.name}:`, clientError)
      }
    }

    

    return { created_accounts: createdAccounts, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in linkClients:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}

async function linkEmployees(parentAccount: any) {
  try {
    // جلب جميع الموظفين النشطين
    const employees = await query(
      'SELECT id, name FROM employees WHERE status = $1 ORDER BY id',
      ['active']
    )

    let createdAccounts = 0
    let linkedRecords = 0

    // إنشاء حساب فرعي لكل موظف
    for (const employee of employees.rows) {
      try {
        // إنشاء كود الحساب الفرعي
        const subAccountCode = `${parentAccount.account_code}${String(employee.id).padStart(3, '0')}`
        const subAccountName = `موظف: ${employee.name}`

        // التحقق من عدم وجود الحساب مسبقاً
        const existingAccount = await query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [subAccountCode]
        )

        let subAccountId

        if (existingAccount.rows.length > 0) {
          // استخدام الحساب الموجود
          subAccountId = existingAccount.rows[0].id
        } else {
          // إنشاء حساب فرعي جديد
          const newAccount = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `, [
            subAccountCode,
            subAccountName,
            parentAccount.account_type,
            parentAccount.id,
            (parentAccount.account_level || 1) + 1
          ])

          subAccountId = newAccount.rows[0].id
          createdAccounts++
        }

        // ربط الموظف بالحساب الفرعي
        await query(
          'UPDATE employees SET account_id = $1 WHERE id = $2',
          [subAccountId, employee.id]
        )

        linkedRecords++

      } catch (employeeError) {
        console.error(`خطأ في ربط الموظف ${employee.name}:`, employeeError)
      }
    }

    

    return { created_accounts: createdAccounts, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in linkEmployees:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}

async function linkSuppliers(parentAccount: any) {
  try {
    // التحقق من وجود جدول الموردين
    const tableCheck = await query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'suppliers'
    `)

    if (tableCheck.rows.length === 0) {
      return { created_accounts: 0, linked_records: 0 }
    }

    // جلب جميع الموردين النشطين
    const suppliers = await query(
      'SELECT id, name FROM suppliers WHERE status = $1 ORDER BY id',
      ['active']
    )

    let createdAccounts = 0
    let linkedRecords = 0

    // إنشاء حساب فرعي لكل مورد
    for (const supplier of suppliers.rows) {
      try {
        // إنشاء كود الحساب الفرعي
        const subAccountCode = `${parentAccount.account_code}${String(supplier.id).padStart(3, '0')}`
        const subAccountName = `مورد: ${supplier.name}`

        // التحقق من عدم وجود الحساب مسبقاً
        const existingAccount = await query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [subAccountCode]
        )

        let subAccountId

        if (existingAccount.rows.length > 0) {
          // استخدام الحساب الموجود
          subAccountId = existingAccount.rows[0].id
        } else {
          // إنشاء حساب فرعي جديد
          const newAccount = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `, [
            subAccountCode,
            subAccountName,
            parentAccount.account_type,
            parentAccount.id,
            (parentAccount.account_level || 1) + 1
          ])

          subAccountId = newAccount.rows[0].id
          createdAccounts++
        }

        // ربط المورد بالحساب الفرعي
        await query(
          'UPDATE suppliers SET account_id = $1 WHERE id = $2',
          [subAccountId, supplier.id]
        )

        linkedRecords++

      } catch (supplierError) {
        console.error(`خطأ في ربط المورد ${supplier.name}:`, supplierError)
      }
    }

    

    return { created_accounts: createdAccounts, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in linkSuppliers:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}

function getLinkTypeLabel(linkType: string): string {
  switch (linkType) {
    case 'clients': return 'عميل'
    case 'employees': return 'موظف'
    case 'suppliers': return 'مورد'
    default: return 'سجل'
  }
}
