import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

/**
 * API لإعادة ربط الحسابات الموجودة وإنشاء حسابات فرعية منفصلة
 * يستخدم لتصحيح البيانات الحالية التي تم ربطها بالحساب الرئيسي
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { link_type } = body

    if (!link_type || !['clients', 'employees', 'suppliers'].includes(link_type)) {
      return NextResponse.json({
        success: false,
        error: 'نوع الربط مطلوب ويجب أن يكون أحد: clients, employees, suppliers'
      }, { status: 400 })
    }

    

    let result
    switch (link_type) {
      case 'clients':
        result = await relinkClients()
        break
      case 'employees':
        result = await relinkEmployees()
        break
      case 'suppliers':
        result = await relinkSuppliers()
        break
      default:
        throw new Error('نوع ربط غير مدعوم')
    }

    return NextResponse.json({
      success: true,
      message: `تم إعادة ربط ${link_type} بنجاح`,
      ...result
    })

  } catch (error) {
    console.error('Error in chart accounts relinking:', error)
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في إعادة تطبيق الربط'
    }, { status: 500 })
  }
}

async function relinkClients() {
  try {
    // البحث عن الحساب الرئيسي للعملاء
    const mainAccount = await query(`
      SELECT id, account_code, account_name, account_type, account_level
      FROM chart_of_accounts 
      WHERE account_name LIKE '%عملاء%' OR account_name LIKE '%العملاء%'
      ORDER BY account_level ASC
      LIMIT 1
    `)

    if (mainAccount.rows.length === 0) {
      return { created_accounts: 0, linked_records: 0, error: 'لم يتم العثور على حساب العملاء الرئيسي' }
    }

    const parentAccount = mainAccount.rows[0]

    // جلب جميع العملاء المربوطين بالحساب الرئيسي
    const clients = await query(`
      SELECT id, name 
      FROM clients 
      WHERE account_id = $1 AND status = 'active'
      ORDER BY id
    `, [parentAccount.id])

    let createdAccounts = 0
    let linkedRecords = 0

    // إنشاء حساب فرعي لكل عميل
    for (const client of clients.rows) {
      try {
        // إنشاء كود الحساب الفرعي
        const subAccountCode = `${parentAccount.account_code}${String(client.id).padStart(3, '0')}`
        const subAccountName = `عميل: ${client.name}`

        // التحقق من عدم وجود الحساب مسبقاً
        const existingAccount = await query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [subAccountCode]
        )

        let subAccountId

        if (existingAccount.rows.length > 0) {
          // استخدام الحساب الموجود
          subAccountId = existingAccount.rows[0].id
        } else {
          // إنشاء حساب فرعي جديد
          const newAccount = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `, [
            subAccountCode, 
            subAccountName, 
            parentAccount.account_type,
            parentAccount.id,
            (parentAccount.account_level || 1) + 1
          ])

          subAccountId = newAccount.rows[0].id
          createdAccounts++
        }

        // ربط العميل بالحساب الفرعي
        await query(
          'UPDATE clients SET account_id = $1 WHERE id = $2',
          [subAccountId, client.id]
        )

        linkedRecords++

      } catch (clientError) {
        console.error(`خطأ في إعادة ربط العميل ${client.name}:`, clientError)
      }
    }

    

    return { created_accounts: createdAccounts, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in relinkClients:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}

async function relinkEmployees() {
  try {
    // البحث عن الحساب الرئيسي للموظفين
    const mainAccount = await query(`
      SELECT id, account_code, account_name, account_type, account_level
      FROM chart_of_accounts 
      WHERE account_name LIKE '%موظف%' OR account_name LIKE '%الموظفين%'
      ORDER BY account_level ASC
      LIMIT 1
    `)

    if (mainAccount.rows.length === 0) {
      return { created_accounts: 0, linked_records: 0, error: 'لم يتم العثور على حساب الموظفين الرئيسي' }
    }

    const parentAccount = mainAccount.rows[0]

    // جلب جميع الموظفين المربوطين بالحساب الرئيسي
    const employees = await query(`
      SELECT id, name 
      FROM employees 
      WHERE account_id = $1 AND status = 'active'
      ORDER BY id
    `, [parentAccount.id])

    let createdAccounts = 0
    let linkedRecords = 0

    // إنشاء حساب فرعي لكل موظف
    for (const employee of employees.rows) {
      try {
        // إنشاء كود الحساب الفرعي
        const subAccountCode = `${parentAccount.account_code}${String(employee.id).padStart(3, '0')}`
        const subAccountName = `موظف: ${employee.name}`

        // التحقق من عدم وجود الحساب مسبقاً
        const existingAccount = await query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [subAccountCode]
        )

        let subAccountId

        if (existingAccount.rows.length > 0) {
          // استخدام الحساب الموجود
          subAccountId = existingAccount.rows[0].id
        } else {
          // إنشاء حساب فرعي جديد
          const newAccount = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `, [
            subAccountCode, 
            subAccountName, 
            parentAccount.account_type,
            parentAccount.id,
            (parentAccount.account_level || 1) + 1
          ])

          subAccountId = newAccount.rows[0].id
          createdAccounts++
        }

        // ربط الموظف بالحساب الفرعي
        await query(
          'UPDATE employees SET account_id = $1 WHERE id = $2',
          [subAccountId, employee.id]
        )

        linkedRecords++

      } catch (employeeError) {
        console.error(`خطأ في إعادة ربط الموظف ${employee.name}:`, employeeError)
      }
    }

    

    return { created_accounts: createdAccounts, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in relinkEmployees:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}

async function relinkSuppliers() {
  try {
    // التحقق من وجود جدول الموردين
    const tableCheck = await query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public' AND table_name = 'suppliers'
    `)

    if (tableCheck.rows.length === 0) {
      return { created_accounts: 0, linked_records: 0 }
    }

    // البحث عن الحساب الرئيسي للموردين
    const mainAccount = await query(`
      SELECT id, account_code, account_name, account_type, account_level
      FROM chart_of_accounts 
      WHERE account_name LIKE '%مورد%' OR account_name LIKE '%الموردين%'
      ORDER BY account_level ASC
      LIMIT 1
    `)

    if (mainAccount.rows.length === 0) {
      return { created_accounts: 0, linked_records: 0, error: 'لم يتم العثور على حساب الموردين الرئيسي' }
    }

    const parentAccount = mainAccount.rows[0]

    // جلب جميع الموردين المربوطين بالحساب الرئيسي
    const suppliers = await query(`
      SELECT id, name 
      FROM suppliers 
      WHERE account_id = $1 AND status = 'active'
      ORDER BY id
    `, [parentAccount.id])

    let createdAccounts = 0
    let linkedRecords = 0

    // إنشاء حساب فرعي لكل مورد
    for (const supplier of suppliers.rows) {
      try {
        // إنشاء كود الحساب الفرعي
        const subAccountCode = `${parentAccount.account_code}${String(supplier.id).padStart(3, '0')}`
        const subAccountName = `مورد: ${supplier.name}`

        // التحقق من عدم وجود الحساب مسبقاً
        const existingAccount = await query(
          'SELECT id FROM chart_of_accounts WHERE account_code = $1',
          [subAccountCode]
        )

        let subAccountId

        if (existingAccount.rows.length > 0) {
          // استخدام الحساب الموجود
          subAccountId = existingAccount.rows[0].id
        } else {
          // إنشاء حساب فرعي جديد
          const newAccount = await query(`
            INSERT INTO chart_of_accounts (
              account_code, account_name, account_type, parent_id,
              is_active, allow_transactions, current_balance, account_level
            ) VALUES ($1, $2, $3, $4, true, true, 0, $5)
            RETURNING id
          `, [
            subAccountCode, 
            subAccountName, 
            parentAccount.account_type,
            parentAccount.id,
            (parentAccount.account_level || 1) + 1
          ])

          subAccountId = newAccount.rows[0].id
          createdAccounts++
        }

        // ربط المورد بالحساب الفرعي
        await query(
          'UPDATE suppliers SET account_id = $1 WHERE id = $2',
          [subAccountId, supplier.id]
        )

        linkedRecords++

      } catch (supplierError) {
        console.error(`خطأ في إعادة ربط المورد ${supplier.name}:`, supplierError)
      }
    }

    

    return { created_accounts: createdAccounts, linked_records: linkedRecords }

  } catch (error) {
    console.error('Error in relinkSuppliers:', error)
    return { created_accounts: 0, linked_records: 0 }
  }
}
