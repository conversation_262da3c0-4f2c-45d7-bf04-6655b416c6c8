import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// POST - حفظ أرصدة افتتاحية متعددة
export async function POST(request: NextRequest) {
  try {
    const { balances } = await request.json()

    if (!balances || !Array.isArray(balances) || balances.length === 0) {
      return NextResponse.json(
        { success: false, error: 'يجب إرسال مصفوفة من الأرصدة' },
        { status: 400 }
      )
    }

    // التحقق من التوازن
    const totalDebit = balances.reduce((sum, balance) => sum + (balance.debit_balance || 0), 0)
    const totalCredit = balances.reduce((sum, balance) => sum + (balance.credit_balance || 0), 0)
    
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      return NextResponse.json(
        { 
          success: false, 
          error: `الأرصدة غير متوازنة. المدين: ${totalDebit.toFixed(2)}, الدائن: ${totalCredit.toFixed(2)}` 
        },
        { status: 400 }
      )
    }

    // التحقق من وجود الحسابات
    const accountIds = balances.map(b => b.account_id)
    const regularAccountIds = accountIds.filter(id => !String(id).startsWith('client_') && !String(id).startsWith('employee_'))
    
    // فحص الحسابات العادية
    if (regularAccountIds.length > 0) {
      const accountsCheck = await query(
        `SELECT id FROM chart_of_accounts WHERE id = ANY($1)`,
        [regularAccountIds]
      )

      if (accountsCheck.rows.length !== regularAccountIds.length) {
        return NextResponse.json(
          { success: false, error: 'بعض الحسابات المحددة غير موجودة' },
          { status: 400 }
        )
      }
    }

    // فحص حسابات العملاء
    const clientAccountIds = accountIds.filter(id => String(id).startsWith('client_')).map(id => String(id).replace('client_', ''))
    if (clientAccountIds.length > 0) {
      const clientsCheck = await query(
        `SELECT id FROM clients WHERE id = ANY($1)`,
        [clientAccountIds]
      )
      if (clientsCheck.rows.length !== clientAccountIds.length) {
        return NextResponse.json(
          { success: false, error: 'بعض العملاء المحددين غير موجودين' },
          { status: 400 }
        )
      }
    }

    // فحص حسابات الموظفين
    const employeeAccountIds = accountIds.filter(id => String(id).startsWith('employee_')).map(id => String(id).replace('employee_', ''))
    if (employeeAccountIds.length > 0) {
      const employeesCheck = await query(
        `SELECT id FROM employees WHERE id = ANY($1)`,
        [employeeAccountIds]
      )
      if (employeesCheck.rows.length !== employeeAccountIds.length) {
        return NextResponse.json(
          { success: false, error: 'بعض الموظفين المحددين غير موجودين' },
          { status: 400 }
        )
      }
    }

    // حذف الأرصدة الموجودة للحسابات المحددة
    // نحتاج لتحويل معرفات الحسابات المرتبطة إلى معرفات حقيقية
    const realAccountIds = accountIds.map(id => {
      if (String(id).startsWith('client_') || String(id).startsWith('employee_')) {
        return String(id) // نحتفظ بالمعرف كما هو للحسابات المرتبطة
      }
      return id
    })

    await query(
      `DELETE FROM opening_balances WHERE account_id::text = ANY($1)`,
      [realAccountIds.map(id => String(id))]
    )

    // إدراج الأرصدة الجديدة
    const insertPromises = balances.map(balance => {
      return query(
        `INSERT INTO opening_balances (account_id, debit_balance, credit_balance, balance_date)
         VALUES ($1, $2, $3, $4)`,
        [
          String(balance.account_id), // تحويل إلى نص للتعامل مع الحسابات المرتبطة
          balance.debit_balance || 0,
          balance.credit_balance || 0,
          balance.balance_date
        ]
      )
    })

    await Promise.all(insertPromises)

    return NextResponse.json({
      success: true,
      message: `تم حفظ ${balances.length} رصيد افتتاحي بنجاح`,
      data: {
        count: balances.length,
        totalDebit: totalDebit.toFixed(2),
        totalCredit: totalCredit.toFixed(2)
      }
    })

  } catch (error) {
    console.error('خطأ في حفظ الأرصدة الافتتاحية:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في حفظ الأرصدة الافتتاحية' },
      { status: 500 }
    )
  }
}