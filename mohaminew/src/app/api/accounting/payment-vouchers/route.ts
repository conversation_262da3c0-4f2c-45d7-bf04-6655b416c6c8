import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// دالة لضمان وجود جدول سندات الصرف
async function ensurePaymentVouchersTable() {
  try {
    await query(`
      CREATE TABLE IF NOT EXISTS payment_vouchers (
        id SERIAL PRIMARY KEY,
        entry_number VARCHAR(50) UNIQUE NOT NULL,
        voucher_number VARCHAR(50),
        entry_date DATE NOT NULL,
        voucher_date DATE,
        payee_name VARCHAR(255) NOT NULL,
        payee_type VARCHAR(50) DEFAULT 'external',
        debit_account_id INTEGER,
        credit_account_id INTEGER,
        amount DECIMAL(15,2) NOT NULL,
        currency_id INTEGER DEFAULT 1,
        payment_method_id INTEGER,
        cost_center_id INTEGER,
        description TEXT,
        reference_number VARCHAR(100),
        case_id INTEGER,
        service_id INTEGER,
        status VARCHAR(20) DEFAULT 'draft',
        created_by VARCHAR(100) DEFAULT 'النظام',
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (debit_account_id) REFERENCES chart_of_accounts(id),
        FOREIGN KEY (credit_account_id) REFERENCES chart_of_accounts(id)
      )
    `)
    
    // إضافة فهارس
    await query(`
      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_entry_date ON payment_vouchers(entry_date);
      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_status ON payment_vouchers(status);
      CREATE INDEX IF NOT EXISTS idx_payment_vouchers_payee_type ON payment_vouchers(payee_type);
    `)
    
  } catch (error) {
    console.error('خطأ في إنشاء جدول سندات الصرف:', error)
  }
}

// دالة لتوليد رقم سند جديد
async function generateVoucherNumber(): Promise<string> {
  const result = await query(`
    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number
    FROM journal_entries
    WHERE entry_number ~ '^PV[0-9]+$'
  `)
  const nextNumber = result.rows[0]?.next_number || 1
  return `PV${String(nextNumber).padStart(6, '0')}`
}

// دالة لتوليد رقم قيد يومية جديد
async function generateJournalEntryNumber(): Promise<string> {
  const result = await query(`
    SELECT COALESCE(MAX(CAST(SUBSTRING(entry_number FROM 3) AS INTEGER)), 0) + 1 as next_number
    FROM journal_entries
    WHERE entry_number ~ '^JE[0-9]+$'
  `)
  const nextNumber = result.rows[0].next_number
  return `JE${nextNumber.toString().padStart(6, '0')}`
}

// دالة لحفظ السند في القيود اليومية
async function createJournalEntryFromVoucher(voucher: any, voucherType: 'receipt' | 'payment') {
  try {
    // ضمان وجود جدول القيود اليومية
    await query(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id SERIAL PRIMARY KEY,
        entry_number VARCHAR(50) UNIQUE NOT NULL,
        entry_date DATE NOT NULL,
        description TEXT NOT NULL,
        total_debit DECIMAL(15,2) NOT NULL DEFAULT 0,
        total_credit DECIMAL(15,2) NOT NULL DEFAULT 0,
        status VARCHAR(20) DEFAULT 'draft',
        created_by VARCHAR(100) DEFAULT 'النظام',
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        voucher_type VARCHAR(20),
        voucher_id INTEGER,
        voucher_number VARCHAR(50)
      )
    `)

    await query(`
      CREATE TABLE IF NOT EXISTS journal_entry_details (
        id SERIAL PRIMARY KEY,
        journal_entry_id INTEGER REFERENCES journal_entries(id) ON DELETE CASCADE,
        account_id INTEGER,
        account_name VARCHAR(255),
        debit_amount DECIMAL(15,2) DEFAULT 0,
        credit_amount DECIMAL(15,2) DEFAULT 0,
        description TEXT,
        line_order INTEGER DEFAULT 1
      )
    `)

    // توليد رقم قيد جديد
    const entryNumber = await generateJournalEntryNumber()

    // إنشاء القيد الرئيسي
    const journalEntry = await query(`
      INSERT INTO journal_entries (
        entry_number, entry_date, description, total_debit, total_credit,
        status, created_by, voucher_type, voucher_id, voucher_number
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING id
    `, [
      entryNumber,
      voucher.entry_date,
      `${voucherType === 'receipt' ? 'سند قبض' : 'سند صرف'} رقم ${voucher.entry_number} - ${voucher.description}`,
      voucher.amount,
      voucher.amount,
      'draft',
      'النظام',
      voucherType,
      voucher.id,
      voucher.entry_number
    ])

    const journalEntryId = journalEntry.rows[0].id

    // الحصول على أسماء الحسابات
    const debitAccount = await query(`
      SELECT account_name FROM chart_of_accounts WHERE id = $1
    `, [voucher.debit_account_id])

    const creditAccount = await query(`
      SELECT account_name FROM chart_of_accounts WHERE id = $1
    `, [voucher.credit_account_id])

    // إنشاء السطر المدين
    await query(`
      INSERT INTO journal_entry_details (
        journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      journalEntryId,
      voucher.debit_account_id,
      debitAccount.rows[0]?.account_name || 'حساب غير معروف',
      voucher.amount,
      0,
      `${voucherType === 'receipt' ? 'استلام من' : 'دفع إلى'} ${voucher.payee_name}`,
      1
    ])

    // إنشاء السطر الدائن
    await query(`
      INSERT INTO journal_entry_details (
        journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
    `, [
      journalEntryId,
      voucher.credit_account_id,
      creditAccount.rows[0]?.account_name || 'حساب غير معروف',
      0,
      voucher.amount,
      `${voucherType === 'receipt' ? 'إيراد من' : 'مصروف لـ'} ${voucher.payee_name}`,
      2
    ])

    
    return entryNumber

  } catch (error) {
    console.error('❌ خطأ في إنشاء القيد اليومية:', error)
    throw error
  }
}

// GET - جلب سندات الصرف
export async function GET(request: NextRequest) {
  try {
    await ensurePaymentVouchersTable()
    
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')
    const dateFrom = searchParams.get('dateFrom')
    const dateTo = searchParams.get('dateTo')
    const search = searchParams.get('search')
    
    const offset = (page - 1) * limit
    
    let sql = `
      SELECT
        je.*,
        (SELECT COUNT(*) FROM journal_entry_details WHERE journal_entry_id = je.id) as details_count
      FROM journal_entries je
      WHERE je.entry_type = 'payment'
    `

    const params: any[] = []
    let paramIndex = 1

    // تصفية حسب الحالة
    if (status && status !== 'all') {
      sql += ` AND je.status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    // تصفية حسب التاريخ
    if (dateFrom) {
      sql += ` AND je.entry_date >= $${paramIndex}`
      params.push(dateFrom)
      paramIndex++
    }

    if (dateTo) {
      sql += ` AND je.entry_date <= $${paramIndex}`
      params.push(dateTo)
      paramIndex++
    }

    // البحث في النص
    if (search) {
      sql += ` AND (je.party_name ILIKE $${paramIndex} OR je.description ILIKE $${paramIndex} OR je.entry_number ILIKE $${paramIndex})`
      params.push(`%${search}%`)
      paramIndex++
    }

    // ترتيب وتحديد العدد
    sql += ` ORDER BY je.entry_date DESC, je.id DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`
    params.push(limit, offset)

    const result = await query(sql, params)

    // جلب تفاصيل كل سند وتحويل البيانات للتوافق مع الواجهة
    const vouchers = []
    for (const row of result.rows) {
      // جلب تفاصيل القيد
      const detailsResult = await query(`
        SELECT * FROM journal_entry_details
        WHERE journal_entry_id = $1
        ORDER BY line_order
      `, [row.id])

      // العثور على الحساب المدين والدائن
      const debitDetail = detailsResult.rows.find(d => parseFloat(d.debit_amount) > 0)
      const creditDetail = detailsResult.rows.find(d => parseFloat(d.credit_amount) > 0)

      vouchers.push({
        id: row.id,
        entry_number: row.entry_number,
        voucher_number: row.entry_number,
        entry_date: row.entry_date,
        voucher_date: row.entry_date,
        amount: parseFloat(row.total_debit || 0),
        total_debit: parseFloat(row.total_debit || 0),
        payee_name: row.party_name,
        payee_type: row.party_type,
        beneficiary_name: row.party_name,
        beneficiary_type: row.party_type,
        description: row.description,
        reference_number: row.reference_number,
        status: row.status,
        debit_account_id: debitDetail?.account_id,
        credit_account_id: creditDetail?.account_id,
        debit_account_name: debitDetail?.account_name,
        debit_account_code: debitDetail?.account_code,
        credit_account_name: creditDetail?.account_name,
        credit_account_code: creditDetail?.account_code,
        currency_id: 1, // افتراضي
        payment_method_id: 1, // افتراضي
        cost_center_id: null,
        case_id: null,
        service_id: null,
        created_by: row.created_by,
        created_date: row.created_date,
        details: detailsResult.rows
      })
    }

    // حساب العدد الإجمالي
    let countSql = `SELECT COUNT(*) as total FROM journal_entries je WHERE je.entry_type = 'payment'`
    const countParams: any[] = []
    let countParamIndex = 1

    if (status && status !== 'all') {
      countSql += ` AND je.status = $${countParamIndex}`
      countParams.push(status)
      countParamIndex++
    }

    if (dateFrom) {
      countSql += ` AND je.entry_date >= $${countParamIndex}`
      countParams.push(dateFrom)
      countParamIndex++
    }

    if (dateTo) {
      countSql += ` AND je.entry_date <= $${countParamIndex}`
      countParams.push(dateTo)
      countParamIndex++
    }

    if (search) {
      countSql += ` AND (je.party_name ILIKE $${countParamIndex} OR je.description ILIKE $${countParamIndex} OR je.entry_number ILIKE $${countParamIndex})`
      countParams.push(`%${search}%`)
    }

    const countResult = await query(countSql, countParams)
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      success: true,
      vouchers: vouchers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      total,
      message: 'تم جلب سندات الصرف بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب سندات الصرف:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب سندات الصرف'
    }, { status: 500 })
  }
}

// POST - إضافة سند صرف جديد
export async function POST(request: NextRequest) {
  try {
    await ensurePaymentVouchersTable()
    
    const body = await request.json()
    console.log('📥 بيانات سند الصرف المستلمة:', body)
    
    const {
      entry_date,
      payee_name,
      payee_type = 'external',
      beneficiary_name,
      beneficiary_type,
      debit_account_id,
      credit_account_id,
      amount,
      description,
      reference_number,
      status = 'draft'
    } = body

    // دعم كلا من payee و beneficiary للتوافق مع النماذج المختلفة
    const finalPayeeName = payee_name || beneficiary_name
    const finalPayeeType = beneficiary_type || payee_type || 'external'

    // التحقق من البيانات المطلوبة
    if (!entry_date || !finalPayeeName || !debit_account_id || !credit_account_id || !amount) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة',
        details: 'يجب توفير: تاريخ السند، اسم المستفيد، الحساب المدين، الحساب الدائن، والمبلغ'
      }, { status: 400 })
    }

    // توليد رقم سند جديد
    const entry_number = await generateVoucherNumber()

    // إنشاء القيد في الهيكل الموحد
    const result = await query(`
      INSERT INTO journal_entries (
        entry_number, entry_type, entry_date, description,
        party_name, party_type, reference_number,
        total_debit, total_credit, status, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `, [
      entry_number, 'payment', entry_date, description,
      finalPayeeName, finalPayeeType, reference_number,
      amount, amount, status, 'النظام'
    ])

    const newEntry = result.rows[0]
    const journalEntryId = newEntry.id

    // الحصول على بيانات الحسابات
    const debitAccount = await query(`
      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1
    `, [debit_account_id])

    const creditAccount = await query(`
      SELECT account_name, account_code FROM chart_of_accounts WHERE id = $1
    `, [credit_account_id])

    // إضافة السطر المدين
    await query(`
      INSERT INTO journal_entry_details (
        journal_entry_id, account_id, account_name, account_code,
        debit_amount, credit_amount, description, line_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      journalEntryId,
      debit_account_id,
      debitAccount.rows[0]?.account_name || 'حساب غير معروف',
      debitAccount.rows[0]?.account_code || '',
      amount,
      0,
      `دفع إلى ${finalPayeeName}`,
      1
    ])

    // إضافة السطر الدائن
    await query(`
      INSERT INTO journal_entry_details (
        journal_entry_id, account_id, account_name, account_code,
        debit_amount, credit_amount, description, line_order
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    `, [
      journalEntryId,
      credit_account_id,
      creditAccount.rows[0]?.account_name || 'حساب غير معروف',
      creditAccount.rows[0]?.account_code || '',
      0,
      amount,
      `مصروف لـ ${finalPayeeName}`,
      2
    ])

    

    // تحويل البيانات للتوافق مع الواجهة
    const voucher = {
      id: newEntry.id,
      entry_number: newEntry.entry_number,
      entry_date: newEntry.entry_date,
      payee_name: newEntry.party_name,
      payee_type: newEntry.party_type,
      amount: newEntry.total_debit,
      description: newEntry.description,
      reference_number: newEntry.reference_number,
      status: newEntry.status,
      created_by: newEntry.created_by,
      created_date: newEntry.created_date
    }

    return NextResponse.json({
      success: true,
      voucher,
      message: `تم إنشاء سند الصرف ${voucher.entry_number} بنجاح`
    })

  } catch (error) {
    console.error('❌ خطأ في إنشاء سند الصرف:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء سند الصرف',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
