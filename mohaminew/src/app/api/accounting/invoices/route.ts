import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // جلب الفواتير مع معلومات العملاء
    const result = await query(`
      SELECT 
        ar.*,
        c.name as client_name,
        e.name as entity_name
      FROM ar
      LEFT JOIN entity e ON ar.entity_id = e.id
      LEFT JOIN company c ON e.id = c.entity_id
      ORDER BY ar.transdate DESC, ar.id DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset])

    // عدد الفواتير الإجمالي
    const countResult = await query('SELECT COUNT(*) as total FROM ar')
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching invoices:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الفواتير' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      invnumber,
      transdate,
      entity_id,
      taxincluded = false,
      amount,
      netamount,
      duedate,
      invoice = true,
      notes,
      curr = 'SAR',
      employee_id,
      department_id,
      items = []
    } = body

    // التحقق من البيانات المطلوبة
    if (!invnumber || !transdate || !amount) {
      return NextResponse.json(
        { success: false, error: 'رقم الفاتورة والتاريخ والمبلغ مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار رقم الفاتورة
    const existingInvoice = await query(
      'SELECT id FROM ar WHERE invnumber = $1',
      [invnumber]
    )

    if (existingInvoice.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'رقم الفاتورة موجود مسبقاً' },
        { status: 400 }
      )
    }

    await query('BEGIN')

    try {
      // إنشاء الفاتورة
      const invoiceResult = await query(`
        INSERT INTO ar (
          invnumber, transdate, entity_id, taxincluded, amount, 
          netamount, duedate, invoice, notes, curr, employee_id, 
          department_id, approved
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
        RETURNING *
      `, [
        invnumber, transdate, entity_id, taxincluded, amount,
        netamount || amount, duedate, invoice, notes, curr,
        employee_id, department_id, false
      ])

      const invoiceId = invoiceResult.rows[0].id

      // إدراج عناصر الفاتورة
      for (const item of items) {
        if (item.description && item.qty && item.sellprice) {
          await query(`
            INSERT INTO invoice (
              trans_id, parts_id, description, qty, sellprice, 
              fxsellprice, discount, unit, notes
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          `, [
            invoiceId, item.parts_id, item.description, item.qty,
            item.sellprice, item.sellprice, item.discount || 0,
            item.unit, item.notes
          ])
        }
      }

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم إنشاء الفاتورة بنجاح',
        data: invoiceResult.rows[0]
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error creating invoice:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء الفاتورة' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      invnumber,
      transdate,
      entity_id,
      taxincluded,
      amount,
      netamount,
      paid,
      datepaid,
      duedate,
      notes,
      approved,
      on_hold
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      )
    }

    // تحديث الفاتورة
    const result = await query(`
      UPDATE ar 
      SET 
        invnumber = COALESCE($2, invnumber),
        transdate = COALESCE($3, transdate),
        entity_id = COALESCE($4, entity_id),
        taxincluded = COALESCE($5, taxincluded),
        amount = COALESCE($6, amount),
        netamount = COALESCE($7, netamount),
        paid = COALESCE($8, paid),
        datepaid = $9,
        duedate = COALESCE($10, duedate),
        notes = COALESCE($11, notes),
        approved = COALESCE($12, approved),
        on_hold = COALESCE($13, on_hold)
      WHERE id = $1
      RETURNING *
    `, [
      id, invnumber, transdate, entity_id, taxincluded,
      amount, netamount, paid, datepaid, duedate, notes,
      approved, on_hold
    ])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الفاتورة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الفاتورة بنجاح',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating invoice:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الفاتورة' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الفاتورة مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من أن الفاتورة غير معتمدة وغير مدفوعة
    const invoiceCheck = await query(
      'SELECT approved, paid, amount FROM ar WHERE id = $1',
      [id]
    )

    if (invoiceCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الفاتورة غير موجودة' },
        { status: 404 }
      )
    }

    const invoice = invoiceCheck.rows[0]
    if (invoice.approved) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف فاتورة معتمدة' },
        { status: 400 }
      )
    }

    if (invoice.paid > 0) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف فاتورة مدفوعة جزئياً أو كلياً' },
        { status: 400 }
      )
    }

    await query('BEGIN')

    try {
      // حذف عناصر الفاتورة
      await query('DELETE FROM invoice WHERE trans_id = $1', [id])
      
      // حذف الفاتورة
      await query('DELETE FROM ar WHERE id = $1', [id])

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم حذف الفاتورة بنجاح'
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error deleting invoice:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الفاتورة' },
      { status: 500 }
    )
  }
}
