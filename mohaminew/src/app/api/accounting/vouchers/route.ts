import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      reference,
      transdate,
      description,
      person_id,
      notes,
      journal_entries
    } = body

    // التحقق من البيانات المطلوبة
    if (!reference || !transdate || !journal_entries || journal_entries.length < 2) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة ناقصة' },
        { status: 400 }
      )
    }

    // التحقق من توازن القيود
    const totalDebit = journal_entries.reduce((sum: number, entry: any) => sum + (entry.debit_amount || 0), 0)
    const totalCredit = journal_entries.reduce((sum: number, entry: any) => sum + (entry.credit_amount || 0), 0)
    
    if (Math.abs(totalDebit - totalCredit) > 0.01) {
      return NextResponse.json(
        { success: false, error: 'القيود غير متوازنة' },
        { status: 400 }
      )
    }

    await query('BEGIN')

    try {
      // إنشاء المعاملة الرئيسية
      const glResult = await query(`
        INSERT INTO gl (reference, description, transdate, person_id, notes, approved)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id
      `, [reference, description, transdate, person_id, notes, false])

      const glId = glResult.rows[0].id

      // إدراج القيود التفصيلية
      for (const entry of journal_entries) {
        if (entry.account_id && (entry.debit_amount > 0 || entry.credit_amount > 0)) {
          const amount = entry.debit_amount > 0 ? entry.debit_amount : -entry.credit_amount
          
          await query(`
            INSERT INTO acc_trans (trans_id, chart_id, amount, transdate, memo, approved)
            VALUES ($1, $2, $3, $4, $5, $6)
          `, [glId, entry.account_id, amount, transdate, entry.memo, false])
        }
      }

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم حفظ السند بنجاح',
        data: { id: glId, reference }
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error saving voucher:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حفظ السند' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // جلب السندات مع تفاصيلها
    const result = await query(`
      SELECT 
        gl.*,
        COUNT(at.entry_id) as entries_count,
        SUM(CASE WHEN at.amount > 0 THEN at.amount ELSE 0 END) as total_debit,
        SUM(CASE WHEN at.amount < 0 THEN ABS(at.amount) ELSE 0 END) as total_credit
      FROM gl
      LEFT JOIN acc_trans at ON gl.id = at.trans_id
      GROUP BY gl.id
      ORDER BY gl.transdate DESC, gl.id DESC
      LIMIT $1 OFFSET $2
    `, [limit, offset])

    // عدد السندات الإجمالي
    const countResult = await query('SELECT COUNT(*) as total FROM gl')
    const total = parseInt(countResult.rows[0].total)

    return NextResponse.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching vouchers:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب السندات' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      reference,
      description,
      transdate,
      person_id,
      notes,
      approved
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف السند مطلوب' },
        { status: 400 }
      )
    }

    // تحديث السند
    const result = await query(`
      UPDATE gl 
      SET 
        reference = COALESCE($2, reference),
        description = COALESCE($3, description),
        transdate = COALESCE($4, transdate),
        person_id = $5,
        notes = COALESCE($6, notes),
        approved = COALESCE($7, approved),
        approved_at = CASE WHEN $7 = true THEN CURRENT_TIMESTAMP ELSE approved_at END
      WHERE id = $1
      RETURNING *
    `, [id, reference, description, transdate, person_id, notes, approved])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'السند غير موجود' },
        { status: 404 }
      )
    }

    // إذا تم اعتماد السند، اعتمد جميع القيود المرتبطة
    if (approved) {
      await query(`
        UPDATE acc_trans 
        SET approved = true 
        WHERE trans_id = $1
      `, [id])
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث السند بنجاح',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating voucher:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث السند' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف السند مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من أن السند غير معتمد
    const voucherCheck = await query(
      'SELECT approved FROM gl WHERE id = $1',
      [id]
    )

    if (voucherCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'السند غير موجود' },
        { status: 404 }
      )
    }

    if (voucherCheck.rows[0].approved) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف سند معتمد' },
        { status: 400 }
      )
    }

    await query('BEGIN')

    try {
      // حذف القيود التفصيلية
      await query('DELETE FROM acc_trans WHERE trans_id = $1', [id])
      
      // حذف السند الرئيسي
      await query('DELETE FROM gl WHERE id = $1', [id])

      await query('COMMIT')

      return NextResponse.json({
        success: true,
        message: 'تم حذف السند بنجاح'
      })

    } catch (error) {
      await query('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('Error deleting voucher:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف السند' },
      { status: 500 }
    )
  }
}
