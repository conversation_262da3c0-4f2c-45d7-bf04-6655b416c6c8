import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, format, dateFrom, dateTo } = body

    // جلب بيانات التقرير
    const reportResponse = await fetch(`${request.nextUrl.origin}/api/accounting/reports/${type}?from=${dateFrom}&to=${dateTo}`)
    const reportData = await reportResponse.json()

    if (!reportData.success) {
      return NextResponse.json(
        { success: false, error: 'فشل في جلب بيانات التقرير' },
        { status: 500 }
      )
    }

    if (format === 'pdf') {
      return await exportToPDF(type, reportData.data, dateFrom, dateTo)
    } else if (format === 'excel') {
      return await exportToExcel(type, reportData.data, dateFrom, dateTo)
    } else {
      return NextResponse.json(
        { success: false, error: 'تنسيق التصدير غير مدعوم' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Error exporting report:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تصدير التقرير' },
      { status: 500 }
    )
  }
}

async function exportToPDF(type: string, data: any[], dateFrom: string, dateTo: string) {
  // هذا مثال بسيط - في التطبيق الحقيقي ستحتاج لمكتبة مثل puppeteer أو jsPDF
  const htmlContent = generateHTMLReport(type, data, dateFrom, dateTo)
  
  // تحويل HTML إلى PDF (يتطلب مكتبة إضافية)
  // const pdf = await generatePDFFromHTML(htmlContent)
  
  // للآن، سنرجع HTML كـ PDF بسيط
  const pdfBuffer = Buffer.from(htmlContent, 'utf-8')
  
  return new NextResponse(pdfBuffer, {
    headers: {
      'Content-Type': 'application/pdf',
      'Content-Disposition': `attachment; filename="${type}_${dateFrom}_${dateTo}.pdf"`
    }
  })
}

async function exportToExcel(type: string, data: any[], dateFrom: string, dateTo: string) {
  // هذا مثال بسيط - في التطبيق الحقيقي ستحتاج لمكتبة مثل exceljs
  let csvContent = generateCSVReport(type, data, dateFrom, dateTo)
  
  const csvBuffer = Buffer.from(csvContent, 'utf-8')
  
  return new NextResponse(csvBuffer, {
    headers: {
      'Content-Type': 'application/vnd.ms-excel',
      'Content-Disposition': `attachment; filename="${type}_${dateFrom}_${dateTo}.csv"`
    }
  })
}

function generateHTMLReport(type: string, data: any[], dateFrom: string, dateTo: string): string {
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  let reportTitle = ''
  let tableHeaders = ''
  let tableRows = ''

  switch (type) {
    case 'trial_balance':
      reportTitle = 'ميزان المراجعة'
      tableHeaders = `
        <tr>
          <th>رمز الحساب</th>
          <th>اسم الحساب</th>
          <th>مدين</th>
          <th>دائن</th>
        </tr>
      `
      tableRows = data.map(item => `
        <tr>
          <td>${item.account_code}</td>
          <td>${item.account_name}</td>
          <td>${item.debit_balance > 0 ? formatCurrency(item.debit_balance) : '-'}</td>
          <td>${item.credit_balance > 0 ? formatCurrency(item.credit_balance) : '-'}</td>
        </tr>
      `).join('')
      break

    case 'income_statement':
      reportTitle = 'قائمة الدخل'
      tableHeaders = `
        <tr>
          <th>رمز الحساب</th>
          <th>اسم الحساب</th>
          <th>المبلغ</th>
        </tr>
      `
      tableRows = data.map(item => `
        <tr>
          <td>${item.account_code}</td>
          <td>${item.account_name}</td>
          <td class="${item.category === 'I' ? 'revenue' : 'expense'}">${formatCurrency(item.amount)}</td>
        </tr>
      `).join('')
      break

    case 'balance_sheet':
      reportTitle = 'الميزانية العمومية'
      tableHeaders = `
        <tr>
          <th>رمز الحساب</th>
          <th>اسم الحساب</th>
          <th>المبلغ</th>
        </tr>
      `
      tableRows = data.map(item => `
        <tr>
          <td>${item.account_code}</td>
          <td>${item.account_name}</td>
          <td class="${item.category}">${formatCurrency(item.amount)}</td>
        </tr>
      `).join('')
      break

    case 'cash_flow':
      reportTitle = 'قائمة التدفق النقدي'
      tableHeaders = `
        <tr>
          <th>التاريخ</th>
          <th>الوصف</th>
          <th>التدفق الداخل</th>
          <th>التدفق الخارج</th>
          <th>الرصيد</th>
        </tr>
      `
      tableRows = data.map(item => `
        <tr>
          <td>${formatDate(item.date)}</td>
          <td>${item.description}</td>
          <td class="inflow">${item.inflow > 0 ? formatCurrency(item.inflow) : '-'}</td>
          <td class="outflow">${item.outflow > 0 ? formatCurrency(item.outflow) : '-'}</td>
          <td class="balance">${formatCurrency(item.balance)}</td>
        </tr>
      `).join('')
      break
  }

  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <title>${reportTitle}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .period { font-size: 14px; color: #666; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background-color: #f5f5f5; font-weight: bold; }
        .revenue { color: #16a085; }
        .expense { color: #e74c3c; }
        .inflow { color: #27ae60; }
        .outflow { color: #e74c3c; }
        .balance { font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="title">${reportTitle}</div>
        <div class="period">من ${formatDate(dateFrom)} إلى ${formatDate(dateTo)}</div>
      </div>
      <table>
        <thead>${tableHeaders}</thead>
        <tbody>${tableRows}</tbody>
      </table>
    </body>
    </html>
  `
}

function generateCSVReport(type: string, data: any[], dateFrom: string, dateTo: string): string {
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA')
  }

  let csvContent = ''

  switch (type) {
    case 'trial_balance':
      csvContent = 'رمز الحساب,اسم الحساب,مدين,دائن\n'
      csvContent += data.map(item => 
        `${item.account_code},"${item.account_name}",${item.debit_balance},${item.credit_balance}`
      ).join('\n')
      break

    case 'income_statement':
      csvContent = 'رمز الحساب,اسم الحساب,الفئة,المبلغ\n'
      csvContent += data.map(item => 
        `${item.account_code},"${item.account_name}",${item.category},${item.amount}`
      ).join('\n')
      break

    case 'balance_sheet':
      csvContent = 'رمز الحساب,اسم الحساب,الفئة,المبلغ\n'
      csvContent += data.map(item => 
        `${item.account_code},"${item.account_name}",${item.category},${item.amount}`
      ).join('\n')
      break

    case 'cash_flow':
      csvContent = 'التاريخ,الوصف,التدفق الداخل,التدفق الخارج,الرصيد\n'
      csvContent += data.map(item => 
        `${item.date},"${item.description}",${item.inflow},${item.outflow},${item.balance}`
      ).join('\n')
      break
  }

  return csvContent
}
