import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - كشف حساب
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const accountId = searchParams.get('account_id')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')

    if (!accountId) {
      return NextResponse.json({
        success: false,
        error: 'معرف الحساب مطلوب'
      }, { status: 400 })
    }

    // التحقق من وجود الحساب
    const accountResult = await query(`
      SELECT 
        id, account_code, account_name, account_type, account_nature,
        opening_balance, current_balance
      FROM chart_of_accounts 
      WHERE id = $1 AND is_active = true
    `, [accountId])

    if (accountResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الحساب غير موجود'
      }, { status: 404 })
    }

    const account = accountResult.rows[0]

    // بناء استعلام كشف الحساب
    let sql = `
      SELECT 
        jed.journal_entry_id,
        je.entry_number,
        je.entry_date,
        je.description as entry_description,
        jed.description as line_description,
        jed.debit_amount,
        jed.credit_amount,
        jed.line_number,
        c.currency_code,
        c.symbol as currency_symbol,
        pm.method_name as payment_method_name,
        je.voucher_type,
        je.payer_name,
        je.beneficiary_name,
        je.reference_number,
        je.created_date
      FROM journal_entry_details jed
      INNER JOIN journal_entries je ON jed.journal_entry_id = je.id
      LEFT JOIN currencies c ON jed.currency_id = c.id
      LEFT JOIN payment_methods pm ON jed.payment_method_id = pm.id
      WHERE jed.account_id = $1
    `

    const params: any[] = [accountId]
    let paramIndex = 2

    // تصفية حسب التاريخ
    if (dateFrom) {
      sql += ` AND je.entry_date >= $${paramIndex}`
      params.push(dateFrom)
      paramIndex++
    }

    if (dateTo) {
      sql += ` AND je.entry_date <= $${paramIndex}`
      params.push(dateTo)
      paramIndex++
    }

    sql += ` ORDER BY je.entry_date ASC, je.entry_number ASC, jed.line_number ASC`

    // تنفيذ الاستعلام
    const transactionsResult = await query(sql, params)
    const transactions = transactionsResult.rows

    // حساب الرصيد الجاري والإحصائيات
    let runningBalance = account.opening_balance || 0
    let totalDebit = 0
    let totalCredit = 0

    const processedTransactions = transactions.map(transaction => {
      const debitAmount = parseFloat(transaction.debit_amount) || 0
      const creditAmount = parseFloat(transaction.credit_amount) || 0

      totalDebit += debitAmount
      totalCredit += creditAmount

      // حساب الرصيد حسب طبيعة الحساب
      if (account.account_nature === 'مدين') {
        // الحسابات المدينة: المدين يزيد الرصيد، الدائن ينقص الرصيد
        runningBalance += debitAmount - creditAmount
      } else {
        // الحسابات الدائنة: الدائن يزيد الرصيد، المدين ينقص الرصيد
        runningBalance += creditAmount - debitAmount
      }

      // تحديد نوع المعاملة والوصف
      let transactionType = 'قيد يومي'
      let description = transaction.line_description || transaction.entry_description

      if (transaction.voucher_type === 'receipt') {
        transactionType = 'سند قبض'
        if (transaction.payer_name) {
          description = `${transactionType} من ${transaction.payer_name} - ${description}`
        }
      } else if (transaction.voucher_type === 'payment') {
        transactionType = 'سند صرف'
        if (transaction.beneficiary_name) {
          description = `${transactionType} إلى ${transaction.beneficiary_name} - ${description}`
        }
      }

      return {
        ...transaction,
        transaction_type: transactionType,
        full_description: description,
        running_balance: runningBalance,
        debit_amount: debitAmount,
        credit_amount: creditAmount
      }
    })

    // حساب الرصيد النهائي
    const finalBalance = runningBalance
    const balanceType = account.account_nature === 'مدين' 
      ? (finalBalance >= 0 ? 'مدين' : 'دائن')
      : (finalBalance >= 0 ? 'دائن' : 'مدين')

    return NextResponse.json({
      success: true,
      data: {
        account: {
          id: account.id,
          code: account.account_code,
          name: account.account_name,
          type: account.account_type,
          nature: account.account_nature,
          opening_balance: account.opening_balance || 0
        },
        period: {
          date_from: dateFrom,
          date_to: dateTo
        },
        transactions: processedTransactions,
        summary: {
          total_debit: totalDebit,
          total_credit: totalCredit,
          final_balance: Math.abs(finalBalance),
          balance_type: balanceType,
          transactions_count: transactions.length
        }
      },
      message: 'تم جلب كشف الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في جلب كشف الحساب:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب كشف الحساب',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
