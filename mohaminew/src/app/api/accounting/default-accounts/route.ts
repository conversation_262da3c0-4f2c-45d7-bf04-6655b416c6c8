import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب الحسابات الأساسية
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        ma.id,
        ma.account_name,
        ma.account_code,
        ma.chart_account_id,
        ma.is_required,
        ma.description,
        coa.account_code as linked_account_code,
        coa.account_name as linked_account_name,
        coa.account_type as linked_account_type
      FROM main_accounts ma
      LEFT JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      ORDER BY 
        CASE 
          WHEN ma.account_name LIKE '%عملاء%' OR ma.account_name LIKE '%العملاء%' THEN 1
          WHEN ma.account_name LIKE '%موظف%' OR ma.account_name LIKE '%الموظفين%' THEN 2
          WHEN ma.account_name LIKE '%مدين%' OR ma.account_name LIKE '%المدينين%' THEN 3
          ELSE 4
        END,
        ma.id
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching default accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الحسابات الأساسية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث ربط حساب أساسي واحد
export async function PUT(request: NextRequest) {
  try {
    const { id, chart_account_id } = await request.json()

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب الأساسي مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود الحساب الأساسي
    const mainAccountCheck = await query(
      'SELECT id, account_name FROM main_accounts WHERE id = $1',
      [id]
    )

    if (mainAccountCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب الأساسي غير موجود' },
        { status: 404 }
      )
    }

    let accountCode = null

    // إذا تم تحديد حساب للربط
    if (chart_account_id) {
      // التحقق من وجود الحساب في دليل الحسابات
      const chartAccountCheck = await query(
        'SELECT account_code, account_name FROM chart_of_accounts WHERE id = $1',
        [chart_account_id]
      )

      if (chartAccountCheck.rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'الحساب المحدد غير موجود في دليل الحسابات' },
          { status: 404 }
        )
      }

      accountCode = chartAccountCheck.rows[0].account_code
    }

    // تحديث الربط
    await query(`
      UPDATE main_accounts 
      SET 
        chart_account_id = $1, 
        account_code = $2,
        updated_date = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [chart_account_id || null, accountCode, id])

    return NextResponse.json({
      success: true,
      message: 'تم تحديث ربط الحساب الأساسي بنجاح',
      data: {
        id,
        chart_account_id,
        account_code: accountCode
      }
    })
  } catch (error) {
    console.error('Error updating default account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث ربط الحساب الأساسي' },
      { status: 500 }
    )
  }
}

// POST - تحديث عدة حسابات أساسية
export async function POST(request: NextRequest) {
  try {
    const { updates } = await request.json()

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { success: false, error: 'بيانات التحديث مطلوبة' },
        { status: 400 }
      )
    }

    const results = []

    // تحديث كل حساب
    for (const update of updates) {
      const { id, chart_account_id } = update

      if (!id) continue

      try {
        let accountCode = null

        // إذا تم تحديد حساب للربط
        if (chart_account_id) {
          const chartAccount = await query(
            'SELECT account_code FROM chart_of_accounts WHERE id = $1',
            [chart_account_id]
          )
          
          if (chartAccount.rows.length > 0) {
            accountCode = chartAccount.rows[0].account_code
          }
        }

        // تحديث الربط
        await query(`
          UPDATE main_accounts 
          SET 
            chart_account_id = $1, 
            account_code = $2,
            updated_date = CURRENT_TIMESTAMP
          WHERE id = $3
        `, [chart_account_id || null, accountCode, id])

        results.push({
          id,
          success: true,
          chart_account_id,
          account_code: accountCode
        })
      } catch (error) {
        console.error(`Error updating account ${id}:`, error)
        results.push({
          id,
          success: false,
          error: error.message
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    return NextResponse.json({
      success: failCount === 0,
      message: `تم تحديث ${successCount} حساب بنجاح${failCount > 0 ? ` وفشل في ${failCount} حساب` : ''}`,
      results,
      summary: {
        total: results.length,
        success: successCount,
        failed: failCount
      }
    })
  } catch (error) {
    console.error('Error updating default accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الحسابات الأساسية' },
      { status: 500 }
    )
  }
}