import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 إصلاح ربط السندات بالحسابات...')
    
    const results = []
    
    // 1. البحث عن الحسابات المناسبة في دليل الحسابات
    console.log('🔍 البحث عن الحسابات المناسبة...')
    
    // البحث عن حساب الصندوق (للمدين في سندات القبض)
    const cashAccount = await query(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name ILIKE '%صندوق%' OR account_name ILIKE '%نقدية%' OR account_code LIKE '1101%'
      ORDER BY account_code 
      LIMIT 1
    `)
    
    // البحث عن حساب الإيرادات (للدائن في سندات القبض)
    const revenueAccount = await query(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name ILIKE '%إيراد%' OR account_name ILIKE '%أتعاب%' OR account_code LIKE '41%'
      ORDER BY account_code 
      LIMIT 1
    `)
    
    // البحث عن حساب المصروفات (للمدين في سندات الصرف)
    const expenseAccount = await query(`
      SELECT id, account_name, account_code 
      FROM chart_of_accounts 
      WHERE account_name ILIKE '%مصروف%' OR account_name ILIKE '%راتب%' OR account_code LIKE '51%'
      ORDER BY account_code 
      LIMIT 1
    `)
    
    let cashAccountId = null
    let revenueAccountId = null
    let expenseAccountId = null
    
    if (cashAccount.rows.length > 0) {
      cashAccountId = cashAccount.rows[0].id
      results.push(`✅ تم العثور على حساب الصندوق: ${cashAccount.rows[0].account_name} (${cashAccount.rows[0].account_code})`)
    } else {
      // إنشاء حساب الصندوق
      const newCashAccount = await query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_nature, 
          account_level, parent_id, is_control_account, allow_transactions
        ) VALUES ('1101001', 'الصندوق الرئيسي', 'أصول', 'مدين', 4, 
          (SELECT id FROM chart_of_accounts WHERE account_code = '1101' LIMIT 1), 
          false, true)
        RETURNING id, account_name, account_code
      `)
      cashAccountId = newCashAccount.rows[0].id
      results.push(`✅ تم إنشاء حساب الصندوق: ${newCashAccount.rows[0].account_name} (${newCashAccount.rows[0].account_code})`)
    }
    
    if (revenueAccount.rows.length > 0) {
      revenueAccountId = revenueAccount.rows[0].id
      results.push(`✅ تم العثور على حساب الإيرادات: ${revenueAccount.rows[0].account_name} (${revenueAccount.rows[0].account_code})`)
    } else {
      // إنشاء حساب الإيرادات
      const newRevenueAccount = await query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_nature, 
          account_level, parent_id, is_control_account, allow_transactions
        ) VALUES ('4101001', 'إيرادات الأتعاب القانونية', 'إيرادات', 'دائن', 4, 
          (SELECT id FROM chart_of_accounts WHERE account_code = '4101' LIMIT 1), 
          false, true)
        RETURNING id, account_name, account_code
      `)
      revenueAccountId = newRevenueAccount.rows[0].id
      results.push(`✅ تم إنشاء حساب الإيرادات: ${newRevenueAccount.rows[0].account_name} (${newRevenueAccount.rows[0].account_code})`)
    }
    
    if (expenseAccount.rows.length > 0) {
      expenseAccountId = expenseAccount.rows[0].id
      results.push(`✅ تم العثور على حساب المصروفات: ${expenseAccount.rows[0].account_name} (${expenseAccount.rows[0].account_code})`)
    } else {
      // إنشاء حساب المصروفات
      const newExpenseAccount = await query(`
        INSERT INTO chart_of_accounts (
          account_code, account_name, account_type, account_nature, 
          account_level, parent_id, is_control_account, allow_transactions
        ) VALUES ('5101001', 'مصروفات الرواتب', 'مصروفات', 'مدين', 4, 
          (SELECT id FROM chart_of_accounts WHERE account_code = '5101' LIMIT 1), 
          false, true)
        RETURNING id, account_name, account_code
      `)
      expenseAccountId = newExpenseAccount.rows[0].id
      results.push(`✅ تم إنشاء حساب المصروفات: ${newExpenseAccount.rows[0].account_name} (${newExpenseAccount.rows[0].account_code})`)
    }
    
    // 2. تحديث سندات القبض (إجباري)
    console.log('📝 تحديث سندات القبض...')
    const updateReceiptVouchers = await query(`
      UPDATE receipt_vouchers
      SET
        debit_account_id = $1,
        credit_account_id = $2
    `, [cashAccountId, revenueAccountId])

    results.push(`✅ تم تحديث ${updateReceiptVouchers.rowCount} سند قبض`)

    // 3. تحديث سندات الصرف (إجباري)
    console.log('📝 تحديث سندات الصرف...')
    const updatePaymentVouchers = await query(`
      UPDATE payment_vouchers
      SET
        debit_account_id = $1,
        credit_account_id = $2
    `, [expenseAccountId, cashAccountId])

    results.push(`✅ تم تحديث ${updatePaymentVouchers.rowCount} سند صرف`)
    
    // 4. اختبار السندات بعد التحديث
    console.log('🧪 اختبار السندات بعد التحديث...')
    
    const receiptTest = await query(`
      SELECT 
        rv.*,
        da.account_name as debit_account_name,
        da.account_code as debit_account_code,
        ca.account_name as credit_account_name,
        ca.account_code as credit_account_code
      FROM receipt_vouchers rv
      LEFT JOIN chart_of_accounts da ON rv.debit_account_id = da.id
      LEFT JOIN chart_of_accounts ca ON rv.credit_account_id = ca.id
      LIMIT 3
    `)
    
    results.push(`✅ اختبار سندات القبض: ${receiptTest.rows.length} سند مع أسماء الحسابات`)
    
    const paymentTest = await query(`
      SELECT 
        pv.*,
        da.account_name as debit_account_name,
        da.account_code as debit_account_code,
        ca.account_name as credit_account_name,
        ca.account_code as credit_account_code
      FROM payment_vouchers pv
      LEFT JOIN chart_of_accounts da ON pv.debit_account_id = da.id
      LEFT JOIN chart_of_accounts ca ON pv.credit_account_id = ca.id
      LIMIT 3
    `)
    
    results.push(`✅ اختبار سندات الصرف: ${paymentTest.rows.length} سند مع أسماء الحسابات`)
    
    // 5. تحديث أرصدة الحسابات
    console.log('💰 تحديث أرصدة الحسابات...')
    
    // حساب إجمالي سندات القبض
    const receiptTotal = await query(`
      SELECT COALESCE(SUM(amount), 0) as total 
      FROM receipt_vouchers 
      WHERE status = 'approved'
    `)
    
    // حساب إجمالي سندات الصرف
    const paymentTotal = await query(`
      SELECT COALESCE(SUM(amount), 0) as total 
      FROM payment_vouchers 
      WHERE status = 'approved'
    `)
    
    // تحديث رصيد الصندوق
    const cashBalance = parseFloat(receiptTotal.rows[0].total) - parseFloat(paymentTotal.rows[0].total)
    await query(`
      UPDATE chart_of_accounts 
      SET current_balance = $1 
      WHERE id = $2
    `, [cashBalance, cashAccountId])
    
    results.push(`💰 تم تحديث رصيد الصندوق: ${cashBalance} ر.س`)
    
    // 6. إحصائيات نهائية
    const stats = {
      receiptVouchers: receiptTest.rows.length,
      paymentVouchers: paymentTest.rows.length,
      cashAccountId,
      revenueAccountId,
      expenseAccountId,
      cashBalance
    }
    
    console.log('🎉 تم إصلاح ربط السندات بالحسابات بنجاح!')
    
    return NextResponse.json({
      success: true,
      message: 'تم إصلاح ربط السندات بالحسابات بنجاح',
      data: {
        results,
        stats,
        accounts: {
          cash: cashAccount.rows[0] || { id: cashAccountId, account_name: 'الصندوق الرئيسي', account_code: '1101001' },
          revenue: revenueAccount.rows[0] || { id: revenueAccountId, account_name: 'إيرادات الأتعاب القانونية', account_code: '4101001' },
          expense: expenseAccount.rows[0] || { id: expenseAccountId, account_name: 'مصروفات الرواتب', account_code: '5101001' }
        }
      }
    })
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح ربط السندات:', error)
    return NextResponse.json({
      success: false,
      message: 'فشل في إصلاح ربط السندات',
      error: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
