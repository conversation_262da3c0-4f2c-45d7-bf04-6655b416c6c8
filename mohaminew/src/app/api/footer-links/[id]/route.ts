import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/database'

// GET - جلب رابط واحد بالمعرف
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const result = await query(
      'SELECT * FROM footer_links WHERE id = $1',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الرابط غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
  } catch (error) {
    console.error('خطأ في جلب الرابط:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الرابط' },
      { status: 500 }
    )
  }
}

// PUT - تحديث رابط
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const {
      category,
      name,
      href,
      sort_order,
      is_active
    } = body

    if (!category || !name || !href) {
      return NextResponse.json(
        { success: false, error: 'الفئة والاسم والرابط مطلوبة' },
        { status: 400 }
      )
    }

    // التحقق من وجود الرابط
    const existingLink = await query(
      'SELECT id FROM footer_links WHERE id = $1',
      [id]
    )

    if (existingLink.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الرابط غير موجود' },
        { status: 404 }
      )
    }

    const result = await query(`
      UPDATE footer_links SET
        category = $1,
        name = $2,
        href = $3,
        sort_order = $4,
        is_active = $5,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `, [category, name, href, sort_order, is_active, id])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الرابط بنجاح'
    })
  } catch (error) {
    console.error('خطأ في تحديث الرابط:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الرابط' },
      { status: 500 }
    )
  }
}

// DELETE - حذف رابط
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // التحقق من وجود الرابط
    const existingLink = await query(
      'SELECT id FROM footer_links WHERE id = $1',
      [id]
    )

    if (existingLink.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الرابط غير موجود' },
        { status: 404 }
      )
    }

    await query('DELETE FROM footer_links WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الرابط بنجاح'
    })
  } catch (error) {
    console.error('خطأ في حذف الرابط:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الرابط' },
      { status: 500 }
    )
  }
}
