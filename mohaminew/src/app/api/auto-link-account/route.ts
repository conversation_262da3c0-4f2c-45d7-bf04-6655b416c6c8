import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// POST - ربط حساب تلقائياً لسجل جديد
export async function POST(request: NextRequest) {
  try {
    const { table_name, record_id, record_name } = await request.json()

    if (!table_name || !record_id) {
      return NextResponse.json(
        { success: false, error: 'اسم الجدول ومعرف السجل مطلوبان' },
        { status: 400 }
      )
    }

    // البحث عن إعدادات الربط للجدول
    const settingsResult = await query(`
      SELECT 
        als.*,
        coa.account_code,
        coa.account_name
      FROM account_linking_settings als
      JOIN chart_of_accounts coa ON als.default_main_account_id = coa.id
      WHERE als.table_name = $1 AND als.is_enabled = true AND als.auto_create_on_insert = true
    `, [table_name])

    if (settingsResult.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا توجد إعدادات ربط مفعلة لهذا الجدول'
      })
    }

    const settings = settingsResult.rows[0]

    // التحقق من عدم وجود رابط مسبق
    const existingLink = await query(
      'SELECT id FROM account_sub_links WHERE main_account_id = $1 AND linked_table = $2 AND linked_record_id = $3',
      [settings.default_main_account_id, table_name, record_id]
    )

    if (existingLink.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'هذا السجل مربوط بحساب مسبقاً'
      })
    }

    // إنشاء كود الحساب الفرعي
    const subAccountCode = `${settings.account_code}-${record_id.toString().padStart(4, '0')}`
    
    // إنشاء اسم الحساب الفرعي
    let subAccountName = `${settings.table_display_name}: `
    if (record_name) {
      subAccountName += record_name
    } else {
      // جلب اسم السجل من الجدول
      try {
        const recordResult = await query(
          `SELECT ${settings.name_field} as name FROM ${table_name} WHERE ${settings.id_field} = $1`,
          [record_id]
        )
        if (recordResult.rows.length > 0) {
          subAccountName += recordResult.rows[0].name
        } else {
          subAccountName += `سجل رقم ${record_id}`
        }
      } catch (error) {
        subAccountName += `سجل رقم ${record_id}`
      }
    }

    // إنشاء الرابط الفرعي
    const linkResult = await query(`
      INSERT INTO account_sub_links
      (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [
      settings.default_main_account_id,
      table_name,
      record_id,
      subAccountCode,
      subAccountName,
      'النظام'
    ])

    return NextResponse.json({
      success: true,
      message: 'تم ربط الحساب تلقائياً بنجاح',
      data: {
        link: linkResult.rows[0],
        main_account: {
          id: settings.default_main_account_id,
          name: settings.account_name,
          code: settings.account_code
        }
      }
    })
  } catch (error) {
    console.error('Error auto-linking account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في الربط التلقائي للحساب' },
      { status: 500 }
    )
  }
}

// GET - التحقق من إمكانية الربط التلقائي لجدول معين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const table_name = searchParams.get('table_name')

    if (!table_name) {
      return NextResponse.json(
        { success: false, error: 'اسم الجدول مطلوب' },
        { status: 400 }
      )
    }

    // البحث عن إعدادات الربط
    const settingsResult = await query(`
      SELECT 
        als.*,
        coa.account_code,
        coa.account_name
      FROM account_linking_settings als
      LEFT JOIN chart_of_accounts coa ON als.default_main_account_id = coa.id
      WHERE als.table_name = $1
    `, [table_name])

    const isEnabled = settingsResult.rows.length > 0 && 
                     settingsResult.rows[0].is_enabled && 
                     settingsResult.rows[0].auto_create_on_insert

    return NextResponse.json({
      success: true,
      data: {
        is_enabled: isEnabled,
        settings: settingsResult.rows.length > 0 ? settingsResult.rows[0] : null
      }
    })
  } catch (error) {
    console.error('Error checking auto-link settings:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في التحقق من إعدادات الربط' },
      { status: 500 }
    )
  }
}

// PUT - تحديث رابط حساب موجود
export async function PUT(request: NextRequest) {
  try {
    const { link_id, sub_account_name, notes } = await request.json()

    if (!link_id) {
      return NextResponse.json(
        { success: false, error: 'معرف الرابط مطلوب' },
        { status: 400 }
      )
    }

    // تحديث الرابط
    const result = await query(`
      UPDATE account_sub_links 
      SET 
        sub_account_name = COALESCE($1, sub_account_name),
        notes = COALESCE($2, notes)
      WHERE id = $3
      RETURNING *
    `, [sub_account_name, notes, link_id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الرابط غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الرابط بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error updating account link:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الرابط' },
      { status: 500 }
    )
  }
}

// DELETE - حذف رابط حساب
export async function DELETE(request: NextRequest) {
  try {
    const { link_id } = await request.json()

    if (!link_id) {
      return NextResponse.json(
        { success: false, error: 'معرف الرابط مطلوب' },
        { status: 400 }
      )
    }

    // حذف الرابط
    const result = await query(
      'DELETE FROM account_sub_links WHERE id = $1 RETURNING *',
      [link_id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الرابط غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الرابط بنجاح'
    })
  } catch (error) {
    console.error('Error deleting account link:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الرابط' },
      { status: 500 }
    )
  }
}
