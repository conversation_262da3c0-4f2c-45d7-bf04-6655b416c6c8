import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// POST - تسجيل دخول العميل (نسخة مبسطة للاختبار)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    let { username, password } = body

    // تنظيف البيانات من المسافات الإضافية
    username = username?.trim()
    password = password?.trim()

    console.log('Login attempt:', { username, password })

    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم وكلمة المرور مطلوبان' },
        { status: 400 }
      )
    }

    // للاختبار: قبول البيانات التجريبية مباشرة
    if (username === 'demo_client' && password === 'password123') {
      // جلب بيانات العميل من قاعدة البيانات
      const clientQuery = `
        SELECT 
          cpa.*,
          c.name as client_name,
          c.phone,
          c.email as client_email
        FROM client_portal_accounts cpa
        JOIN clients c ON cpa.client_id = c.id
        WHERE cpa.username = $1 AND cpa.is_active = true
      `
      
      const result = await query(clientQuery, [username])

      if (result.rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'العميل غير موجود' },
          { status: 404 }
        )
      }

      const client = result.rows[0]

      // إنشاء token بسيط
      const token = `simple_token_${client.client_id}_${Date.now()}`
      const sessionToken = `session_${client.client_id}_${Date.now()}`

      // إرجاع البيانات (بدون كلمة المرور)
      const { password_hash, ...clientData } = client

      return NextResponse.json({
        success: true,
        data: {
          client: clientData,
          token,
          sessionToken
        },
        message: 'تم تسجيل الدخول بنجاح'
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' },
        { status: 401 }
      )
    }

  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error)
    return NextResponse.json(
      { success: false, error: 'خطأ في الخادم' },
      { status: 500 }
    )
  }
}