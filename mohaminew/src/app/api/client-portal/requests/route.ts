import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب طلبات العميل
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const clientId = 1 // مؤقت للاختبار
    const status = searchParams.get('status')
    const requestType = searchParams.get('request_type')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    let whereConditions = ['client_id = $1']
    let queryParams: any[] = [clientId]
    let paramIndex = 2

    // تصفية حسب الحالة
    if (status) {
      whereConditions.push(`status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    // تصفية حسب نوع الطلب
    if (requestType) {
      whereConditions.push(`request_type = $${paramIndex}`)
      queryParams.push(requestType)
      paramIndex++
    }

    const whereClause = whereConditions.join(' AND ')

    const requestsQuery = `
      SELECT 
        id, request_type, title, description, priority, status,
        created_date, updated_at, response, response_date,
        COUNT(*) OVER() as total_count
      FROM client_requests
      WHERE ${whereClause}
      ORDER BY created_date DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(requestsQuery, queryParams)

    return NextResponse.json({
      success: true,
      data: {
        requests: result.rows,
        totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,
        currentPage: page,
        totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0
      }
    })

  } catch (error) {
    console.error('خطأ في جلب طلبات العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الطلبات' },
      { status: 500 }
    )
  }
}

// POST - إنشاء طلب جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      request_type,
      title,
      description,
      priority = 'medium',
      case_id
    } = body

    const clientId = 1 // مؤقت للاختبار

    // التحقق من البيانات المطلوبة
    if (!request_type || !title || !description) {
      return NextResponse.json(
        { success: false, error: 'نوع الطلب والعنوان والوصف مطلوبة' },
        { status: 400 }
      )
    }

    // إدراج الطلب الجديد
    const insertQuery = `
      INSERT INTO client_requests (
        client_id, case_id, request_type, title, description, priority, status
      ) VALUES (
        $1, $2, $3, $4, $5, $6, 'pending'
      ) RETURNING *
    `

    const values = [
      clientId,
      case_id || null,
      request_type,
      title,
      description,
      priority
    ]

    const result = await query(insertQuery, values)

    // إنشاء إشعار للإدارة (اختياري)
    await query(`
      INSERT INTO admin_notifications (
        title, message, type, related_table, related_id
      ) VALUES (
        $1, $2, 'info', 'client_requests', $3
      )
    `, [
      'طلب جديد من العميل',
      `تم إنشاء طلب جديد: ${title}`,
      result.rows[0].id
    ]).catch(err => console.log('تحذير: فشل في إنشاء إشعار الإدارة:', err))

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إنشاء الطلب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء الطلب:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء الطلب' },
      { status: 500 }
    )
  }
}

// PUT - تحديث طلب (للعميل: إلغاء فقط)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, action } = body

    const clientId = 1 // مؤقت للاختبار

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الطلب مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من ملكية الطلب
    const ownershipCheck = await query(
      'SELECT id, status FROM client_requests WHERE id = $1 AND client_id = $2',
      [id, clientId]
    )

    if (ownershipCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الطلب غير موجود أو غير مملوك لك' },
        { status: 404 }
      )
    }

    const currentRequest = ownershipCheck.rows[0]

    if (action === 'cancel') {
      // يمكن للعميل إلغاء الطلب فقط إذا كان في حالة انتظار
      if (currentRequest.status !== 'pending') {
        return NextResponse.json(
          { success: false, error: 'لا يمكن إلغاء الطلب في هذه الحالة' },
          { status: 400 }
        )
      }

      const updateQuery = `
        UPDATE client_requests 
        SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
        RETURNING *
      `

      const result = await query(updateQuery, [id])

      return NextResponse.json({
        success: true,
        data: result.rows[0],
        message: 'تم إلغاء الطلب بنجاح'
      })
    }

    return NextResponse.json(
      { success: false, error: 'إجراء غير مدعوم' },
      { status: 400 }
    )

  } catch (error) {
    console.error('خطأ في تحديث الطلب:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الطلب' },
      { status: 500 }
    )
  }
}