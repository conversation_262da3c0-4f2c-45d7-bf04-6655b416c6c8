import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// التحقق من صحة رمز العميل
async function verifyClientToken(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }

  const token = authHeader.substring(7)
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any
    if (decoded.type !== 'client') {
      return null
    }
    return decoded
  } catch (error) {
    return null
  }
}

// GET - جلب وثائق العميل
export async function GET(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const caseId = searchParams.get('case_id')
    const category = searchParams.get('category')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    let whereConditions = ['d.client_id = $1', 'd.is_active = true']
    let queryParams: any[] = [clientToken.clientId]
    let paramIndex = 2

    // تصفية حسب القضية
    if (caseId) {
      whereConditions.push(`d.case_id = $${paramIndex}`)
      queryParams.push(parseInt(caseId))
      paramIndex++
    }

    // تصفية حسب الفئة
    if (category) {
      whereConditions.push(`d.category = $${paramIndex}`)
      queryParams.push(category)
      paramIndex++
    }

    const whereClause = whereConditions.join(' AND ')

    // الاستعلام الرئيسي
    const documentsQuery = `
      SELECT 
        d.id,
        d.title,
        d.description,
        d.file_name,
        d.file_size,
        d.file_type,
        d.category,
        d.subcategory,
        d.tags,
        d.created_date,
        d.access_level,
        i.title as case_title,
        i.case_number,
        COUNT(*) OVER() as total_count
      FROM documents d
      LEFT JOIN issues i ON d.case_id = i.id
      WHERE ${whereClause}
      ORDER BY d.created_date DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(documentsQuery, queryParams)

    // جلب إحصائيات الفئات
    const categoriesQuery = `
      SELECT category, COUNT(*) as count
      FROM documents 
      WHERE client_id = $1 AND is_active = true
      GROUP BY category
      ORDER BY count DESC
    `
    const categoriesResult = await query(categoriesQuery, [clientToken.clientId])

    return NextResponse.json({
      success: true,
      data: result.rows,
      pagination: {
        currentPage: page,
        totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,
        totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0
      },
      categories: categoriesResult.rows
    })

  } catch (error) {
    console.error('خطأ في جلب وثائق العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الوثائق' },
      { status: 500 }
    )
  }
}

// POST - رفع وثيقة جديدة من العميل
export async function POST(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      fileName,
      filePath,
      fileSize,
      fileType,
      mimeType,
      caseId,
      category,
      tags
    } = body

    // التحقق من البيانات المطلوبة
    if (!title || !fileName || !filePath) {
      return NextResponse.json(
        { success: false, error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // التحقق من ملكية القضية إذا تم تحديدها
    if (caseId) {
      const caseCheck = await query(
        'SELECT id FROM issues WHERE id = $1 AND client_id = $2',
        [caseId, clientToken.clientId]
      )
      
      if (caseCheck.rows.length === 0) {
        return NextResponse.json(
          { success: false, error: 'القضية غير موجودة أو غير مصرح بالوصول إليها' },
          { status: 403 }
        )
      }
    }

    const insertQuery = `
      INSERT INTO documents (
        title, description, file_name, file_path, file_size, file_type, mime_type,
        case_id, client_id, category, tags, access_level, uploaded_by
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
      ) RETURNING *
    `

    const values = [
      title,
      description,
      fileName,
      filePath,
      fileSize,
      fileType,
      mimeType,
      caseId || null,
      clientToken.clientId,
      category || 'client_upload',
      tags || [],
      'private', // الوثائق المرفوعة من العميل خاصة بشكل افتراضي
      null // العميل لا يحتاج user_id
    ]

    const result = await query(insertQuery, values)

    // إنشاء إشعار للمحامين المسؤولين
    if (caseId) {
      await query(`
        INSERT INTO client_notifications (client_id, case_id, title, message, type)
        VALUES ($1, $2, $3, $4, $5)
      `, [
        clientToken.clientId,
        caseId,
        'تم رفع وثيقة جديدة',
        `قام العميل برفع وثيقة جديدة: ${title}`,
        'info'
      ])
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم رفع الوثيقة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في رفع الوثيقة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في رفع الوثيقة' },
      { status: 500 }
    )
  }
}

// GET - تحميل وثيقة محددة
export async function PUT(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { documentId } = body

    if (!documentId) {
      return NextResponse.json(
        { success: false, error: 'معرف الوثيقة مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من ملكية الوثيقة
    const documentQuery = `
      SELECT 
        d.*,
        i.title as case_title,
        i.case_number
      FROM documents d
      LEFT JOIN issues i ON d.case_id = i.id
      WHERE d.id = $1 AND d.client_id = $2 AND d.is_active = true
    `

    const result = await query(documentQuery, [documentId, clientToken.clientId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الوثيقة غير موجودة أو غير مصرح بالوصول إليها' },
        { status: 404 }
      )
    }

    const document = result.rows[0]

    // تسجيل عملية التحميل (اختياري)
    await query(`
      INSERT INTO document_access_log (document_id, accessed_by_client, access_type, access_date)
      VALUES ($1, $2, 'download', CURRENT_TIMESTAMP)
    `, [documentId, clientToken.clientId])

    return NextResponse.json({
      success: true,
      data: document
    })

  } catch (error) {
    console.error('خطأ في تحميل الوثيقة:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحميل الوثيقة' },
      { status: 500 }
    )
  }
}