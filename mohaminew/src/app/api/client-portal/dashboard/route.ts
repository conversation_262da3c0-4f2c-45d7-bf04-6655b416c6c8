import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب بيانات لوحة تحكم العميل
export async function GET(request: NextRequest) {
  try {
    // في التطبيق الحقيقي، سيتم الحصول على معرف العميل من الجلسة
    const clientId = 1 // مؤقت للاختبار

    // جلب معلومات العميل
    const clientQuery = `
      SELECT c.*, cpa.username, cpa.email as portal_email, cpa.language, cpa.timezone
      FROM clients c
      LEFT JOIN client_portal_accounts cpa ON c.id = cpa.client_id
      WHERE c.id = $1
    `
    const clientResult = await query(clientQuery, [clientId])

    if (clientResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العميل غير موجود' },
        { status: 404 }
      )
    }

    const client = clientResult.rows[0]

    // جلب القضايا
    const casesQuery = `
      SELECT id, case_number, title, status, created_date, updated_at as last_update
      FROM issues
      WHERE client_id = $1
      ORDER BY updated_at DESC
      LIMIT 10
    `
    const casesResult = await query(casesQuery, [clientId])

    // جلب الوثائق
    const documentsQuery = `
      SELECT id, title, file_name, file_size, created_date, category
      FROM documents
      WHERE client_id = $1 AND is_active = true
      ORDER BY created_date DESC
      LIMIT 10
    `
    const documentsResult = await query(documentsQuery, [clientId])

    // جلب الإشعارات
    const notificationsQuery = `
      SELECT id, title, message, type, is_read, created_date
      FROM client_notifications
      WHERE client_id = $1
      ORDER BY created_date DESC
      LIMIT 20
    `
    const notificationsResult = await query(notificationsQuery, [clientId])

    // جلب الطلبات
    const requestsQuery = `
      SELECT id, title, status, priority, created_date, request_type
      FROM client_requests
      WHERE client_id = $1
      ORDER BY created_date DESC
      LIMIT 10
    `
    const requestsResult = await query(requestsQuery, [clientId])

    // جلب الفواتير
    const invoicesQuery = `
      SELECT id, invoice_number, total_amount, status, due_date, payment_status
      FROM invoices
      WHERE client_id = $1
      ORDER BY created_date DESC
      LIMIT 10
    `
    const invoicesResult = await query(invoicesQuery, [clientId])

    // تجميع البيانات
    const dashboardData = {
      profile: {
        name: client.name,
        email: client.portal_email || client.email,
        phone: client.phone,
        address: client.address,
        username: client.username,
        language: client.language || 'ar',
        timezone: client.timezone || 'Asia/Riyadh'
      },
      cases: casesResult.rows,
      documents: documentsResult.rows,
      notifications: notificationsResult.rows,
      requests: requestsResult.rows,
      invoices: invoicesResult.rows.map(invoice => ({
        ...invoice,
        status: invoice.payment_status // استخدام حالة الدفع كحالة رئيسية
      }))
    }

    return NextResponse.json({
      success: true,
      data: dashboardData
    })

  } catch (error) {
    console.error('خطأ في جلب بيانات لوحة التحكم:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب بيانات لوحة التحكم' },
      { status: 500 }
    )
  }
}