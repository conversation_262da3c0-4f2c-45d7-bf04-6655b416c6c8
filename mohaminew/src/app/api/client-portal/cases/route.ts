import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

// التحقق من صحة رمز العميل
async function verifyClientToken(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }

  const token = authHeader.substring(7)
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any
    if (decoded.type !== 'client') {
      return null
    }
    return decoded
  } catch (error) {
    return null
  }
}

// GET - جلب قضايا العميل
export async function GET(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    let whereConditions = ['i.client_id = $1']
    let queryParams: any[] = [clientToken.clientId]
    let paramIndex = 2

    // تصفية حسب الحالة
    if (status) {
      whereConditions.push(`i.status = $${paramIndex}`)
      queryParams.push(status)
      paramIndex++
    }

    const whereClause = whereConditions.join(' AND ')

    // الاستعلام الرئيسي
    const casesQuery = `
      SELECT 
        i.*,
        c.name as client_name,
        it.name as issue_type_name,
        COUNT(*) OVER() as total_count
      FROM issues i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN issue_types it ON i.issue_type_id = it.id
      WHERE ${whereClause}
      ORDER BY i.created_date DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(casesQuery, queryParams)

    // جلب آخر المتابعات لكل قضية
    const caseIds = result.rows.map(row => row.id)
    let followsData = []
    
    if (caseIds.length > 0) {
      const followsQuery = `
        SELECT DISTINCT ON (case_id) 
          case_id, description, created_date, status
        FROM follows 
        WHERE case_id = ANY($1)
        ORDER BY case_id, created_date DESC
      `
      const followsResult = await query(followsQuery, [caseIds])
      followsData = followsResult.rows
    }

    // دمج البيانات
    const casesWithFollows = result.rows.map(case_ => {
      const lastFollow = followsData.find(f => f.case_id === case_.id)
      return {
        ...case_,
        last_follow: lastFollow || null
      }
    })

    return NextResponse.json({
      success: true,
      data: casesWithFollows,
      pagination: {
        currentPage: page,
        totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,
        totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0
      }
    })

  } catch (error) {
    console.error('خطأ في جلب قضايا العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب القضايا' },
      { status: 500 }
    )
  }
}

// GET - جلب تفاصيل قضية محددة
export async function POST(request: NextRequest) {
  try {
    const clientToken = await verifyClientToken(request)
    if (!clientToken) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح بالوصول' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { caseId } = body

    if (!caseId) {
      return NextResponse.json(
        { success: false, error: 'معرف القضية مطلوب' },
        { status: 400 }
      )
    }

    // جلب تفاصيل القضية
    const caseQuery = `
      SELECT 
        i.*,
        c.name as client_name,
        c.phone as client_phone,
        c.email as client_email,
        it.name as issue_type_name,
        it.description as issue_type_description
      FROM issues i
      LEFT JOIN clients c ON i.client_id = c.id
      LEFT JOIN issue_types it ON i.issue_type_id = it.id
      WHERE i.id = $1 AND i.client_id = $2
    `

    const caseResult = await query(caseQuery, [caseId, clientToken.clientId])

    if (caseResult.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'القضية غير موجودة أو غير مصرح بالوصول إليها' },
        { status: 404 }
      )
    }

    const caseData = caseResult.rows[0]

    // جلب المتابعات
    const followsQuery = `
      SELECT * FROM follows 
      WHERE case_id = $1 
      ORDER BY created_date DESC
    `
    const followsResult = await query(followsQuery, [caseId])

    // جلب الوثائق المرتبطة
    const documentsQuery = `
      SELECT 
        id, title, file_name, category, created_date, file_size
      FROM documents 
      WHERE case_id = $1 AND is_active = true
      ORDER BY created_date DESC
    `
    const documentsResult = await query(documentsQuery, [caseId])

    // جلب الحركات المالية
    const movementsQuery = `
      SELECT * FROM movements 
      WHERE case_id = $1 
      ORDER BY date DESC
    `
    const movementsResult = await query(movementsQuery, [caseId])

    return NextResponse.json({
      success: true,
      data: {
        case: caseData,
        follows: followsResult.rows,
        documents: documentsResult.rows,
        movements: movementsResult.rows
      }
    })

  } catch (error) {
    console.error('خطأ في جلب تفاصيل القضية:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب تفاصيل القضية' },
      { status: 500 }
    )
  }
}