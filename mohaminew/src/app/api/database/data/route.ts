import { NextRequest, NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'mohammi',
  user: 'postgres',
  password: process.env.DB_PASSWORD || 'your_password_here',
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tableName = searchParams.get('table')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    if (!tableName) {
      return NextResponse.json({
        success: false,
        error: 'اسم الجدول مطلوب'
      }, { status: 400 })
    }
    
    // التحقق من أن اسم الجدول آمن
    const validTableName = /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(tableName)
    if (!validTableName) {
      return NextResponse.json({
        success: false,
        error: 'اسم جدول غير صحيح'
      }, { status: 400 })
    }
    
    const client = await pool.connect()
    
    // استعلام آمن باستخدام identifier quoting
    const query = `SELECT * FROM ${tableName} ORDER BY 1 LIMIT $1 OFFSET $2`
    
    const result = await client.query(query, [limit, offset])
    client.release()
    
    return NextResponse.json({
      success: true,
      data: result.rows,
      count: result.rowCount
    })
    
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب بيانات الجدول'
    }, { status: 500 })
  }
}
