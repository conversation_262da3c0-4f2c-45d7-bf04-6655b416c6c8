import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const result = await query(
      'SELECT * FROM announcements WHERE is_active = $1 ORDER BY created_date DESC',
      [true]
    )

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching announcements:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch announcements' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { announcement_1, announcement_2, announcement_3, announcement_4, is_active } = body

    const result = await query(
      `INSERT INTO announcements (announcement_1, announcement_2, announcement_3, announcement_4, is_active, created_date, updated_date)
       VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
       RETURNING *`,
      [announcement_1, announcement_2, announcement_3, announcement_4, is_active]
    )

    return NextResponse.json({
      success: true,
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating announcement:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create announcement' },
      { status: 500 }
    )
  }
}
