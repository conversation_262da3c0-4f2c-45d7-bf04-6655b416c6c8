import { NextResponse } from 'next/server'
import { Pool } from 'pg'


// التحقق من وجود كلمة مرور قاعدة البيانات
if (!process.env.DB_PASSWORD) {
  throw new Error('DB_PASSWORD environment variable is required')
}

export async function POST() {
  let pool: Pool | null = null
  
  try {
    const dbConfig = {
      host: 'localhost',
      port: 5432,
      database: 'moham<PERSON>',
      user: 'postgres',
      password: process.env.DB_PASSWORD || 'your_password_here',
      connectionTimeoutMillis: 5000,
      idleTimeoutMillis: 30000,
      max: 20
    }

    pool = new Pool(dbConfig)
    
    

    // حذف الجداول الموجودة
    const dropTables = [
      'DROP TABLE IF EXISTS follows CASCADE',
      'DROP TABLE IF EXISTS movements CASCADE', 
      'DROP TABLE IF EXISTS journal_entries CASCADE',
      'DROP TABLE IF EXISTS issues CASCADE',
      'DROP TABLE IF EXISTS issue_types CASCADE',
      'DROP TABLE IF EXISTS lineages CASCADE',
      'DROP TABLE IF EXISTS courts CASCADE',
      'DROP TABLE IF EXISTS branches CASCADE',
      'DROP TABLE IF EXISTS governorates CASCADE',
      'DROP TABLE IF EXISTS companies CASCADE',
      'DROP TABLE IF EXISTS users CASCADE',
      'DROP TABLE IF EXISTS employees CASCADE',
      'DROP TABLE IF EXISTS clients CASCADE'
    ]

    for (const dropQuery of dropTables) {
      await pool.query(dropQuery)
    }

    

    // إنشاء الجداول من جديد
    
    // جدول العملاء/الموكلين
    await pool.query(`
      CREATE TABLE clients (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        id_number VARCHAR(20) UNIQUE,
        status VARCHAR(20) DEFAULT 'active',
        cases_count INTEGER DEFAULT 0,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول الموظفين
    await pool.query(`
      CREATE TABLE employees (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        position VARCHAR(255),
        department VARCHAR(255),
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        id_number VARCHAR(20) UNIQUE,
        salary DECIMAL(10,2),
        hire_date DATE,
        status VARCHAR(20) DEFAULT 'active',
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول أنواع القضايا
    await pool.query(`
      CREATE TABLE issue_types (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        color VARCHAR(50),
        cases_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول القضايا
    await pool.query(`
      CREATE TABLE issues (
        id SERIAL PRIMARY KEY,
        case_number VARCHAR(50) UNIQUE NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        client_id INTEGER REFERENCES clients(id),
        client_name VARCHAR(255),
        court_name VARCHAR(255),
        issue_type_id INTEGER REFERENCES issue_types(id),
        issue_type VARCHAR(255),
        status VARCHAR(50) DEFAULT 'pending',
        amount DECIMAL(12,2),
        next_hearing DATE,
        notes TEXT,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول النسب المالية
    await pool.query(`
      CREATE TABLE lineages (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        management_share DECIMAL(5,2) DEFAULT 0.00,
        court_share DECIMAL(5,2) DEFAULT 0.00,
        commission_share DECIMAL(5,2) DEFAULT 0.00,
        other_share DECIMAL(5,2) DEFAULT 0.00,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول المتابعات
    await pool.query(`
      CREATE TABLE follows (
        id SERIAL PRIMARY KEY,
        case_id INTEGER REFERENCES issues(id),
        case_number VARCHAR(50),
        case_title VARCHAR(255),
        client_name VARCHAR(255),
        follow_type VARCHAR(50),
        description TEXT,
        due_date DATE,
        status VARCHAR(50) DEFAULT 'pending',
        priority VARCHAR(20) DEFAULT 'medium',
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول الحركات المالية
    await pool.query(`
      CREATE TABLE movements (
        id SERIAL PRIMARY KEY,
        case_id INTEGER REFERENCES issues(id),
        case_number VARCHAR(50),
        movement_type VARCHAR(50),
        amount DECIMAL(12,2),
        description TEXT,
        movement_date DATE DEFAULT CURRENT_DATE,
        status VARCHAR(50) DEFAULT 'completed',
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // جدول المستخدمين
    await pool.query(`
      CREATE TABLE users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE,
        password_hash VARCHAR(255),
        role VARCHAR(50) DEFAULT 'user',
        status VARCHAR(20) DEFAULT 'active',
        last_login TIMESTAMP,
        created_date DATE DEFAULT CURRENT_DATE,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    

    // إضافة بيانات تجريبية
    
    // العملاء
    await pool.query(`
      INSERT INTO clients (name, phone, email, address, id_number, status) VALUES
      ('أحمد محمد علي', '01234567890', '<EMAIL>', 'الرياض - المملكة العربية السعودية', '1234567890', 'active'),
      ('فاطمة سعد العتيبي', '01987654321', '<EMAIL>', 'جدة - المملكة العربية السعودية', '1987654321', 'active'),
      ('خالد عبدالله الزهراني', '01555666777', '<EMAIL>', 'الدمام - المملكة العربية السعودية', '1555666777', 'active'),
      ('نورا أحمد المطيري', '01333444555', '<EMAIL>', 'مكة المكرمة - المملكة العربية السعودية', '1333444555', 'active'),
      ('عبدالرحمن سالم القرني', '01666777888', '<EMAIL>', 'المدينة المنورة - المملكة العربية السعودية', '1666777888', 'active')
    `)

    // الموظفين
    await pool.query(`
      INSERT INTO employees (name, position, department, phone, email, salary, hire_date, id_number, status) VALUES
      ('سارة أحمد المطيري', 'محامي أول', 'القسم القانوني', '01122334455', '<EMAIL>', 8000.00, '2023-01-15', '2234567891', 'active'),
      ('محمد علي الغامدي', 'محامي', 'القسم المدني', '01556677888', '<EMAIL>', 6500.00, '2023-03-20', '2987654322', 'active'),
      ('نورا سلمان القحطاني', 'سكرتيرة قانونية', 'الإدارة', '01444555666', '<EMAIL>', 4500.00, '2023-02-10', '2555666778', 'active'),
      ('عبدالله خالد العتيبي', 'محاسب', 'الشؤون المالية', '01777888999', '<EMAIL>', 5500.00, '2023-05-01', '2777888990', 'active')
    `)

    // أنواع القضايا
    await pool.query(`
      INSERT INTO issue_types (name, description, color) VALUES
      ('قضايا مدنية', 'القضايا المدنية والتجارية', '#3B82F6'),
      ('قضايا جنائية', 'القضايا الجنائية والعقوبات', '#EF4444'),
      ('قضايا أسرة', 'قضايا الأحوال الشخصية والأسرة', '#10B981'),
      ('قضايا عمالية', 'قضايا العمل والعمال', '#F59E0B'),
      ('قضايا إدارية', 'القضايا الإدارية والحكومية', '#8B5CF6'),
      ('قضايا تجارية', 'القضايا التجارية والشركات', '#06B6D4')
    `)

    // القضايا
    await pool.query(`
      INSERT INTO issues (case_number, title, description, client_name, court_name, issue_type, status, amount, next_hearing) VALUES
      ('2024/001', 'قضية مطالبة مالية', 'مطالبة بمبلغ مالي مستحق من شركة المقاولات', 'أحمد محمد علي', 'محكمة الرياض العامة', 'قضايا مدنية', 'جاري', 150000.00, '2024-08-15'),
      ('2024/002', 'قضية طلاق', 'دعوى طلاق للضرر والإهمال', 'فاطمة سعد العتيبي', 'محكمة جدة الشرعية', 'قضايا أسرة', 'منتهية', 0, NULL),
      ('2024/003', 'قضية عمالية', 'مطالبة بمستحقات عمالية ومكافأة نهاية الخدمة', 'خالد عبدالله الزهراني', 'لجنة المنازعات العمالية', 'قضايا عمالية', 'معلقة', 45000.00, '2024-09-01'),
      ('2024/004', 'قضية تعويض', 'قضية تعويض عن حادث مروري', 'نورا أحمد المطيري', 'محكمة مكة العامة', 'قضايا مدنية', 'جاري', 80000.00, '2024-08-25'),
      ('2024/005', 'قضية ميراث', 'قسمة تركة الوالد المتوفى', 'عبدالرحمن سالم القرني', 'محكمة المدينة الشرعية', 'قضايا أسرة', 'جاري', 200000.00, '2024-09-10')
    `)

    // النسب المالية
    await pool.query(`
      INSERT INTO lineages (name, management_share, court_share, commission_share, other_share) VALUES
      ('النسبة العامة', 60.00, 15.00, 20.00, 5.00),
      ('قضايا كبرى', 50.00, 20.00, 25.00, 5.00),
      ('قضايا بسيطة', 70.00, 10.00, 15.00, 5.00),
      ('قضايا عمالية', 65.00, 10.00, 20.00, 5.00)
    `)

    // المتابعات
    await pool.query(`
      INSERT INTO follows (case_number, case_title, client_name, follow_type, description, due_date, status, priority) VALUES
      ('2024/001', 'قضية مطالبة مالية', 'أحمد محمد علي', 'جلسة محكمة', 'الحضور لجلسة المرافعة النهائية', '2024-08-15', 'معلق', 'high'),
      ('2024/003', 'قضية عمالية', 'خالد عبدالله الزهراني', 'تقديم مستندات', 'تقديم المستندات المطلوبة للجنة', '2024-08-20', 'معلق', 'medium'),
      ('2024/004', 'قضية تعويض', 'نورا أحمد المطيري', 'فحص طبي', 'إجراء فحص طبي لتقدير الأضرار', '2024-08-18', 'معلق', 'medium'),
      ('2024/005', 'قضية ميراث', 'عبدالرحمن سالم القرني', 'جمع وثائق', 'جمع جميع الوثائق المطلوبة للقسمة', '2024-08-30', 'معلق', 'low')
    `)

    // المستخدمين
    await pool.query(`
      INSERT INTO users (username, email, role, status) VALUES
      ('admin', '<EMAIL>', 'admin', 'active'),
      ('manager', '<EMAIL>', 'manager', 'active'),
      ('lawyer1', '<EMAIL>', 'lawyer', 'active'),
      ('secretary', '<EMAIL>', 'user', 'active')
    `)

    

    return NextResponse.json({
      success: true,
      message: 'تمت إعادة تهيئة قاعدة البيانات وإضافة البيانات التجريبية بنجاح',
      details: 'تم إنشاء جميع الجداول وإضافة بيانات تجريبية شاملة للاختبار'
    })

  } catch (error) {
    console.error('خطأ في إعادة تهيئة قاعدة البيانات:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في إعادة تهيئة قاعدة البيانات: ' + error 
      },
      { status: 500 }
    )
  } finally {
    if (pool) {
      await pool.end()
    }
  }
}
