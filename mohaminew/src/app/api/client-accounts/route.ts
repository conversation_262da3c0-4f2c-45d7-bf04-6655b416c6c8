import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import bcrypt from 'bcryptjs'
import crypto from 'crypto'

// GET - جلب حسابات العملاء
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search')
    const isActive = searchParams.get('is_active')
    const isVerified = searchParams.get('is_verified')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    let whereConditions = ['1=1']
    let queryParams: any[] = []
    let paramIndex = 1

    // البحث في اسم المستخدم والبريد الإلكتروني
    if (search) {
      whereConditions.push(`(
        cpa.username ILIKE $${paramIndex} OR 
        cpa.email ILIKE $${paramIndex} OR 
        c.name ILIKE $${paramIndex}
      )`)
      queryParams.push(`%${search}%`)
      paramIndex++
    }

    // تصفية حسب الحالة النشطة
    if (isActive !== null) {
      whereConditions.push(`cpa.is_active = $${paramIndex}`)
      queryParams.push(isActive === 'true')
      paramIndex++
    }

    // تصفية حسب التحقق
    if (isVerified !== null) {
      whereConditions.push(`cpa.is_verified = $${paramIndex}`)
      queryParams.push(isVerified === 'true')
      paramIndex++
    }

    const whereClause = whereConditions.join(' AND ')

    // الاستعلام الرئيسي
    const accountsQuery = `
      SELECT 
        cpa.*,
        c.name as client_name,
        c.phone as client_phone,
        c.address as client_address,
        COUNT(*) OVER() as total_count
      FROM client_portal_accounts cpa
      LEFT JOIN clients c ON cpa.client_id = c.id
      WHERE ${whereClause}
      ORDER BY cpa.created_date DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `

    queryParams.push(limit, offset)
    const result = await query(accountsQuery, queryParams)

    // إحصائيات الحسابات
    const statsQuery = `
      SELECT 
        COUNT(*) as total_accounts,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_accounts,
        COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_accounts,
        COUNT(CASE WHEN last_login >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as recent_logins
      FROM client_portal_accounts cpa
      LEFT JOIN clients c ON cpa.client_id = c.id
      WHERE ${whereClause}
    `

    const statsResult = await query(statsQuery, queryParams.slice(0, -2))

    return NextResponse.json({
      success: true,
      data: {
        accounts: result.rows,
        totalCount: result.rows.length > 0 ? parseInt(result.rows[0].total_count) : 0,
        currentPage: page,
        totalPages: result.rows.length > 0 ? Math.ceil(parseInt(result.rows[0].total_count) / limit) : 0,
        statistics: statsResult.rows[0]
      }
    })

  } catch (error) {
    console.error('خطأ في جلب حسابات العملاء:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب حسابات العملاء' },
      { status: 500 }
    )
  }
}

// POST - إنشاء حساب عميل جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      clientId,
      username,
      email,
      password,
      language = 'ar',
      timezone = 'Asia/Riyadh',
      sendWelcomeEmail = true
    } = body

    // التحقق من البيانات المطلوبة
    if (!clientId || !username || !email || !password) {
      return NextResponse.json(
        { success: false, error: 'جميع البيانات المطلوبة يجب أن تكون موجودة' },
        { status: 400 }
      )
    }

    // التحقق من وجود العميل
    const clientCheck = await query('SELECT id, name FROM clients WHERE id = $1', [clientId])
    if (clientCheck.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'العميل غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم وجود حساب للعميل
    const existingAccountCheck = await query(
      'SELECT id FROM client_portal_accounts WHERE client_id = $1',
      [clientId]
    )
    if (existingAccountCheck.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'يوجد حساب للعميل بالفعل' },
        { status: 400 }
      )
    }

    // التحقق من عدم تكرار اسم المستخدم والبريد الإلكتروني
    const duplicateCheck = await query(
      'SELECT username, email FROM client_portal_accounts WHERE username = $1 OR email = $2',
      [username, email]
    )
    if (duplicateCheck.rows.length > 0) {
      const duplicate = duplicateCheck.rows[0]
      if (duplicate.username === username) {
        return NextResponse.json(
          { success: false, error: 'اسم المستخدم موجود بالفعل' },
          { status: 400 }
        )
      }
      if (duplicate.email === email) {
        return NextResponse.json(
          { success: false, error: 'البريد الإلكتروني موجود بالفعل' },
          { status: 400 }
        )
      }
    }

    // تشفير كلمة المرور
    const saltRounds = 12
    const passwordHash = await bcrypt.hash(password, saltRounds)

    // إنشاء رمز التحقق
    const verificationToken = crypto.randomBytes(32).toString('hex')

    // إنشاء الحساب
    const insertQuery = `
      INSERT INTO client_portal_accounts (
        client_id, username, email, password_hash, language, timezone,
        verification_token, notification_preferences
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8
      ) RETURNING id, username, email, is_active, is_verified, created_date
    `

    const defaultNotificationPrefs = {
      email_notifications: true,
      case_updates: true,
      document_uploads: true,
      appointment_reminders: true,
      invoice_notifications: true
    }

    const values = [
      clientId,
      username,
      email,
      passwordHash,
      language,
      timezone,
      verificationToken,
      JSON.stringify(defaultNotificationPrefs)
    ]

    const result = await query(insertQuery, values)
    const newAccount = result.rows[0]

    // إرسال بريد ترحيبي (محاكاة)
    if (sendWelcomeEmail) {
      console.log(`إرسال بريد ترحيبي إلى: ${email}`)
      console.log(`رابط التحقق: /verify-email?token=${verificationToken}`)
    }

    // إنشاء إشعار للعميل
    await query(`
      INSERT INTO client_notifications (client_id, title, message, type)
      VALUES ($1, $2, $3, $4)
    `, [
      clientId,
      'مرحباً بك في بوابة العملاء',
      'تم إنشاء حسابك بنجاح. يرجى التحقق من بريدك الإلكتروني لتفعيل الحساب.',
      'success'
    ])

    return NextResponse.json({
      success: true,
      data: newAccount,
      message: 'تم إنشاء الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إنشاء حساب العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء حساب العميل' },
      { status: 500 }
    )
  }
}

// PUT - تحديث حساب عميل
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      username,
      email,
      isActive,
      isVerified,
      language,
      timezone,
      notificationPreferences,
      resetPassword = false,
      newPassword
    } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب مطلوب' },
        { status: 400 }
      )
    }

    let updateFields = []
    let values = [id]
    let paramIndex = 2

    // تحديث اسم المستخدم
    if (username) {
      // التحقق من عدم تكرار اسم المستخدم
      const duplicateCheck = await query(
        'SELECT id FROM client_portal_accounts WHERE username = $1 AND id != $2',
        [username, id]
      )
      if (duplicateCheck.rows.length > 0) {
        return NextResponse.json(
          { success: false, error: 'اسم المستخدم موجود بالفعل' },
          { status: 400 }
        )
      }
      updateFields.push(`username = $${paramIndex}`)
      values.push(username)
      paramIndex++
    }

    // تحديث البريد الإلكتروني
    if (email) {
      // التحقق من عدم تكرار البريد الإلكتروني
      const duplicateCheck = await query(
        'SELECT id FROM client_portal_accounts WHERE email = $1 AND id != $2',
        [email, id]
      )
      if (duplicateCheck.rows.length > 0) {
        return NextResponse.json(
          { success: false, error: 'البريد الإلكتروني موجود بالفعل' },
          { status: 400 }
        )
      }
      updateFields.push(`email = $${paramIndex}`)
      values.push(email)
      paramIndex++
      
      // إعادة تعيين حالة التحقق إذا تم تغيير البريد الإلكتروني
      updateFields.push(`is_verified = false`)
      updateFields.push(`verification_token = $${paramIndex}`)
      values.push(crypto.randomBytes(32).toString('hex'))
      paramIndex++
    }

    // تحديث الحالة النشطة
    if (isActive !== undefined) {
      updateFields.push(`is_active = $${paramIndex}`)
      values.push(isActive)
      paramIndex++
    }

    // تحديث حالة التحقق
    if (isVerified !== undefined) {
      updateFields.push(`is_verified = $${paramIndex}`)
      values.push(isVerified)
      paramIndex++
      if (isVerified) {
        updateFields.push(`verification_token = NULL`)
      }
    }

    // تحديث اللغة
    if (language) {
      updateFields.push(`language = $${paramIndex}`)
      values.push(language)
      paramIndex++
    }

    // تحديث المنطقة الزمنية
    if (timezone) {
      updateFields.push(`timezone = $${paramIndex}`)
      values.push(timezone)
      paramIndex++
    }

    // تحديث إعدادات الإشعارات
    if (notificationPreferences) {
      updateFields.push(`notification_preferences = $${paramIndex}`)
      values.push(JSON.stringify(notificationPreferences))
      paramIndex++
    }

    // إعادة تعيين كلمة المرور
    if (resetPassword && newPassword) {
      const passwordHash = await bcrypt.hash(newPassword, 12)
      updateFields.push(`password_hash = $${paramIndex}`)
      values.push(passwordHash)
      paramIndex++
      updateFields.push(`reset_token = NULL`)
      updateFields.push(`reset_token_expires = NULL`)
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لا توجد بيانات للتحديث' },
        { status: 400 }
      )
    }

    updateFields.push(`updated_at = CURRENT_TIMESTAMP`)

    const updateQuery = `
      UPDATE client_portal_accounts 
      SET ${updateFields.join(', ')}
      WHERE id = $1
      RETURNING id, username, email, is_active, is_verified, language, timezone, updated_at
    `

    const result = await query(updateQuery, values)

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث حساب العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث حساب العميل' },
      { status: 500 }
    )
  }
}

// DELETE - حذف حساب عميل
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف الحساب مطلوب' },
        { status: 400 }
      )
    }

    // حذف الجلسات النشطة
    await query('DELETE FROM client_sessions WHERE client_id = $1', [parseInt(id)])

    // حذف الحساب
    const deleteQuery = `
      DELETE FROM client_portal_accounts 
      WHERE id = $1 
      RETURNING username, email
    `

    const result = await query(deleteQuery, [parseInt(id)])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الحساب بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف حساب العميل:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف حساب العميل' },
      { status: 500 }
    )
  }
}