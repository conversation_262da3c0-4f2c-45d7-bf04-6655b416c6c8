import { NextRequest, NextResponse } from 'next/server'
import { openDb } from '@/lib/database'

// GET - جلب خدمة واحدة بالـ slug
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params
    

    const db = await openDb()

    const service = await db.get('SELECT * FROM services WHERE slug = $1 AND is_active = true', [slug])
    
    if (!service) {
      return NextResponse.json(
        { success: false, error: 'الخدمة غير موجودة' },
        { status: 404 }
      )
    }
    
    
    
    return NextResponse.json({
      success: true,
      service: service,
      message: 'تم جلب الخدمة بنجاح'
    })

  } catch (error) {
    console.error('❌ خطأ في جلب الخدمة:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'فشل في جلب الخدمة',
        details: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    )
  }
}
