import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب المحادثات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userType = searchParams.get('userType')
    const userId = searchParams.get('userId')

    if (!userType || !userId) {
      return NextResponse.json(
        { success: false, error: 'نوع المستخدم ومعرف المستخدم مطلوبان' },
        { status: 400 }
      )
    }

    let conversationsQuery = ''
    let queryParams: any[] = []

    if (userType === 'user') {
      conversationsQuery = `
        SELECT 
          c.*,
          cl.name as client_name,
          u.username as user_name,
          (
            SELECT COUNT(*) 
            FROM messages m 
            WHERE m.conversation_id = c.id 
            AND m.sender_type = 'client' 
            AND m.is_read = false
          ) as unread_count
        FROM conversations c
        LEFT JOIN clients cl ON c.client_id = cl.id
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.user_id = $1
        ORDER BY c.last_message_at DESC
      `
      queryParams = [userId]
    } else {
      conversationsQuery = `
        SELECT 
          c.*,
          cl.name as client_name,
          u.username as user_name,
          (
            SELECT COUNT(*) 
            FROM messages m 
            WHERE m.conversation_id = c.id 
            AND m.sender_type = 'user' 
            AND m.is_read = false
          ) as unread_count
        FROM conversations c
        LEFT JOIN clients cl ON c.client_id = cl.id
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.client_id = $1
        ORDER BY c.last_message_at DESC
      `
      queryParams = [userId]
    }

    const result = await query(conversationsQuery, queryParams)

    return NextResponse.json({
      success: true,
      data: result.rows
    })

  } catch (error) {
    console.error('Error fetching conversations:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب المحادثات' },
      { status: 500 }
    )
  }
}

// POST - إنشاء محادثة جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { clientId, userId, title } = body

    if (!clientId || !userId) {
      return NextResponse.json(
        { success: false, error: 'معرف العميل ومعرف المستخدم مطلوبان' },
        { status: 400 }
      )
    }

    // التحقق من وجود محادثة بين نفس العميل والمستخدم
    const existingConversation = await query(`
      SELECT id FROM conversations 
      WHERE client_id = $1 AND user_id = $2
    `, [clientId, userId])

    if (existingConversation.rows.length > 0) {
      return NextResponse.json({
        success: true,
        data: existingConversation.rows[0],
        message: 'المحادثة موجودة مسبقاً'
      })
    }

    // إنشاء محادثة جديدة
    const result = await query(`
      INSERT INTO conversations (client_id, user_id, title, status)
      VALUES ($1, $2, $3, 'active')
      RETURNING *
    `, [clientId, userId, title || 'محادثة جديدة'])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إنشاء المحادثة بنجاح'
    })

  } catch (error) {
    console.error('Error creating conversation:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إنشاء المحادثة' },
      { status: 500 }
    )
  }
}

// PUT - تحديث محادثة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, title, status } = body

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المحادثة مطلوب' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE conversations 
      SET title = COALESCE($2, title),
          status = COALESCE($3, status),
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *
    `, [id, title, status])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المحادثة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث المحادثة بنجاح'
    })

  } catch (error) {
    console.error('Error updating conversation:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المحادثة' },
      { status: 500 }
    )
  }
}

// DELETE - حذف محادثة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المحادثة مطلوب' },
        { status: 400 }
      )
    }

    // حذف المحادثة (سيتم حذف الرسائل تلقائياً بسبب CASCADE)
    const result = await query(`
      DELETE FROM conversations 
      WHERE id = $1
      RETURNING *
    `, [id])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'المحادثة غير موجودة' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المحادثة بنجاح'
    })

  } catch (error) {
    console.error('Error deleting conversation:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المحادثة' },
      { status: 500 }
    )
  }
}
