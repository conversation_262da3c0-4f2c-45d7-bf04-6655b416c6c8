import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// POST - تحديد الرسائل كمقروءة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { conversationId, readerType, readerId, messageId } = body

    if (!readerType || !readerId) {
      return NextResponse.json(
        { success: false, error: 'نوع القارئ ومعرف القارئ مطلوبان' },
        { status: 400 }
      )
    }

    if (messageId) {
      // تحديد رسالة واحدة كمقروءة
      await query(`
        INSERT INTO message_read_status (message_id, reader_type, reader_id)
        VALUES ($1, $2, $3)
        ON CONFLICT (message_id, reader_type, reader_id) 
        DO UPDATE SET read_at = CURRENT_TIMESTAMP
      `, [messageId, readerType, readerId])

      // تحديث حالة الرسالة
      await query(`
        UPDATE messages 
        SET is_read = true 
        WHERE id = $1
      `, [messageId])

    } else if (conversationId) {
      // تحديد جميع الرسائل في المحادثة كمقروءة
      const messages = await query(`
        SELECT id FROM messages 
        WHERE conversation_id = $1 
        AND NOT (sender_type = $2 AND sender_id = $3)
        AND is_read = false
      `, [conversationId, readerType, readerId])

      for (const message of messages.rows) {
        await query(`
          INSERT INTO message_read_status (message_id, reader_type, reader_id)
          VALUES ($1, $2, $3)
          ON CONFLICT (message_id, reader_type, reader_id) 
          DO UPDATE SET read_at = CURRENT_TIMESTAMP
        `, [message.id, readerType, readerId])
      }

      // تحديث حالة جميع الرسائل
      await query(`
        UPDATE messages 
        SET is_read = true 
        WHERE conversation_id = $1 
        AND NOT (sender_type = $2 AND sender_id = $3)
      `, [conversationId, readerType, readerId])

    } else {
      return NextResponse.json(
        { success: false, error: 'معرف المحادثة أو معرف الرسالة مطلوب' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث حالة القراءة بنجاح'
    })

  } catch (error) {
    console.error('Error marking messages as read:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث حالة القراءة' },
      { status: 500 }
    )
  }
}
