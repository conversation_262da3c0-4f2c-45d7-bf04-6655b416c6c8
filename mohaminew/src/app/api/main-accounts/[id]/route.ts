import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import { applyAccountLinking } from '@/lib/account-linking'

// PUT - تحديث حساب رئيسي
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)
    const body = await request.json()
    const { account_name, description, chart_account_id } = body

    if (!account_name?.trim()) {
      return NextResponse.json(
        { success: false, error: 'اسم الحساب مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود الحساب
    const existingAccount = await query(
      'SELECT id FROM main_accounts WHERE id = $1',
      [id]
    )

    if (existingAccount.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    // التحقق من عدم وجود حساب آخر بنفس الاسم
    const duplicateAccount = await query(
      'SELECT id FROM main_accounts WHERE LOWER(account_name) = LOWER($1) AND id != $2',
      [account_name.trim(), id]
    )

    if (duplicateAccount.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'يوجد حساب رئيسي آخر بهذا الاسم' },
        { status: 400 }
      )
    }

    const result = await query(`
      UPDATE main_accounts
      SET
        account_name = $1,
        description = $2,
        chart_account_id = $3,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING *
    `, [
      account_name.trim(),
      description?.trim() || null,
      chart_account_id || null,
      id
    ])

    let linkingResult = null

    // تطبيق الربط التلقائي إذا تم ربط الحساب بدليل الحسابات
    if (chart_account_id) {
      console.log(`🔗 تطبيق الربط التلقائي للحساب المحدث ${id}`)
      linkingResult = await applyAccountLinking(id, chart_account_id)
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الحساب الرئيسي بنجاح',
      data: result.rows[0],
      linking_result: linkingResult
    })
  } catch (error) {
    console.error('Error updating main account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الحساب الرئيسي' },
      { status: 500 }
    )
  }
}

// DELETE - حذف حساب رئيسي
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id)

    // التحقق من وجود الحساب
    const existingAccount = await query(
      'SELECT id, account_name, is_required FROM main_accounts WHERE id = $1',
      [id]
    )

    if (existingAccount.rows.length === 0) {
      return NextResponse.json(
        { success: false, error: 'الحساب غير موجود' },
        { status: 404 }
      )
    }

    // منع حذف الحسابات المطلوبة
    if (existingAccount.rows[0].is_required) {
      return NextResponse.json(
        { success: false, error: 'لا يمكن حذف هذا الحساب لأنه مطلوب للنظام' },
        { status: 400 }
      )
    }

    // التحقق من وجود ربط مع جداول أخرى (اختياري)
    // يمكن إضافة فحوصات إضافية هنا حسب الحاجة

    await query('DELETE FROM main_accounts WHERE id = $1', [id])

    return NextResponse.json({
      success: true,
      message: 'تم حذف الحساب الرئيسي بنجاح'
    })
  } catch (error) {
    console.error('Error deleting main account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الحساب الرئيسي' },
      { status: 500 }
    )
  }
}
