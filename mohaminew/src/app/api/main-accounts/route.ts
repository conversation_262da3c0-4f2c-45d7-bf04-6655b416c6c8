import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - جلب الحسابات الأساسية
export async function GET() {
  try {
    const result = await query(`
      SELECT 
        ma.id,
        ma.account_name,
        ma.account_code,
        ma.chart_account_id,
        ma.is_required,
        ma.description,
        coa.account_code as linked_account_code,
        coa.account_name as linked_account_name,
        coa.account_type as linked_account_type
      FROM main_accounts ma
      LEFT JOIN chart_of_accounts coa ON ma.chart_account_id = coa.id
      ORDER BY ma.id
    `)

    return NextResponse.json({
      success: true,
      data: result.rows
    })
  } catch (error) {
    console.error('Error fetching main accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الحسابات الأساسية' },
      { status: 500 }
    )
  }
}

// PUT - تحديث ربط الحسابات الأساسية
export async function PUT(request: NextRequest) {
  try {
    const { updates } = await request.json()

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { success: false, error: 'بيانات التحديث مطلوبة' },
        { status: 400 }
      )
    }

    // تحديث كل حساب
    for (const update of updates) {
      const { id, chart_account_id } = update

      if (!id) continue

      // تحديث الربط
      await query(`
        UPDATE main_accounts 
        SET chart_account_id = $1, updated_date = CURRENT_TIMESTAMP
        WHERE id = $2
      `, [chart_account_id || null, id])

      // إذا تم الربط، تحديث كود الحساب من دليل الحسابات
      if (chart_account_id) {
        const chartAccount = await query(
          'SELECT account_code FROM chart_of_accounts WHERE id = $1',
          [chart_account_id]
        )
        
        if (chartAccount.rows.length > 0) {
          await query(`
            UPDATE main_accounts 
            SET account_code = $1
            WHERE id = $2
          `, [chartAccount.rows[0].account_code, id])
        }
      } else {
        // إذا تم إلغاء الربط، مسح كود الحساب
        await query(`
          UPDATE main_accounts 
          SET account_code = NULL
          WHERE id = $1
        `, [id])
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث ربط الحسابات الأساسية بنجاح'
    })
  } catch (error) {
    console.error('Error updating main accounts:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث ربط الحسابات الأساسية' },
      { status: 500 }
    )
  }
}

// POST - إضافة حساب رئيسي جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { account_name, description, chart_account_id } = body

    if (!account_name?.trim()) {
      return NextResponse.json(
        { success: false, error: 'اسم الحساب مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من عدم وجود حساب بنفس الاسم
    const existingAccount = await query(
      'SELECT id FROM main_accounts WHERE LOWER(account_name) = LOWER($1)',
      [account_name.trim()]
    )

    if (existingAccount.rows.length > 0) {
      return NextResponse.json(
        { success: false, error: 'يوجد حساب رئيسي بهذا الاسم مسبقاً' },
        { status: 400 }
      )
    }

    const result = await query(`
      INSERT INTO main_accounts (
        account_name,
        description,
        chart_account_id,
        is_required,
        created_date
      ) VALUES ($1, $2, $3, false, CURRENT_DATE)
      RETURNING *
    `, [
      account_name.trim(),
      description?.trim() || null,
      chart_account_id || null
    ])

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الحساب الرئيسي بنجاح',
      data: result.rows[0]
    })
  } catch (error) {
    console.error('Error creating main account:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة الحساب الرئيسي' },
      { status: 500 }
    )
  }
}