import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// GET - البحث في صفحات التنقل
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const searchTerm = searchParams.get('q')

    if (!searchTerm || searchTerm.trim().length < 2) {
      return NextResponse.json({
        success: false,
        error: 'يجب أن يكون النص المراد البحث عنه أكثر من حرفين'
      }, { status: 400 })
    }

    // البحث في العناوين والكلمات المفتاحية والوصف
    const result = await query(`
      SELECT 
        id,
        page_title,
        page_url,
        page_description,
        category,
        keywords
      FROM navigation_pages
      WHERE is_active = true
      AND (
        LOWER(page_title) LIKE LOWER($1) OR
        LOWER(page_description) LIKE LOWER($1) OR
        LOWER(keywords) LIKE LOWER($1) OR
        LOWER(category) LIKE LOWER($1)
      )
      ORDER BY 
        CASE 
          WHEN LOWER(page_title) LIKE LOWER($2) THEN 1
          WHEN LOWER(page_title) LIKE LOWER($1) THEN 2
          WHEN LOWER(keywords) LIKE LOWER($1) THEN 3
          ELSE 4
        END,
        page_title
      LIMIT 10
    `, [`%${searchTerm}%`, `${searchTerm}%`])

    return NextResponse.json({
      success: true,
      data: result.rows,
      total: result.rows.length,
      searchTerm,
      message: `تم العثور على ${result.rows.length} نتيجة`
    })

  } catch (error) {
    console.error('خطأ في البحث:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في البحث',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// POST - إضافة صفحة جديدة للتنقل
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      page_title,
      page_url,
      page_description = '',
      category = '',
      keywords = ''
    } = body

    if (!page_title || !page_url) {
      return NextResponse.json({
        success: false,
        error: 'عنوان الصفحة ورابط الصفحة مطلوبان'
      }, { status: 400 })
    }

    // التحقق من عدم تكرار الرابط
    const existingPage = await query(
      'SELECT id FROM navigation_pages WHERE page_url = $1',
      [page_url]
    )

    if (existingPage.rows.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رابط الصفحة موجود مسبقاً'
      }, { status: 400 })
    }

    const result = await query(`
      INSERT INTO navigation_pages (page_title, page_url, page_description, category, keywords)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [page_title, page_url, page_description, category, keywords])

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم إضافة الصفحة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في إضافة الصفحة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة الصفحة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// PUT - تحديث صفحة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      page_title,
      page_url,
      page_description,
      category,
      keywords,
      is_active
    } = body

    if (!id || !page_title || !page_url) {
      return NextResponse.json({
        success: false,
        error: 'المعرف وعنوان الصفحة ورابط الصفحة مطلوبان'
      }, { status: 400 })
    }

    const result = await query(`
      UPDATE navigation_pages 
      SET page_title = $1, page_url = $2, page_description = $3, 
          category = $4, keywords = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING *
    `, [page_title, page_url, page_description, category, keywords, is_active, id])

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الصفحة غير موجودة'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: result.rows[0],
      message: 'تم تحديث الصفحة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في تحديث الصفحة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الصفحة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}

// DELETE - حذف صفحة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الصفحة مطلوب'
      }, { status: 400 })
    }

    const result = await query(
      'DELETE FROM navigation_pages WHERE id = $1 RETURNING *',
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الصفحة غير موجودة'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الصفحة بنجاح'
    })

  } catch (error) {
    console.error('خطأ في حذف الصفحة:', error)
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف الصفحة',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 })
  }
}
