import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

const LAWS_DIR = '/home/<USER>/Downloads/legal-system/laws'

// GET - جلب جميع الملفات القانونية
export async function GET() {
  try {
    const files = await getAllLegalFiles(LAWS_DIR)
    
    return NextResponse.json({
      success: true,
      data: files
    })
  } catch (error) {
    console.error('Error fetching legal files:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الملفات القانونية' },
      { status: 500 }
    )
  }
}

async function getAllLegalFiles(dir: string): Promise<any[]> {
  const files: any[] = []
  
  try {
    const items = await fs.readdir(dir, { withFileTypes: true })
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      
      if (item.isDirectory()) {
        // إضافة الملفات من المجلدات الفرعية
        const subFiles = await getAllLegalFiles(fullPath)
        files.push(...subFiles)
      } else if (item.isFile() && (item.name.endsWith('.txt') || item.name.endsWith('.pdf'))) {
        // تنظيف اسم الملف
        let cleanName = item.name
          .replace(/^agoyemen\.net_\d+_/, '') // إزالة البادئة
          .replace(/\.txt$|\.pdf$/, '') // إزالة الامتداد
          .replace(/-/g, ' ') // استبدال الشرطات بمسافات
          .replace(/_/g, ' ') // استبدال الشرطات السفلية بمسافات
        
        // تحديد الفئة بناءً على المسار
        let category = 'عام'
        if (fullPath.includes('الدستور')) category = 'الدستور'
        else if (fullPath.includes('القوانين')) category = 'القوانين'
        else if (fullPath.includes('اللوائح')) category = 'اللوائح'
        else if (fullPath.includes('الاتفاقيات')) category = 'الاتفاقيات'
        else if (fullPath.includes('التشريعات')) category = 'التشريعات'
        else if (fullPath.includes('المواثيق')) category = 'المواثيق والإعلانات'
        else if (fullPath.includes('الأنظمة')) category = 'الأنظمة'
        else if (fullPath.includes('نصوص عقابية')) category = 'النصوص العقابية'
        
        files.push({
          id: Buffer.from(fullPath).toString('base64'),
          name: cleanName,
          originalName: item.name,
          path: fullPath,
          category,
          type: item.name.endsWith('.pdf') ? 'pdf' : 'txt',
          size: (await fs.stat(fullPath)).size
        })
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error)
  }
  
  return files.sort((a, b) => a.name.localeCompare(b.name, 'ar'))
}

// POST - إضافة ملف قانوني جديد
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const category = formData.get('category') as string || 'عام'
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'لم يتم تحديد ملف' },
        { status: 400 }
      )
    }
    
    // التحقق من نوع الملف
    if (!file.name.endsWith('.txt') && !file.name.endsWith('.pdf')) {
      return NextResponse.json(
        { success: false, error: 'نوع الملف غير مدعوم. يجب أن يكون txt أو pdf' },
        { status: 400 }
      )
    }
    
    // تحديد المجلد المناسب
    let targetDir = LAWS_DIR
    if (category !== 'عام') {
      targetDir = path.join(LAWS_DIR, category)
      
      // إنشاء المجلد إذا لم يكن موجوداً
      try {
        await fs.access(targetDir)
      } catch {
        await fs.mkdir(targetDir, { recursive: true })
      }
    }
    
    // حفظ الملف
    const buffer = Buffer.from(await file.arrayBuffer())
    const filePath = path.join(targetDir, file.name)
    await fs.writeFile(filePath, buffer)
    
    return NextResponse.json({
      success: true,
      message: 'تم رفع الملف بنجاح',
      data: {
        name: file.name,
        path: filePath,
        category,
        size: buffer.length
      }
    })
  } catch (error) {
    console.error('Error uploading file:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في رفع الملف' },
      { status: 500 }
    )
  }
}