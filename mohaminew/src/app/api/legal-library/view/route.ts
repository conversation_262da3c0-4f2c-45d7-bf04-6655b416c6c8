import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('id')
    
    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'معرف الملف مطلوب' },
        { status: 400 }
      )
    }
    
    // فك تشفير مسار الملف
    const filePath = Buffer.from(fileId, 'base64').toString('utf-8')
    
    // التحقق من وجود الملف
    try {
      await fs.access(filePath)
    } catch {
      return NextResponse.json(
        { success: false, error: 'الملف غير موجود' },
        { status: 404 }
      )
    }
    
    const fileName = path.basename(filePath)
    const fileExtension = path.extname(filePath)
    
    // إذا كان ملف PDF، إرجاعه مباشرة للعرض
    if (fileExtension === '.pdf') {
      const fileBuffer = await fs.readFile(filePath)
      return new NextResponse(fileBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `inline; filename="${encodeURIComponent(fileName)}"`,
        },
      })
    }
    
    // إذا كان ملف نصي، إرجاع محتواه كـ HTML للعرض
    if (fileExtension === '.txt') {
      const fileContent = await fs.readFile(filePath, 'utf-8')
      
      const htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${fileName}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .file-title {
            color: #007bff;
            font-size: 24px;
            font-weight: bold;
            margin: 0;
        }
        .content {
            white-space: pre-wrap;
            font-size: 16px;
            line-height: 1.8;
        }
        .download-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            font-weight: bold;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        @media print {
            .download-btn { display: none; }
            body { background: white; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <a href="/api/legal-library/download?id=${fileId}" class="download-btn">تحميل الملف</a>
    <div class="container">
        <div class="header">
            <h1 class="file-title">${fileName.replace('.txt', '')}</h1>
        </div>
        <div class="content">${fileContent}</div>
    </div>
</body>
</html>`
      
      return new NextResponse(htmlContent, {
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
        },
      })
    }
    
    // نوع ملف غير مدعوم للعرض
    return NextResponse.json(
      { success: false, error: 'نوع الملف غير مدعوم للعرض' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Error viewing file:', error)
    return NextResponse.json(
      { success: false, error: 'فشل في عرض الملف' },
      { status: 500 }
    )
  }
}
