'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { BookOpen, Plus, Edit, Trash2, Search, Filter } from 'lucide-react'

interface Account {
  id: number
  accno: string
  description: string
  charttype: string
  category: string
  contra: boolean
  tax: boolean
  link: string
  heading: number
  obsolete: boolean
  balance?: number
}

export default function ChartOfAccountsPage() {
  const [accounts, setAccounts] = useState<Account[]>([])
  const [filteredAccounts, setFilteredAccounts] = useState<Account[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')

  useEffect(() => {
    fetchAccounts()
  }, [])

  useEffect(() => {
    filterAccounts()
  }, [accounts, searchTerm, categoryFilter, typeFilter])

  const fetchAccounts = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/chart-of-accounts')
      const data = await response.json()
      if (data.success) {
        setAccounts(data.data)
      }
    } catch (error) {
      console.error('Error fetching accounts:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterAccounts = () => {
    let filtered = accounts

    if (searchTerm) {
      filtered = filtered.filter(account =>
        account.accno.toLowerCase().includes(searchTerm.toLowerCase()) ||
        account.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (categoryFilter !== 'all') {
      filtered = filtered.filter(account => account.category === categoryFilter)
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(account => account.charttype === typeFilter)
    }

    setFilteredAccounts(filtered)
  }

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'A': return 'أصول'
      case 'L': return 'خصوم'
      case 'Q': return 'حقوق ملكية'
      case 'I': return 'إيرادات'
      case 'E': return 'مصروفات'
      default: return category
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'A': return 'bg-blue-100 text-blue-800'
      case 'L': return 'bg-red-100 text-red-800'
      case 'Q': return 'bg-green-100 text-green-800'
      case 'I': return 'bg-purple-100 text-purple-800'
      case 'E': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeName = (charttype: string) => {
    return charttype === 'H' ? 'عنوان' : 'حساب'
  }

  const getTypeColor = (charttype: string) => {
    return charttype === 'H' 
      ? 'bg-yellow-100 text-yellow-800' 
      : 'bg-green-100 text-green-800'
  }

  const formatBalance = (balance?: number) => {
    if (balance === undefined || balance === null) return '-'
    return balance.toLocaleString('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2
    })
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <BookOpen className="h-8 w-8 mr-3 text-blue-600" />
              دليل الحسابات
            </h1>
            <p className="text-gray-600 mt-1">إدارة دليل الحسابات المحاسبي المتكامل</p>
          </div>
          <div className="flex space-x-3 space-x-reverse">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              حساب جديد
            </Button>
          </div>
        </div>

        {/* البحث والفلترة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Search className="h-5 w-5 mr-2" />
              البحث والفلترة
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Input
                  placeholder="البحث برقم الحساب أو الوصف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <div>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="فلترة حسب الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الفئات</SelectItem>
                    <SelectItem value="A">الأصول</SelectItem>
                    <SelectItem value="L">الخصوم</SelectItem>
                    <SelectItem value="Q">حقوق الملكية</SelectItem>
                    <SelectItem value="I">الإيرادات</SelectItem>
                    <SelectItem value="E">المصروفات</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="فلترة حسب النوع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الأنواع</SelectItem>
                    <SelectItem value="H">العناوين</SelectItem>
                    <SelectItem value="A">الحسابات</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {['A', 'L', 'Q', 'I', 'E'].map(category => {
            const categoryAccounts = accounts.filter(acc => acc.category === category)
            return (
              <Card key={category}>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-gray-900">
                    {categoryAccounts.length}
                  </div>
                  <div className="text-sm text-gray-600">
                    {getCategoryName(category)}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* قائمة الحسابات */}
        <Card>
          <CardHeader>
            <CardTitle>
              دليل الحسابات ({filteredAccounts.length} من {accounts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="text-gray-500">جاري التحميل...</div>
              </div>
            ) : filteredAccounts.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-500">لا توجد حسابات</div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b bg-gray-50">
                      <th className="text-right p-3 font-medium">رقم الحساب</th>
                      <th className="text-right p-3 font-medium">اسم الحساب</th>
                      <th className="text-center p-3 font-medium">النوع</th>
                      <th className="text-center p-3 font-medium">الفئة</th>
                      <th className="text-right p-3 font-medium">الرصيد</th>
                      <th className="text-center p-3 font-medium">خصائص</th>
                      <th className="text-center p-3 font-medium">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredAccounts.map((account) => (
                      <tr 
                        key={account.id} 
                        className={`border-b hover:bg-gray-50 ${
                          account.charttype === 'H' ? 'bg-yellow-50 font-medium' : ''
                        }`}
                      >
                        <td className="p-3 font-mono text-sm">
                          {account.accno}
                        </td>
                        <td className="p-3">
                          <div className={account.charttype === 'H' ? 'font-bold' : ''}>
                            {account.description}
                          </div>
                        </td>
                        <td className="p-3 text-center">
                          <Badge className={getTypeColor(account.charttype)}>
                            {getTypeName(account.charttype)}
                          </Badge>
                        </td>
                        <td className="p-3 text-center">
                          <Badge className={getCategoryColor(account.category)}>
                            {getCategoryName(account.category)}
                          </Badge>
                        </td>
                        <td className="p-3 text-right font-mono text-sm">
                          {account.charttype === 'A' ? formatBalance(account.balance) : '-'}
                        </td>
                        <td className="p-3 text-center">
                          <div className="flex justify-center space-x-1 space-x-reverse">
                            {account.tax && (
                              <Badge variant="outline" className="text-xs">ضريبي</Badge>
                            )}
                            {account.contra && (
                              <Badge variant="outline" className="text-xs">مقابل</Badge>
                            )}
                            {account.obsolete && (
                              <Badge variant="outline" className="text-xs text-red-600">ملغي</Badge>
                            )}
                          </div>
                        </td>
                        <td className="p-3">
                          <div className="flex items-center justify-center space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                            {!account.obsolete && (
                              <Button size="sm" variant="outline" className="text-red-600 hover:text-red-700">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
