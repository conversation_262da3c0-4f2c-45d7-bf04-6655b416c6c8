'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { ComprehensiveAccountSelect } from '@/components/ui/comprehensive-account-select'
import { 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Search, 
  Filter,
  Download,
  Eye,
  Calendar,
  DollarSign
} from 'lucide-react'

interface AccountBalance {
  id: number
  account_code: string
  account_name: string
  account_type: string
  account_nature: string
  is_linked_record: boolean
  original_table?: string
  debit_balance: number
  credit_balance: number
  net_balance: number
  balance_type: 'مدين' | 'دائن' | 'متوازن'
  transactions_count: number
  last_transaction_date?: string
  // معلومات إضافية للحسابات المرتبطة
  linked_entity_name?: string
  linked_entity_phone?: string
  linked_entity_email?: string
}

interface Summary {
  total_debtors: number
  total_creditors: number
  debtors_count: number
  creditors_count: number
  net_position: number
}

export default function DebtorsCreditors() {
  const [balances, setBalances] = useState<AccountBalance[]>([])
  const [summary, setSummary] = useState<Summary>({
    total_debtors: 0,
    total_creditors: 0,
    debtors_count: 0,
    creditors_count: 0,
    net_position: 0
  })
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedAccountType, setSelectedAccountType] = useState('')
  const [balanceFilter, setBalanceFilter] = useState<'all' | 'debtors' | 'creditors'>('all')
  const [dateFilter, setDateFilter] = useState('')

  useEffect(() => {
    fetchBalances()
  }, [])

  const fetchBalances = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (dateFilter) params.append('as_of_date', dateFilter)
      
      const response = await fetch(`/api/accounting/debtors-creditors?${params.toString()}`)
      if (response.ok) {
        const data = await response.json()
        setBalances(data.balances || [])
        setSummary(data.summary || {
          total_debtors: 0,
          total_creditors: 0,
          debtors_count: 0,
          creditors_count: 0,
          net_position: 0
        })
      } else {
        console.error('فشل في جلب الأرصدة')
      }
    } catch (error) {
      console.error('خطأ في جلب الأرصدة:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredBalances = balances.filter(balance => {
    const matchesSearch = balance.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         balance.account_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (balance.linked_entity_name && balance.linked_entity_name.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesType = !selectedAccountType || balance.account_type === selectedAccountType
    
    const matchesBalance = balanceFilter === 'all' || 
                          (balanceFilter === 'debtors' && balance.balance_type === 'مدين') ||
                          (balanceFilter === 'creditors' && balance.balance_type === 'دائن')
    
    return matchesSearch && matchesType && matchesBalance
  })

  const exportToExcel = async () => {
    try {
      const response = await fetch('/api/accounting/debtors-creditors/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          balances: filteredBalances,
          summary,
          filters: {
            searchTerm,
            selectedAccountType,
            balanceFilter,
            dateFilter
          }
        }),
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `debtors-creditors-${new Date().toISOString().split('T')[0]}.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('خطأ في التصدير:', error)
    }
  }

  const getBalanceTypeColor = (balanceType: string) => {
    switch (balanceType) {
      case 'مدين': return 'bg-red-100 text-red-800'
      case 'دائن': return 'bg-green-100 text-green-800'
      case 'متوازن': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getEntityTypeIcon = (originalTable?: string) => {
    switch (originalTable) {
      case 'clients': return '👤'
      case 'employees': return '👨‍💼'
      case 'suppliers': return '🏢'
      default: return '📋'
    }
  }

  return (
    <MainLayout>
      <div className="container mx-auto p-6 space-y-6">
        {/* العنوان */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Users className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">قائمة الدائنين والمدينين</h1>
              <p className="text-gray-600">عرض أرصدة جميع الحسابات والعملاء والموظفين والموردين</p>
            </div>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <Button onClick={exportToExcel} variant="outline" className="flex items-center space-x-2 space-x-reverse">
              <Download className="h-4 w-4" />
              <span>تصدير Excel</span>
            </Button>
            <Button onClick={fetchBalances} disabled={loading}>
              {loading ? 'جاري التحديث...' : 'تحديث البيانات'}
            </Button>
          </div>
        </div>

        {/* الملخص */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي المدينين</p>
                  <p className="text-2xl font-bold text-red-600">
                    {summary.total_debtors.toLocaleString()} ر.ي
                  </p>
                  <p className="text-xs text-gray-500">{summary.debtors_count} حساب</p>
                </div>
                <TrendingUp className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي الدائنين</p>
                  <p className="text-2xl font-bold text-green-600">
                    {summary.total_creditors.toLocaleString()} ر.ي
                  </p>
                  <p className="text-xs text-gray-500">{summary.creditors_count} حساب</p>
                </div>
                <TrendingDown className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">صافي المركز</p>
                  <p className={`text-2xl font-bold ${summary.net_position >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {summary.net_position.toLocaleString()} ر.ي
                  </p>
                  <p className="text-xs text-gray-500">
                    {summary.net_position >= 0 ? 'مركز إيجابي' : 'مركز سلبي'}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">إجمالي الحسابات</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {balances.length}
                  </p>
                  <p className="text-xs text-gray-500">حساب نشط</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-center">
                <Label htmlFor="date-filter" className="text-sm text-gray-600">كما في تاريخ</Label>
                <Input
                  id="date-filter"
                  type="date"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="mt-1"
                />
                <Button 
                  onClick={fetchBalances} 
                  size="sm" 
                  className="mt-2 w-full"
                  disabled={loading}
                >
                  تطبيق
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* المرشحات */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <Filter className="h-5 w-5" />
              <span>المرشحات</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">البحث</Label>
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="search"
                    placeholder="ابحث بالاسم أو الرمز..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pr-10"
                  />
                </div>
              </div>

              <div>
                <Label>نوع الحساب</Label>
                <select
                  value={selectedAccountType}
                  onChange={(e) => setSelectedAccountType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">جميع الأنواع</option>
                  <option value="أصول">أصول</option>
                  <option value="خصوم">خصوم</option>
                  <option value="حقوق ملكية">حقوق ملكية</option>
                  <option value="إيرادات">إيرادات</option>
                  <option value="مصروفات">مصروفات</option>
                </select>
              </div>

              <div>
                <Label>نوع الرصيد</Label>
                <select
                  value={balanceFilter}
                  onChange={(e) => setBalanceFilter(e.target.value as 'all' | 'debtors' | 'creditors')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">جميع الأرصدة</option>
                  <option value="debtors">المدينين فقط</option>
                  <option value="creditors">الدائنين فقط</option>
                </select>
              </div>

              <div className="flex items-end">
                <Button 
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedAccountType('')
                    setBalanceFilter('all')
                    setDateFilter('')
                  }}
                  variant="outline"
                  className="w-full"
                >
                  مسح المرشحات
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* جدول الأرصدة */}
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل الأرصدة ({filteredBalances.length} حساب)</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p>جاري تحميل البيانات...</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 p-3 text-right">رمز الحساب</th>
                      <th className="border border-gray-300 p-3 text-right">اسم الحساب</th>
                      <th className="border border-gray-300 p-3 text-right">النوع</th>
                      <th className="border border-gray-300 p-3 text-right">الرصيد المدين</th>
                      <th className="border border-gray-300 p-3 text-right">الرصيد الدائن</th>
                      <th className="border border-gray-300 p-3 text-right">صافي الرصيد</th>
                      <th className="border border-gray-300 p-3 text-right">نوع الرصيد</th>
                      <th className="border border-gray-300 p-3 text-right">عدد المعاملات</th>
                      <th className="border border-gray-300 p-3 text-right">آخر معاملة</th>
                      <th className="border border-gray-300 p-3 text-right">إجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredBalances.map((balance) => (
                      <tr key={balance.id} className="hover:bg-gray-50">
                        <td className="border border-gray-300 p-3 font-mono text-sm">
                          {balance.account_code}
                        </td>
                        <td className="border border-gray-300 p-3">
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <span className="text-lg">
                              {getEntityTypeIcon(balance.original_table)}
                            </span>
                            <div>
                              <div className="font-medium">{balance.account_name}</div>
                              {balance.linked_entity_name && (
                                <div className="text-sm text-gray-500">
                                  {balance.linked_entity_name}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="border border-gray-300 p-3">
                          <Badge variant="outline" className="text-xs">
                            {balance.account_type}
                          </Badge>
                          {balance.is_linked_record && (
                            <Badge className="bg-blue-100 text-blue-800 mr-1" size="sm">
                              {balance.original_table === 'clients' ? 'عميل' : 
                               balance.original_table === 'employees' ? 'موظف' : 
                               balance.original_table === 'suppliers' ? 'مورد' : 'مرتبط'}
                            </Badge>
                          )}
                        </td>
                        <td className="border border-gray-300 p-3 text-right">
                          {balance.debit_balance > 0 ? (
                            <span className="text-red-600 font-medium">
                              {balance.debit_balance.toLocaleString()}
                            </span>
                          ) : '-'}
                        </td>
                        <td className="border border-gray-300 p-3 text-right">
                          {balance.credit_balance > 0 ? (
                            <span className="text-green-600 font-medium">
                              {balance.credit_balance.toLocaleString()}
                            </span>
                          ) : '-'}
                        </td>
                        <td className="border border-gray-300 p-3 text-right">
                          <span className={`font-bold ${
                            balance.net_balance > 0 ? 'text-red-600' : 
                            balance.net_balance < 0 ? 'text-green-600' : 'text-gray-600'
                          }`}>
                            {Math.abs(balance.net_balance).toLocaleString()}
                          </span>
                        </td>
                        <td className="border border-gray-300 p-3">
                          <Badge className={getBalanceTypeColor(balance.balance_type)} size="sm">
                            {balance.balance_type}
                          </Badge>
                        </td>
                        <td className="border border-gray-300 p-3 text-center">
                          {balance.transactions_count}
                        </td>
                        <td className="border border-gray-300 p-3 text-sm">
                          {balance.last_transaction_date ? 
                            new Date(balance.last_transaction_date).toLocaleDateString('en-GB') : 
                            'لا توجد معاملات'
                          }
                        </td>
                        <td className="border border-gray-300 p-3">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`/accounting/reports/account-statement?account_id=${balance.id}`, '_blank')}
                            className="flex items-center space-x-1 space-x-reverse"
                          >
                            <Eye className="h-3 w-3" />
                            <span>كشف حساب</span>
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>

                {filteredBalances.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                    <p>لا توجد أرصدة تطابق المعايير المحددة</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}