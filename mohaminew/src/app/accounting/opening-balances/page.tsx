'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Calculator,
  Plus,
  Search,
  Save,
  X,
  Trash2,
  AlertCircle,
  CheckCircle,
  DollarSign
} from 'lucide-react'

interface OpeningBalance {
  id: number
  account_id: number
  account_code: string
  account_name: string
  debit_balance: number
  credit_balance: number
  balance_date: string
}

interface ChartAccount {
  id: number
  account_code: string
  account_name: string
  account_type: string
  account_level: number
}

interface BalanceRow {
  id: string
  account_id: number | null
  account_code: string
  account_name: string
  debit_balance: string
  credit_balance: string
}

export default function OpeningBalancesPage() {
  const [balances, setBalances] = useState<OpeningBalance[]>([])
  const [chartAccounts, setChartAccounts] = useState<ChartAccount[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // حالة نافذة الإضافة
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [balanceRows, setBalanceRows] = useState<BalanceRow[]>([
    {
      id: '1',
      account_id: null,
      account_code: '',
      account_name: '',
      debit_balance: '',
      credit_balance: ''
    }
  ])

  // حالة البحث في الحسابات
  const [accountSearchTerms, setAccountSearchTerms] = useState<{ [key: string]: string }>({})

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setIsLoading(true)
      
      // جلب الأرصدة الافتتاحية
      const balancesResponse = await fetch('/api/accounting/opening-balances')
      const balancesData = await balancesResponse.json()
      
      // جلب دليل الحسابات مع الحسابات المرتبطة (العملاء والموظفين)
      const accountsResponse = await fetch('/api/accounting/chart-of-accounts?include_linked=true&only_transactional=true')
      const accountsData = await accountsResponse.json()

      if (balancesData.success) {
        setBalances(balancesData.data || [])
      }
      
      if (accountsData.success) {
        console.log('تم جلب الحسابات:', accountsData.accounts?.length || 0)
        setChartAccounts(accountsData.accounts || [])
      } else {
        console.error('خطأ في جلب الحسابات:', accountsData.error)
        setMessage({ type: 'error', text: 'فشل في جلب دليل الحسابات' })
      }

    } catch (error) {
      console.error('خطأ في جلب البيانات:', error)
      setMessage({ type: 'error', text: 'حدث خطأ في جلب البيانات' })
    } finally {
      setIsLoading(false)
    }
  }

  // تصفية الأرصدة حسب البحث
  const filteredBalances = balances.filter(balance =>
    balance.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    balance.account_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // إضافة صف جديد
  const addNewRow = () => {
    const newRow: BalanceRow = {
      id: Date.now().toString(),
      account_id: null,
      account_code: '',
      account_name: '',
      debit_balance: '',
      credit_balance: ''
    }
    setBalanceRows([...balanceRows, newRow])
  }

  // حذف صف
  const removeRow = (id: string) => {
    if (balanceRows.length > 1) {
      setBalanceRows(balanceRows.filter(row => row.id !== id))
      // إزالة مصطلح البحث للصف المحذوف
      const newSearchTerms = { ...accountSearchTerms }
      delete newSearchTerms[id]
      setAccountSearchTerms(newSearchTerms)
    }
  }

  // تحديث صف
  const updateRow = (id: string, field: keyof BalanceRow, value: any) => {
    setBalanceRows(balanceRows.map(row => 
      row.id === id ? { ...row, [field]: value } : row
    ))
  }

  // اختيار حساب
  const selectAccount = (rowId: string, account: any) => {
    // تحديد معرف الحساب الصحيح
    let accountId = account.id
    if (account.is_linked_record) {
      // للحسابات المرتبطة، نستخدم معرف خاص
      accountId = account.original_table === 'clients' 
        ? `client_${account.external_id}` 
        : `employee_${account.external_id}`
    }
    
    updateRow(rowId, 'account_id', accountId)
    updateRow(rowId, 'account_code', account.account_code)
    updateRow(rowId, 'account_name', account.account_name)
    
    // مسح مصطلح البحث
    setAccountSearchTerms(prev => ({
      ...prev,
      [rowId]: ''
    }))
  }

  // تحديث مصطلح البحث للحساب
  const updateAccountSearch = (rowId: string, searchTerm: string) => {
    setAccountSearchTerms(prev => ({
      ...prev,
      [rowId]: searchTerm
    }))
  }

  // تصفية الحسابات حسب البحث
  const getFilteredAccounts = (rowId: string) => {
    const searchTerm = accountSearchTerms[rowId] || ''
    if (!searchTerm.trim()) {
      return chartAccounts
    }
    
    return chartAccounts.filter(account => 
      account.account_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.account_type.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  // حساب الإجماليات
  const calculateTotals = () => {
    const totalDebit = balanceRows.reduce((sum, row) => {
      const debit = parseFloat(row.debit_balance) || 0
      return sum + debit
    }, 0)

    const totalCredit = balanceRows.reduce((sum, row) => {
      const credit = parseFloat(row.credit_balance) || 0
      return sum + credit
    }, 0)

    return { totalDebit, totalCredit }
  }

  const { totalDebit, totalCredit } = calculateTotals()
  const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01 // للتعامل مع أخطاء الفاصلة العشرية

  // حفظ الأرصدة
  const handleSave = async () => {
    try {
      // التحقق من التوازن
      if (!isBalanced) {
        setMessage({ 
          type: 'error', 
          text: `يجب أن يكون إجمالي المدين (${totalDebit.toFixed(2)}) مساوياً لإجمالي الدائن (${totalCredit.toFixed(2)})` 
        })
        return
      }

      // التحقق من وجود حسابات محددة
      const validRows = balanceRows.filter(row => 
        row.account_id && 
        (parseFloat(row.debit_balance) > 0 || parseFloat(row.credit_balance) > 0)
      )

      if (validRows.length === 0) {
        setMessage({ type: 'error', text: 'يجب إضافة حساب واحد على الأقل برصيد' })
        return
      }

      // إعداد البيانات للإرسال
      const balancesToSave = validRows.map(row => ({
        account_id: row.account_id,
        debit_balance: parseFloat(row.debit_balance) || 0,
        credit_balance: parseFloat(row.credit_balance) || 0,
        balance_date: new Date().toISOString().split('T')[0]
      }))

      const response = await fetch('/api/accounting/opening-balances/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ balances: balancesToSave }),
      })

      const data = await response.json()

      if (data.success) {
        setMessage({ type: 'success', text: `تم حفظ ${validRows.length} رصيد افتتاحي بنجاح` })
        setIsAddModalOpen(false)
        setBalanceRows([{
          id: '1',
          account_id: null,
          account_code: '',
          account_name: '',
          debit_balance: '',
          credit_balance: ''
        }])
        setAccountSearchTerms({})
        await fetchData()
      } else {
        setMessage({ type: 'error', text: data.error || 'فشل في حفظ الأرصدة' })
      }

    } catch (error) {
      console.error('خطأ في الحفظ:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء حفظ الأرصدة' })
    }
  }

  // حذف رصيد افتتاحي
  const handleDelete = async (id: number) => {
    if (!confirm('هل أنت متأكد من حذف هذا الرصيد الافتتاحي؟')) return

    try {
      const response = await fetch(`/api/accounting/opening-balances?id=${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()
      if (data.success) {
        setMessage({ type: 'success', text: 'تم حذف الرصيد الافتتاحي بنجاح' })
        await fetchData()
      } else {
        setMessage({ type: 'error', text: data.error || 'فشل في حذف الرصيد' })
      }
    } catch (error) {
      console.error('خطأ في الحذف:', error)
      setMessage({ type: 'error', text: 'حدث خطأ أثناء حذف الرصيد' })
    }
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Calculator className="h-8 w-8 animate-spin mx-auto mb-2" />
            <span>جاري التحميل...</span>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Calculator className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">الأرصدة الافتتاحية</h1>
              <p className="text-gray-600">إدارة الأرصدة الافتتاحية للحسابات</p>
            </div>
          </div>
          <Button
            onClick={() => setIsAddModalOpen(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 ml-2" />
            إضافة أرصدة افتتاحية
          </Button>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* إحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">
                {filteredBalances.length}
              </div>
              <div className="text-sm text-gray-600">إجمالي الأرصدة</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {filteredBalances.reduce((sum, b) => sum + b.debit_balance, 0).toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">إجمالي المدين</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {filteredBalances.reduce((sum, b) => sum + b.credit_balance, 0).toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">إجمالي الدائن</div>
            </CardContent>
          </Card>
        </div>

        {/* البحث */}
        <Card>
          <CardContent className="p-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="البحث في الأرصدة الافتتاحية..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* جدول الأرصدة */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 ml-2" />
              الأرصدة الافتتاحية ({filteredBalances.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredBalances.length === 0 ? (
              <div className="text-center py-8">
                <Calculator className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">لا توجد أرصدة افتتاحية</p>
                <p className="text-sm text-gray-400">انقر على "إضافة أرصدة افتتاحية" لبدء الإضافة</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-right p-3">رقم الحساب</th>
                      <th className="text-right p-3">اسم الحساب</th>
                      <th className="text-right p-3">الرصيد المدين</th>
                      <th className="text-right p-3">الرصيد الدائن</th>
                      <th className="text-right p-3">تاريخ الرصيد</th>
                      <th className="text-center p-3">الإجراءات</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredBalances.map((balance) => (
                      <tr key={balance.id} className="border-b hover:bg-gray-50">
                        <td className="p-3 font-mono text-blue-600">{balance.account_code}</td>
                        <td className="p-3">{balance.account_name}</td>
                        <td className="p-3 text-green-600 font-medium">
                          {balance.debit_balance > 0 ? balance.debit_balance.toFixed(2) : '-'}
                        </td>
                        <td className="p-3 text-red-600 font-medium">
                          {balance.credit_balance > 0 ? balance.credit_balance.toFixed(2) : '-'}
                        </td>
                        <td className="p-3 text-gray-600">{balance.balance_date}</td>
                        <td className="p-3 text-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(balance.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>

        {/* نافذة إضافة الأرصدة */}
        {isAddModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">إضافة أرصدة افتتاحية</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsAddModalOpen(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {/* أزرار التحكم */}
                <div className="flex justify-between items-center">
                  <Button
                    onClick={addNewRow}
                    variant="outline"
                    className="border-green-600 text-green-600 hover:bg-green-50"
                  >
                    <Plus className="h-4 w-4 ml-2" />
                    إضافة صف جديد
                  </Button>
                  
                  <div className="flex items-center space-x-4 space-x-reverse">
                    <div className="text-sm">
                      <span className="text-gray-600">إجمالي المدين: </span>
                      <span className="font-bold text-green-600">{totalDebit.toFixed(2)}</span>
                    </div>
                    <div className="text-sm">
                      <span className="text-gray-600">إجمالي الدائن: </span>
                      <span className="font-bold text-blue-600">{totalCredit.toFixed(2)}</span>
                    </div>
                    <div className="flex items-center">
                      {isBalanced ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-600" />
                      )}
                      <span className={`text-sm ml-1 ${isBalanced ? 'text-green-600' : 'text-red-600'}`}>
                        {isBalanced ? 'متوازن' : 'غير متوازن'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* جدول الإدخال */}
                <div className="border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="text-right p-3 border-b">اسم الحساب</th>
                        <th className="text-right p-3 border-b">الرصيد المدين</th>
                        <th className="text-right p-3 border-b">الرصيد الدائن</th>
                        <th className="text-center p-3 border-b">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {balanceRows.map((row, index) => (
                        <tr key={row.id} className="border-b">
                          <td className="p-3" style={{ minWidth: '300px' }}>
                            {/* حقل البحث والاختيار */}
                            <div className="space-y-2">
                              <div className="relative">
                                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                <Input
                                  placeholder="ابحث عن الحساب..."
                                  value={accountSearchTerms[row.id] || ''}
                                  onChange={(e) => updateAccountSearch(row.id, e.target.value)}
                                  className="pr-10"
                                />
                              </div>
                              
                              {/* عرض الحساب المختار */}
                              {row.account_id && (
                                <div className="flex items-center justify-between p-3 bg-blue-50 rounded border border-blue-200">
                                  <div className="flex items-center space-x-2 space-x-reverse">
                                    <span className="font-mono text-sm text-blue-600 font-medium">{row.account_code}</span>
                                    <span className="text-sm font-medium">{row.account_name}</span>
                                    {/* تحديد نوع الحساب */}
                                    <Badge 
                                      variant="outline" 
                                      className={`text-xs ${
                                        row.account_code?.startsWith('C') 
                                          ? 'border-green-500 text-green-700 bg-green-50' 
                                          : row.account_code?.startsWith('E')
                                          ? 'border-purple-500 text-purple-700 bg-purple-50'
                                          : 'border-blue-500 text-blue-700 bg-blue-50'
                                      }`}
                                    >
                                      {row.account_code?.startsWith('C') 
                                        ? 'عميل' 
                                        : row.account_code?.startsWith('E')
                                        ? 'موظف'
                                        : 'حساب مالي'
                                      }
                                    </Badge>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      updateRow(row.id, 'account_id', null)
                                      updateRow(row.id, 'account_code', '')
                                      updateRow(row.id, 'account_name', '')
                                    }}
                                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                              
                              {/* قائمة الحسابات المفلترة */}
                              {accountSearchTerms[row.id] && !row.account_id && (
                                <div className="max-h-60 overflow-y-auto border rounded bg-white shadow-lg z-50">
                                  {getFilteredAccounts(row.id).slice(0, 15).map((account) => (
                                    <div
                                      key={account.id}
                                      onClick={() => selectAccount(row.id, account)}
                                      className="p-3 hover:bg-blue-50 cursor-pointer border-b last:border-b-0 transition-colors"
                                    >
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-2 space-x-reverse">
                                          <span className="font-mono text-sm text-blue-600 font-medium">{account.account_code}</span>
                                          <span className="text-sm font-medium">{account.account_name}</span>
                                        </div>
                                        <div className="flex items-center space-x-1 space-x-reverse">
                                          <Badge 
                                            variant="outline" 
                                            className={`text-xs ${
                                              account.is_linked_record 
                                                ? account.original_table === 'clients' 
                                                  ? 'border-green-500 text-green-700 bg-green-50' 
                                                  : 'border-purple-500 text-purple-700 bg-purple-50'
                                                : 'border-blue-500 text-blue-700 bg-blue-50'
                                            }`}
                                          >
                                            {account.is_linked_record 
                                              ? account.original_table === 'clients' 
                                                ? 'عميل' 
                                                : 'موظف'
                                              : account.account_type
                                            }
                                          </Badge>
                                        </div>
                                      </div>
                                      {account.description && (
                                        <div className="text-xs text-gray-500 mt-1 mr-16">
                                          {account.description}
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                  {getFilteredAccounts(row.id).length === 0 && (
                                    <div className="p-4 text-center text-gray-500 text-sm">
                                      <div className="mb-2">لا توجد نتائج للبحث</div>
                                      <div className="text-xs text-gray-400">جرب البحث باسم الحساب أو رقمه</div>
                                    </div>
                                  )}
                                  {getFilteredAccounts(row.id).length > 15 && (
                                    <div className="p-2 text-center text-gray-400 text-xs border-t bg-gray-50">
                                      يوجد {getFilteredAccounts(row.id).length - 15} نتيجة إضافية...
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="p-3">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              value={row.debit_balance}
                              onChange={(e) => updateRow(row.id, 'debit_balance', e.target.value)}
                              className="text-center"
                            />
                          </td>
                          <td className="p-3">
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              value={row.credit_balance}
                              onChange={(e) => updateRow(row.id, 'credit_balance', e.target.value)}
                              className="text-center"
                            />
                          </td>
                          <td className="p-3 text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeRow(row.id)}
                              disabled={balanceRows.length === 1}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* أزرار الحفظ */}
                <div className="flex justify-end space-x-2 space-x-reverse pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => setIsAddModalOpen(false)}
                  >
                    إلغاء
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={!isBalanced}
                    className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
                  >
                    <Save className="h-4 w-4 ml-2" />
                    حفظ الأرصدة
                  </Button>
                </div>

                {/* تنبيه التوازن */}
                {!isBalanced && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-red-800">
                      يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن لحفظ الأرصدة
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}