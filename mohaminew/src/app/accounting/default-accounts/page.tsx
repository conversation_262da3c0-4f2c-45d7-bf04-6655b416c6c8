'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Settings, 
  Save, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Wallet,
  Link2
} from 'lucide-react'

interface MainAccount {
  id: number
  account_name: string
  account_code: string | null
  chart_account_id: number | null
  is_required: boolean
  description: string
  linked_account_code: string | null
  linked_account_name: string | null
  linked_account_type: string | null
}

interface ChartAccount {
  id: number
  account_code: string
  account_name: string
  account_type: string
  account_level: number
}

export default function DefaultAccountsPage() {
  const [mainAccounts, setMainAccounts] = useState<MainAccount[]>([])
  const [chartAccounts, setChartAccounts] = useState<ChartAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // حالة الربط لكل حساب أساسي
  const [accountLinks, setAccountLinks] = useState<{ [key: number]: number | null }>({})

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    setLoading(true)
    try {
      // جلب الحسابات الأساسية
      const mainAccountsResponse = await fetch('/api/accounting/default-accounts')
      const mainAccountsData = await mainAccountsResponse.json()

      // جلب دليل الحسابات
      const accountsResponse = await fetch('/api/accounting/chart-of-accounts?only_transactional=true')
      const accountsData = await accountsResponse.json()
      
      if (mainAccountsData.success && accountsData.success) {
        setMainAccounts(mainAccountsData.data || [])
        setChartAccounts(accountsData.accounts || [])
        
        // تعيين الروابط الحالية
        const links: { [key: number]: number | null } = {}
        mainAccountsData.data?.forEach((account: MainAccount) => {
          links[account.id] = account.chart_account_id
        })
        setAccountLinks(links)
      } else {
        setMessage({
          type: 'error',
          text: 'فشل في جلب البيانات'
        })
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      setMessage({
        type: 'error',
        text: 'حدث خطأ في جلب البيانات'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAccountChange = (mainAccountId: number, accountId: string) => {
    setAccountLinks({
      ...accountLinks,
      [mainAccountId]: accountId ? parseInt(accountId) : null
    })
  }

  const handleSave = async () => {
    setSaving(true)
    setMessage(null)
    
    try {
      const updates = Object.entries(accountLinks).map(([mainAccountId, chartAccountId]) => ({
        id: parseInt(mainAccountId),
        chart_account_id: chartAccountId
      }))

      const promises = Object.entries(accountLinks).map(async ([mainAccountId, chartAccountId]) => {
        if (chartAccountId) {
          const response = await fetch('/api/accounting/default-accounts', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              id: parseInt(mainAccountId),
              chart_account_id: chartAccountId
            })
          })
          return response.json()
        }
        return { success: true }
      })

      const results = await Promise.all(promises)
      const allSuccessful = results.every(result => result.success)

      if (allSuccessful) {
        setMessage({
          type: 'success',
          text: 'تم حفظ ربط الحسابات الأساسية بنجاح'
        })
        fetchData() // إعادة جلب البيانات
      } else {
        setMessage({
          type: 'error',
          text: 'فشل في حفظ بعض الحسابات'
        })
      }
    } catch (error) {
      console.error('Error saving main accounts:', error)
      setMessage({
        type: 'error',
        text: 'حدث خطأ في حفظ ربط الحسابات'
      })
    } finally {
      setSaving(false)
    }
  }

  const getAccountIcon = (accountName: string) => {
    const name = accountName.toLowerCase()
    if (name.includes('إيراد') || name.includes('ايراد')) {
      return <TrendingUp className="h-5 w-5 text-green-600" />
    } else if (name.includes('مصروف')) {
      return <TrendingDown className="h-5 w-5 text-red-600" />
    } else if (name.includes('صندوق')) {
      return <Wallet className="h-5 w-5 text-blue-600" />
    } else if (name.includes('عميل') || name.includes('عملاء')) {
      return <DollarSign className="h-5 w-5 text-purple-600" />
    } else if (name.includes('موظف')) {
      return <DollarSign className="h-5 w-5 text-orange-600" />
    } else if (name.includes('رأس المال')) {
      return <Settings className="h-5 w-5 text-indigo-600" />
    } else {
      return <Settings className="h-5 w-5 text-gray-600" />
    }
  }

  const getAccountColor = (accountName: string) => {
    const name = accountName.toLowerCase()
    if (name.includes('إيراد') || name.includes('ايراد')) {
      return 'bg-green-50 border-green-200'
    } else if (name.includes('مصروف')) {
      return 'bg-red-50 border-red-200'
    } else if (name.includes('صندوق')) {
      return 'bg-blue-50 border-blue-200'
    } else if (name.includes('عميل') || name.includes('عملاء')) {
      return 'bg-purple-50 border-purple-200'
    } else if (name.includes('موظف')) {
      return 'bg-orange-50 border-orange-200'
    } else if (name.includes('رأس المال')) {
      return 'bg-indigo-50 border-indigo-200'
    } else {
      return 'bg-gray-50 border-gray-200'
    }
  }

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="mr-2">جاري التحميل...</span>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* العنوان والأزرار */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Settings className="h-8 w-8 text-emerald-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">الحسابات الأساسية</h1>
              <p className="text-gray-600">ربط الحسابات الأساسية للنظام بدليل الحسابات المحاسبي</p>
            </div>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            <Button
              onClick={fetchData}
              variant="outline"
              disabled={loading}
            >
              <RefreshCw className="h-4 w-4 ml-2" />
              تحديث
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              {saving ? (
                <RefreshCw className="h-4 w-4 ml-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 ml-2" />
              )}
              حفظ الربط
            </Button>
          </div>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <Alert className={message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            {message.type === 'success' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>
              {message.text}
            </AlertDescription>
          </Alert>
        )}

        {/* إحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">
                {mainAccounts.length}
              </div>
              <div className="text-sm text-gray-600">إجمالي الحسابات الأساسية</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {mainAccounts.filter(acc => acc.chart_account_id).length}
              </div>
              <div className="text-sm text-gray-600">الحسابات المربوطة</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">
                {mainAccounts.filter(acc => !acc.chart_account_id).length}
              </div>
              <div className="text-sm text-gray-600">الحسابات غير المربوطة</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">
                {mainAccounts.filter(acc => acc.is_required).length}
              </div>
              <div className="text-sm text-gray-600">الحسابات المطلوبة</div>
            </CardContent>
          </Card>
        </div>

        {/* قائمة الحسابات الافتراضية بالتصميم المطلوب */}
        <div className="bg-white rounded-lg shadow-sm border" dir="rtl">
          <div className="divide-y divide-gray-200">

            {/* عنوان العملاء */}
            <div className="bg-gray-100 px-6 py-3 border-b">
              <h3 className="text-lg font-semibold text-gray-800">العملاء</h3>
            </div>

            {/* العملاء */}
            {mainAccounts
              .filter(account => account.account_name.includes('عملاء') || account.account_name.includes('العملاء'))
              .map((account) => (
                <div key={account.id} className="flex items-center justify-between py-4 px-6 hover:bg-gray-50">
                  <div className="flex-1 text-right">
                    <span className="text-lg font-medium text-gray-900">{account.account_name}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <Select
                      value={accountLinks[account.id]?.toString() || ''}
                      onValueChange={(value) => handleAccountChange(account.id, value)}
                    >
                      <SelectTrigger className="w-80 h-10 text-right">
                        <SelectValue placeholder="اختر حساب من دليل الحسابات" />
                      </SelectTrigger>
                      <SelectContent className="max-h-60">
                        {chartAccounts.map((chartAccount) => (
                          <SelectItem key={chartAccount.id} value={chartAccount.id.toString()}>
                            <div className="flex justify-between items-center w-full text-right">
                              <span className="flex-1">{chartAccount.account_name}</span>
                              <span className="text-sm text-gray-500 ml-2">
                                {chartAccount.account_code}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {account.chart_account_id && (
                      <div className="text-sm text-green-600 min-w-20 text-center">
                        <span>{account.account_code}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}

            {/* عنوان الموظفين */}
            <div className="bg-gray-100 px-6 py-3 border-b">
              <h3 className="text-lg font-semibold text-gray-800">الموظفين</h3>
            </div>

            {/* الموظفين */}
            {mainAccounts
              .filter(account => account.account_name.includes('موظف') || account.account_name.includes('الموظفين'))
              .map((account) => (
                <div key={account.id} className="flex items-center justify-between py-4 px-6 hover:bg-gray-50">
                  <div className="flex-1 text-right">
                    <span className="text-lg font-medium text-gray-900">{account.account_name}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <Select
                      value={accountLinks[account.id]?.toString() || ''}
                      onValueChange={(value) => handleAccountChange(account.id, value)}
                    >
                      <SelectTrigger className="w-80 h-10 text-right">
                        <SelectValue placeholder="اختر حساب من دليل الحسابات" />
                      </SelectTrigger>
                      <SelectContent className="max-h-60">
                        {chartAccounts.map((chartAccount) => (
                          <SelectItem key={chartAccount.id} value={chartAccount.id.toString()}>
                            <div className="flex justify-between items-center w-full text-right">
                              <span className="flex-1">{chartAccount.account_name}</span>
                              <span className="text-sm text-gray-500 ml-2">
                                {chartAccount.account_code}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {account.chart_account_id && (
                      <div className="text-sm text-green-600 min-w-20 text-center">
                        <span>{account.account_code}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}

            {/* عنوان المدينين */}
            <div className="bg-gray-100 px-6 py-3 border-b">
              <h3 className="text-lg font-semibold text-gray-800">المدينين</h3>
            </div>

            {/* المدينين */}
            {mainAccounts
              .filter(account => account.account_name.includes('مدين') || account.account_name.includes('المدينين'))
              .map((account) => (
                <div key={account.id} className="flex items-center justify-between py-4 px-6 hover:bg-gray-50">
                  <div className="flex-1 text-right">
                    <span className="text-lg font-medium text-gray-900">{account.account_name}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <Select
                      value={accountLinks[account.id]?.toString() || ''}
                      onValueChange={(value) => handleAccountChange(account.id, value)}
                    >
                      <SelectTrigger className="w-80 h-10 text-right">
                        <SelectValue placeholder="اختر حساب من دليل الحسابات" />
                      </SelectTrigger>
                      <SelectContent className="max-h-60">
                        {chartAccounts.map((chartAccount) => (
                          <SelectItem key={chartAccount.id} value={chartAccount.id.toString()}>
                            <div className="flex justify-between items-center w-full text-right">
                              <span className="flex-1">{chartAccount.account_name}</span>
                              <span className="text-sm text-gray-500 ml-2">
                                {chartAccount.account_code}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {account.chart_account_id && (
                      <div className="text-sm text-green-600 min-w-20 text-center">
                        <span>{account.account_code}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}

            {/* عنوان حسابات فرعية */}
            <div className="bg-gray-100 px-6 py-3 border-b">
              <h3 className="text-lg font-semibold text-gray-800">حسابات فرعية</h3>
            </div>

            {/* باقي الحسابات */}
            {mainAccounts
              .filter(account =>
                !account.account_name.includes('عملاء') &&
                !account.account_name.includes('العملاء') &&
                !account.account_name.includes('موظف') &&
                !account.account_name.includes('الموظفين') &&
                !account.account_name.includes('مدين') &&
                !account.account_name.includes('المدينين')
              )
              .map((account) => (
                <div key={account.id} className="flex items-center justify-between py-4 px-6 hover:bg-gray-50">
                  <div className="flex-1 text-right">
                    <span className="text-lg font-medium text-gray-900">{account.account_name}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <Select
                      value={accountLinks[account.id]?.toString() || ''}
                      onValueChange={(value) => handleAccountChange(account.id, value)}
                    >
                      <SelectTrigger className="w-80 h-10 text-right">
                        <SelectValue placeholder="اختر حساب من دليل الحسابات" />
                      </SelectTrigger>
                      <SelectContent className="max-h-60">
                        {chartAccounts.map((chartAccount) => (
                          <SelectItem key={chartAccount.id} value={chartAccount.id.toString()}>
                            <div className="flex justify-between items-center w-full text-right">
                              <span className="flex-1">{chartAccount.account_name}</span>
                              <span className="text-sm text-gray-500 ml-2">
                                {chartAccount.account_code}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {account.chart_account_id && (
                      <div className="text-sm text-green-600 min-w-20 text-center">
                        <span>{account.account_code}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* معلومات إضافية */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-start space-x-3 space-x-reverse">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-900 mb-2">معلومات مهمة</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• يتم ربط كل حساب أساسي بحساب محدد من دليل الحسابات</li>
                  <li>• عند الربط، يتم تحديث كود الحساب تلقائياً في جدول الحسابات الأساسية</li>
                  <li>• يُفضل اختيار الحسابات التفصيلية (المستوى 3 أو أعلى) للربط</li>
                  <li>• الحسابات المطلوبة يجب ربطها لضمان عمل النظام بشكل صحيح</li>
                  <li>• يمكن إلغاء الربط في أي وقت عن طريق اختيار "إلغاء الربط"</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}