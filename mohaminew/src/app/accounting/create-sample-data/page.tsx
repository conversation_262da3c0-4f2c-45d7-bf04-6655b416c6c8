'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, AlertCircle, Loader2, Database, FileText } from 'lucide-react'
import MainLayout from '@/components/layout/main-layout'

interface CreateResult {
  success: boolean
  message: string
  accountsCreated?: number
  error?: string
}

export default function CreateSampleDataPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<CreateResult | null>(null)

  const handleCreateSampleData = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/accounting/create-sample-accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        message: 'فشل في الاتصال بالخادم',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="space-y-6 p-6 bg-white min-h-screen">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إنشاء بيانات تجريبية</h1>
              <p className="text-gray-600 mt-2">
                إنشاء دليل حسابات تجريبي للنظام القانوني
              </p>
            </div>
          </div>

          <div className="grid gap-6">
            {/* شرح البيانات التجريبية */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="h-6 w-6 mr-3 text-blue-600" />
                  البيانات التجريبية
                </CardTitle>
                <CardDescription>
                  سيتم إنشاء دليل حسابات شامل يتضمن جميع أنواع الحسابات المطلوبة للنظام القانوني
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-green-800">الأصول:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• النقدية والبنوك</li>
                      <li>• العملاء (حساب رئيسي)</li>
                      <li>• الأصول الثابتة</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold text-blue-800">الخصوم:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• الموظفين (حساب رئيسي)</li>
                      <li>• الموردين (حساب رئيسي)</li>
                      <li>• الضرائب المستحقة</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold text-purple-800">الإيرادات:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• أتعاب الاستشارات</li>
                      <li>• أتعاب التقاضي</li>
                      <li>• أتعاب صياغة العقود</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-semibold text-red-800">المصروفات:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• رواتب الموظفين</li>
                      <li>• إيجار المكتب</li>
                      <li>• المصروفات التشغيلية</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* زر الإنشاء */}
            <Card>
              <CardHeader>
                <CardTitle>إنشاء البيانات</CardTitle>
                <CardDescription>
                  اضغط على الزر أدناه لإنشاء دليل الحسابات التجريبي
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={handleCreateSampleData} 
                  disabled={isLoading}
                  className="w-full md:w-auto"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      جاري الإنشاء...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      إنشاء البيانات التجريبية
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* النتائج */}
            {result && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    {result.success ? (
                      <CheckCircle className="h-6 w-6 mr-3 text-green-600" />
                    ) : (
                      <AlertCircle className="h-6 w-6 mr-3 text-red-600" />
                    )}
                    نتائج الإنشاء
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                    <AlertDescription className={result.success ? 'text-green-800' : 'text-red-800'}>
                      {result.message}
                    </AlertDescription>
                  </Alert>

                  {result.success && result.accountsCreated && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold mb-2">تفاصيل الإنشاء:</h4>
                      <p className="text-sm">تم إنشاء {result.accountsCreated} حساب بنجاح</p>
                    </div>
                  )}

                  {!result.success && result.error && (
                    <div className="mt-4 p-4 bg-red-50 rounded-lg">
                      <h4 className="font-semibold text-red-800 mb-2">تفاصيل الخطأ:</h4>
                      <p className="text-sm text-red-600">{result.error}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* الخطوات التالية */}
            {result?.success && (
              <Card>
                <CardHeader>
                  <CardTitle>الخطوات التالية</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <p>✅ تم إنشاء دليل الحسابات التجريبي بنجاح</p>
                    <p>✅ يمكنك الآن الذهاب إلى دليل الحسابات لمراجعة البيانات</p>
                    <p>✅ يمكنك تطبيق التصميم المحاسبي لربط العملاء والموظفين</p>
                  </div>
                  
                  <div className="mt-4 space-x-2 space-x-reverse">
                    <Button 
                      onClick={() => window.location.href = '/accounting/chart-of-accounts'}
                      variant="outline"
                    >
                      عرض دليل الحسابات
                    </Button>
                    <Button 
                      onClick={() => window.location.href = '/accounting/implement-structure'}
                      variant="outline"
                    >
                      تطبيق التصميم المحاسبي
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
