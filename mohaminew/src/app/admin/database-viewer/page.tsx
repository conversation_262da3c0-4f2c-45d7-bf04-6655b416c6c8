'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Database, Table, RefreshCw, Eye, BarChart3 } from 'lucide-react'

interface TableInfo {
  table_name: string
  column_count: number
  row_count: number
  table_size: string
}

interface ColumnInfo {
  column_name: string
  data_type: string
  is_nullable: string
  column_default: string
}

export default function DatabaseViewer() {
  const [tables, setTables] = useState<TableInfo[]>([])
  const [selectedTable, setSelectedTable] = useState<string>('')
  const [columns, setColumns] = useState<ColumnInfo[]>([])
  const [tableData, setTableData] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [stats, setStats] = useState<any>({})

  // جلب قائمة الجداول
  const fetchTables = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/database/tables')
      const data = await response.json()
      if (data.success) {
        setTables(data.tables)
      }
    } catch (error) {
      console.error('Error fetching tables:', error)
    } finally {
      setLoading(false)
    }
  }

  // جلب تفاصيل الجدول
  const fetchTableDetails = async (tableName: string) => {
    setLoading(true)
    try {
      const [columnsRes, dataRes] = await Promise.all([
        fetch(`/api/database/columns?table=${tableName}`),
        fetch(`/api/database/data?table=${tableName}&limit=50`)
      ])
      
      const columnsData = await columnsRes.json()
      const tableDataRes = await dataRes.json()
      
      if (columnsData.success) setColumns(columnsData.columns)
      if (tableDataRes.success) setTableData(tableDataRes.data)
      
    } catch (error) {
      console.error('Error fetching table details:', error)
    } finally {
      setLoading(false)
    }
  }

  // جلب إحصائيات قاعدة البيانات
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/database/stats')
      const data = await response.json()
      if (data.success) {
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  useEffect(() => {
    fetchTables()
    fetchStats()
  }, [])

  useEffect(() => {
    if (selectedTable) {
      fetchTableDetails(selectedTable)
    }
  }, [selectedTable])

  return (
    <div className="container mx-auto p-6 space-y-6" dir="rtl">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 space-x-reverse">
          <Database className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold">مستعرض قاعدة البيانات</h1>
        </div>
        <Button onClick={() => { fetchTables(); fetchStats() }} disabled={loading}>
          <RefreshCw className={`h-4 w-4 ml-2 ${loading ? 'animate-spin' : ''}`} />
          تحديث
        </Button>
      </div>

      {/* إحصائيات عامة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الجداول</p>
                <p className="text-2xl font-bold">{tables.length}</p>
              </div>
              <Table className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">حجم قاعدة البيانات</p>
                <p className="text-2xl font-bold">{stats.database_size || 'N/A'}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي السجلات</p>
                <p className="text-2xl font-bold">{stats.total_rows || 'N/A'}</p>
              </div>
              <Database className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الجدول المحدد</p>
                <p className="text-lg font-bold">{selectedTable || 'لا يوجد'}</p>
              </div>
              <Eye className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* قائمة الجداول */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Table className="h-5 w-5 ml-2" />
              الجداول ({tables.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {tables.map((table) => (
                <div
                  key={table.table_name}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedTable === table.table_name
                      ? 'bg-blue-50 border-blue-200'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedTable(table.table_name)}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{table.table_name}</span>
                    <Badge variant="secondary">{table.row_count} سجل</Badge>
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    {table.column_count} عمود • {table.table_size}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* تفاصيل الجدول */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="h-5 w-5 ml-2" />
              {selectedTable ? `تفاصيل جدول: ${selectedTable}` : 'اختر جدولاً لعرض التفاصيل'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedTable ? (
              <div className="space-y-4">
                {/* أعمدة الجدول */}
                <div>
                  <h3 className="font-semibold mb-2">الأعمدة ({columns.length})</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                    {columns.map((column) => (
                      <div key={column.column_name} className="p-2 bg-gray-50 rounded">
                        <div className="font-medium">{column.column_name}</div>
                        <div className="text-sm text-gray-600">
                          {column.data_type} • {column.is_nullable === 'YES' ? 'اختياري' : 'مطلوب'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* بيانات الجدول */}
                <div>
                  <h3 className="font-semibold mb-2">البيانات (أول 50 سجل)</h3>
                  <div className="overflow-x-auto max-h-64 overflow-y-auto">
                    <table className="min-w-full border border-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          {columns.slice(0, 5).map((column) => (
                            <th key={column.column_name} className="px-3 py-2 text-right text-xs font-medium text-gray-500 border-b">
                              {column.column_name}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {tableData.slice(0, 10).map((row, index) => (
                          <tr key={index} className="hover:bg-gray-50">
                            {columns.slice(0, 5).map((column) => (
                              <td key={column.column_name} className="px-3 py-2 text-sm border-b">
                                {String(row[column.column_name] || '').slice(0, 50)}
                                {String(row[column.column_name] || '').length > 50 ? '...' : ''}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                اختر جدولاً من القائمة لعرض تفاصيله
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
