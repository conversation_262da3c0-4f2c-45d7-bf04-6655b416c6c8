--
-- PostgreSQL database dump
--

-- Dumped from database version 14.18 (Ubuntu 14.18-1.pgdg24.04+1)
-- Dumped by pg_dump version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_employee_id_fkey;
ALTER TABLE IF EXISTS ONLY public.user_role_assignments DROP CONSTRAINT IF EXISTS user_role_assignments_user_id_fkey;
ALTER TABLE IF EXISTS ONLY public.user_role_assignments DROP CONSTRAINT IF EXISTS user_role_assignments_role_name_fkey;
ALTER TABLE IF EXISTS ONLY public.user_role_assignments DROP CONSTRAINT IF EXISTS user_role_assignments_assigned_by_fkey;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS user_permissions_user_id_fkey;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS user_permissions_granted_by_fkey;
ALTER TABLE IF EXISTS ONLY public.timecard DROP CONSTRAINT IF EXISTS timecard_project_id_fkey;
ALTER TABLE IF EXISTS ONLY public.time_entries DROP CONSTRAINT IF EXISTS time_entries_employee_id_fkey;
ALTER TABLE IF EXISTS ONLY public.time_entries DROP CONSTRAINT IF EXISTS time_entries_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.time_entries DROP CONSTRAINT IF EXISTS time_entries_case_id_fkey;
ALTER TABLE IF EXISTS ONLY public.suppliers DROP CONSTRAINT IF EXISTS suppliers_account_id_fkey;
ALTER TABLE IF EXISTS ONLY public.services DROP CONSTRAINT IF EXISTS services_lineage_id_fkey;
ALTER TABLE IF EXISTS ONLY public.service_distributions DROP CONSTRAINT IF EXISTS service_distributions_case_distribution_id_fkey;
ALTER TABLE IF EXISTS ONLY public.payment_vouchers DROP CONSTRAINT IF EXISTS payment_vouchers_debit_account_id_fkey;
ALTER TABLE IF EXISTS ONLY public.payment_vouchers DROP CONSTRAINT IF EXISTS payment_vouchers_credit_account_id_fkey;
ALTER TABLE IF EXISTS ONLY public.payment_links DROP CONSTRAINT IF EXISTS payment_links_payment_id_fkey;
ALTER TABLE IF EXISTS ONLY public.payment_links DROP CONSTRAINT IF EXISTS payment_links_entry_id_fkey;
ALTER TABLE IF EXISTS ONLY public.payment DROP CONSTRAINT IF EXISTS payment_gl_id_fkey;
ALTER TABLE IF EXISTS ONLY public.movements DROP CONSTRAINT IF EXISTS movements_case_id_fkey;
ALTER TABLE IF EXISTS ONLY public.messages DROP CONSTRAINT IF EXISTS messages_reply_to_message_id_fkey;
ALTER TABLE IF EXISTS ONLY public.messages DROP CONSTRAINT IF EXISTS messages_conversation_id_fkey;
ALTER TABLE IF EXISTS ONLY public.message_read_status DROP CONSTRAINT IF EXISTS message_read_status_message_id_fkey;
ALTER TABLE IF EXISTS ONLY public.main_accounts DROP CONSTRAINT IF EXISTS main_accounts_chart_account_id_fkey;
ALTER TABLE IF EXISTS ONLY public.lawyer_earnings DROP CONSTRAINT IF EXISTS lawyer_earnings_follow_id_fkey;
ALTER TABLE IF EXISTS ONLY public.lawyer_earnings DROP CONSTRAINT IF EXISTS lawyer_earnings_case_id_fkey;
ALTER TABLE IF EXISTS ONLY public.journal_entry_details DROP CONSTRAINT IF EXISTS journal_entry_details_journal_entry_id_fkey;
ALTER TABLE IF EXISTS ONLY public.issues DROP CONSTRAINT IF EXISTS issues_issue_type_id_fkey;
ALTER TABLE IF EXISTS ONLY public.issues DROP CONSTRAINT IF EXISTS issues_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.invoices DROP CONSTRAINT IF EXISTS invoices_created_by_fkey;
ALTER TABLE IF EXISTS ONLY public.invoices DROP CONSTRAINT IF EXISTS invoices_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.invoice DROP CONSTRAINT IF EXISTS invoice_trans_id_fkey;
ALTER TABLE IF EXISTS ONLY public.invoice_items DROP CONSTRAINT IF EXISTS invoice_items_time_entry_id_fkey;
ALTER TABLE IF EXISTS ONLY public.invoice_items DROP CONSTRAINT IF EXISTS invoice_items_invoice_id_fkey;
ALTER TABLE IF EXISTS ONLY public.invoice_items DROP CONSTRAINT IF EXISTS invoice_items_case_id_fkey;
ALTER TABLE IF EXISTS ONLY public.hearings DROP CONSTRAINT IF EXISTS hearings_issue_id_fkey;
ALTER TABLE IF EXISTS ONLY public.follows DROP CONSTRAINT IF EXISTS follows_next_hearing_id_fkey;
ALTER TABLE IF EXISTS ONLY public.follows DROP CONSTRAINT IF EXISTS follows_case_id_fkey;
ALTER TABLE IF EXISTS ONLY public.issues DROP CONSTRAINT IF EXISTS fk_issues_client_id;
ALTER TABLE IF EXISTS ONLY public.employees DROP CONSTRAINT IF EXISTS employees_governorate_id_fkey;
ALTER TABLE IF EXISTS ONLY public.employees DROP CONSTRAINT IF EXISTS employees_department_id_fkey;
ALTER TABLE IF EXISTS ONLY public.employees DROP CONSTRAINT IF EXISTS employees_branch_id_fkey;
ALTER TABLE IF EXISTS ONLY public.employees DROP CONSTRAINT IF EXISTS employees_account_id_fkey;
ALTER TABLE IF EXISTS ONLY public.documents DROP CONSTRAINT IF EXISTS documents_uploaded_by_fkey;
ALTER TABLE IF EXISTS ONLY public.documents DROP CONSTRAINT IF EXISTS documents_employee_id_fkey;
ALTER TABLE IF EXISTS ONLY public.documents DROP CONSTRAINT IF EXISTS documents_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.documents DROP CONSTRAINT IF EXISTS documents_case_id_fkey;
ALTER TABLE IF EXISTS ONLY public.document_versions DROP CONSTRAINT IF EXISTS document_versions_uploaded_by_fkey;
ALTER TABLE IF EXISTS ONLY public.document_versions DROP CONSTRAINT IF EXISTS document_versions_document_id_fkey;
ALTER TABLE IF EXISTS ONLY public.document_shares DROP CONSTRAINT IF EXISTS document_shares_shared_with_user_fkey;
ALTER TABLE IF EXISTS ONLY public.document_shares DROP CONSTRAINT IF EXISTS document_shares_shared_with_client_fkey;
ALTER TABLE IF EXISTS ONLY public.document_shares DROP CONSTRAINT IF EXISTS document_shares_shared_by_fkey;
ALTER TABLE IF EXISTS ONLY public.document_shares DROP CONSTRAINT IF EXISTS document_shares_document_id_fkey;
ALTER TABLE IF EXISTS ONLY public.cost_centers DROP CONSTRAINT IF EXISTS cost_centers_parent_id_fkey;
ALTER TABLE IF EXISTS ONLY public.conversations DROP CONSTRAINT IF EXISTS conversations_user_id_fkey;
ALTER TABLE IF EXISTS ONLY public.conversations DROP CONSTRAINT IF EXISTS conversations_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.clients DROP CONSTRAINT IF EXISTS clients_account_id_fkey;
ALTER TABLE IF EXISTS ONLY public.client_sessions DROP CONSTRAINT IF EXISTS client_sessions_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.client_requests DROP CONSTRAINT IF EXISTS client_requests_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.client_requests DROP CONSTRAINT IF EXISTS client_requests_case_id_fkey;
ALTER TABLE IF EXISTS ONLY public.client_requests DROP CONSTRAINT IF EXISTS client_requests_assigned_to_fkey;
ALTER TABLE IF EXISTS ONLY public.client_portal_accounts DROP CONSTRAINT IF EXISTS client_portal_accounts_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.client_notifications DROP CONSTRAINT IF EXISTS client_notifications_document_id_fkey;
ALTER TABLE IF EXISTS ONLY public.client_notifications DROP CONSTRAINT IF EXISTS client_notifications_client_id_fkey;
ALTER TABLE IF EXISTS ONLY public.client_notifications DROP CONSTRAINT IF EXISTS client_notifications_case_id_fkey;
ALTER TABLE IF EXISTS ONLY public.chart_of_accounts DROP CONSTRAINT IF EXISTS chart_of_accounts_parent_id_fkey;
ALTER TABLE IF EXISTS ONLY public.case_distribution DROP CONSTRAINT IF EXISTS case_distribution_lineage_id_fkey;
ALTER TABLE IF EXISTS ONLY public.branches DROP CONSTRAINT IF EXISTS branches_governorate_id_fkey;
ALTER TABLE IF EXISTS ONLY public.accounts DROP CONSTRAINT IF EXISTS accounts_parent_id_fkey;
ALTER TABLE IF EXISTS ONLY public.accounting_transaction_details DROP CONSTRAINT IF EXISTS accounting_transaction_details_transaction_id_fkey;
ALTER TABLE IF EXISTS ONLY public.acc_trans DROP CONSTRAINT IF EXISTS acc_trans_trans_id_fkey;
DROP TRIGGER IF EXISTS trigger_update_parent_balance_suppliers ON public.suppliers;
DROP TRIGGER IF EXISTS trigger_update_parent_balance_employees ON public.employees;
DROP TRIGGER IF EXISTS trigger_update_parent_balance_clients ON public.clients;
DROP TRIGGER IF EXISTS trigger_update_employee_account_name ON public.employees;
DROP TRIGGER IF EXISTS trigger_update_conversation_last_message ON public.messages;
DROP TRIGGER IF EXISTS trigger_update_client_account_name ON public.clients;
DROP TRIGGER IF EXISTS trigger_create_supplier_account ON public.suppliers;
DROP TRIGGER IF EXISTS trigger_create_employee_account ON public.employees;
DROP TRIGGER IF EXISTS trigger_create_client_account ON public.clients;
DROP TRIGGER IF EXISTS trigger_auto_link_employee_account ON public.employees;
DROP TRIGGER IF EXISTS trigger_auto_link_client_account ON public.clients;
DROP INDEX IF EXISTS public.idx_vouchers_type;
DROP INDEX IF EXISTS public.idx_vouchers_date;
DROP INDEX IF EXISTS public.idx_users_username;
DROP INDEX IF EXISTS public.idx_users_online;
DROP INDEX IF EXISTS public.idx_users_last_login;
DROP INDEX IF EXISTS public.idx_users_device_id;
DROP INDEX IF EXISTS public.idx_user_role_assignments_user_id;
DROP INDEX IF EXISTS public.idx_user_role_assignments_role_name;
DROP INDEX IF EXISTS public.idx_timecard_project_id;
DROP INDEX IF EXISTS public.idx_timecard_employee_id;
DROP INDEX IF EXISTS public.idx_time_entries_employee_id;
DROP INDEX IF EXISTS public.idx_time_entries_date;
DROP INDEX IF EXISTS public.idx_time_entries_case_id;
DROP INDEX IF EXISTS public.idx_suppliers_account_id;
DROP INDEX IF EXISTS public.idx_serviceslow_sort;
DROP INDEX IF EXISTS public.idx_serviceslow_slug;
DROP INDEX IF EXISTS public.idx_serviceslow_active;
DROP INDEX IF EXISTS public.idx_project_customer_id;
DROP INDEX IF EXISTS public.idx_payment_vouchers_status;
DROP INDEX IF EXISTS public.idx_payment_vouchers_payee_type;
DROP INDEX IF EXISTS public.idx_payment_vouchers_entry_date;
DROP INDEX IF EXISTS public.idx_payment_entity_id;
DROP INDEX IF EXISTS public.idx_notifications_recipient;
DROP INDEX IF EXISTS public.idx_notifications_is_read;
DROP INDEX IF EXISTS public.idx_notifications_created_at;
DROP INDEX IF EXISTS public.idx_messages_sender;
DROP INDEX IF EXISTS public.idx_messages_reply_to;
DROP INDEX IF EXISTS public.idx_messages_is_read;
DROP INDEX IF EXISTS public.idx_messages_created_at;
DROP INDEX IF EXISTS public.idx_messages_conversation_id;
DROP INDEX IF EXISTS public.idx_message_read_status_reader;
DROP INDEX IF EXISTS public.idx_message_read_status_message;
DROP INDEX IF EXISTS public.idx_main_accounts_name;
DROP INDEX IF EXISTS public.idx_main_accounts_code;
DROP INDEX IF EXISTS public.idx_lawyer_earnings_service_id;
DROP INDEX IF EXISTS public.idx_lawyer_earnings_lawyer_id;
DROP INDEX IF EXISTS public.idx_lawyer_earnings_case_id;
DROP INDEX IF EXISTS public.idx_journal_entry_details_account;
DROP INDEX IF EXISTS public.idx_journal_entries_type;
DROP INDEX IF EXISTS public.idx_journal_entries_status;
DROP INDEX IF EXISTS public.idx_journal_entries_date;
DROP INDEX IF EXISTS public.idx_issues_status;
DROP INDEX IF EXISTS public.idx_issues_created_date;
DROP INDEX IF EXISTS public.idx_issues_court_id;
DROP INDEX IF EXISTS public.idx_issues_client_id;
DROP INDEX IF EXISTS public.idx_issues_case_number;
DROP INDEX IF EXISTS public.idx_invoices_status;
DROP INDEX IF EXISTS public.idx_invoices_date;
DROP INDEX IF EXISTS public.idx_invoices_client_id;
DROP INDEX IF EXISTS public.idx_gl_transdate;
DROP INDEX IF EXISTS public.idx_follows_user_id;
DROP INDEX IF EXISTS public.idx_follows_approved;
DROP INDEX IF EXISTS public.idx_employees_account_id_not_null;
DROP INDEX IF EXISTS public.idx_employees_account_id;
DROP INDEX IF EXISTS public.idx_documents_tags;
DROP INDEX IF EXISTS public.idx_documents_content_text;
DROP INDEX IF EXISTS public.idx_documents_client_id;
DROP INDEX IF EXISTS public.idx_documents_category;
DROP INDEX IF EXISTS public.idx_documents_case_id;
DROP INDEX IF EXISTS public.idx_currencies_code;
DROP INDEX IF EXISTS public.idx_cost_centers_parent;
DROP INDEX IF EXISTS public.idx_cost_centers_code;
DROP INDEX IF EXISTS public.idx_cost_centers_active;
DROP INDEX IF EXISTS public.idx_conversations_user_id;
DROP INDEX IF EXISTS public.idx_conversations_status;
DROP INDEX IF EXISTS public.idx_conversations_last_message;
DROP INDEX IF EXISTS public.idx_conversations_client_id;
DROP INDEX IF EXISTS public.idx_clients_username;
DROP INDEX IF EXISTS public.idx_clients_online;
DROP INDEX IF EXISTS public.idx_clients_last_login;
DROP INDEX IF EXISTS public.idx_clients_account_id_not_null;
DROP INDEX IF EXISTS public.idx_clients_account_id;
DROP INDEX IF EXISTS public.idx_client_notifications_read;
DROP INDEX IF EXISTS public.idx_client_notifications_client_id;
DROP INDEX IF EXISTS public.idx_chart_parent_id;
DROP INDEX IF EXISTS public.idx_chart_linked_table;
DROP INDEX IF EXISTS public.idx_chart_linked_record;
DROP INDEX IF EXISTS public.idx_chart_accounts_type;
DROP INDEX IF EXISTS public.idx_chart_accounts_parent;
DROP INDEX IF EXISTS public.idx_chart_accounts_linked_table;
DROP INDEX IF EXISTS public.idx_chart_accounts_code;
DROP INDEX IF EXISTS public.idx_chart_account_level;
DROP INDEX IF EXISTS public.idx_chart_account_code;
DROP INDEX IF EXISTS public.idx_ar_transdate;
DROP INDEX IF EXISTS public.idx_ar_entity_id;
DROP INDEX IF EXISTS public.idx_announcements_created_date;
DROP INDEX IF EXISTS public.idx_announcements_active;
DROP INDEX IF EXISTS public.idx_ai_settings_enabled;
DROP INDEX IF EXISTS public.idx_accounts_type;
DROP INDEX IF EXISTS public.idx_accounts_code;
DROP INDEX IF EXISTS public.idx_accounting_transactions_type;
DROP INDEX IF EXISTS public.idx_accounting_transactions_status;
DROP INDEX IF EXISTS public.idx_accounting_transactions_date;
DROP INDEX IF EXISTS public.idx_accounting_transaction_details_transaction;
DROP INDEX IF EXISTS public.idx_accounting_transaction_details_account;
DROP INDEX IF EXISTS public.idx_acc_trans_transdate;
DROP INDEX IF EXISTS public.idx_acc_trans_trans_id;
DROP INDEX IF EXISTS public.idx_acc_trans_chart_id;
ALTER TABLE IF EXISTS ONLY public.website_services DROP CONSTRAINT IF EXISTS website_services_slug_key;
ALTER TABLE IF EXISTS ONLY public.website_services DROP CONSTRAINT IF EXISTS website_services_pkey;
ALTER TABLE IF EXISTS ONLY public.vouchers DROP CONSTRAINT IF EXISTS vouchers_voucher_number_key;
ALTER TABLE IF EXISTS ONLY public.vouchers DROP CONSTRAINT IF EXISTS vouchers_pkey;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_username_key;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_pkey;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_email_key;
ALTER TABLE IF EXISTS ONLY public.user_roles DROP CONSTRAINT IF EXISTS user_roles_role_name_key;
ALTER TABLE IF EXISTS ONLY public.user_roles DROP CONSTRAINT IF EXISTS user_roles_pkey;
ALTER TABLE IF EXISTS ONLY public.user_role_assignments DROP CONSTRAINT IF EXISTS user_role_assignments_user_id_role_name_key;
ALTER TABLE IF EXISTS ONLY public.user_role_assignments DROP CONSTRAINT IF EXISTS user_role_assignments_pkey;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS user_permissions_user_id_permission_key_key;
ALTER TABLE IF EXISTS ONLY public.user_permissions DROP CONSTRAINT IF EXISTS user_permissions_pkey;
ALTER TABLE IF EXISTS ONLY public.chart_of_accounts DROP CONSTRAINT IF EXISTS unique_level_code;
ALTER TABLE IF EXISTS ONLY public.timecard DROP CONSTRAINT IF EXISTS timecard_pkey;
ALTER TABLE IF EXISTS ONLY public.time_entries DROP CONSTRAINT IF EXISTS time_entries_pkey;
ALTER TABLE IF EXISTS ONLY public.test_table DROP CONSTRAINT IF EXISTS test_table_pkey;
ALTER TABLE IF EXISTS ONLY public.suppliers DROP CONSTRAINT IF EXISTS suppliers_pkey;
ALTER TABLE IF EXISTS ONLY public.serviceslow DROP CONSTRAINT IF EXISTS serviceslow_slug_key;
ALTER TABLE IF EXISTS ONLY public.serviceslow DROP CONSTRAINT IF EXISTS serviceslow_pkey;
ALTER TABLE IF EXISTS ONLY public.services DROP CONSTRAINT IF EXISTS services_pkey;
ALTER TABLE IF EXISTS ONLY public.service_distributions DROP CONSTRAINT IF EXISTS service_distributions_pkey;
ALTER TABLE IF EXISTS ONLY public.security_logs DROP CONSTRAINT IF EXISTS security_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.receipt_vouchers DROP CONSTRAINT IF EXISTS receipt_vouchers_pkey;
ALTER TABLE IF EXISTS ONLY public.receipt_vouchers DROP CONSTRAINT IF EXISTS receipt_vouchers_entry_number_key;
ALTER TABLE IF EXISTS ONLY public.public_announcements DROP CONSTRAINT IF EXISTS public_announcements_pkey;
ALTER TABLE IF EXISTS ONLY public.project DROP CONSTRAINT IF EXISTS project_projectnumber_key;
ALTER TABLE IF EXISTS ONLY public.project DROP CONSTRAINT IF EXISTS project_pkey;
ALTER TABLE IF EXISTS ONLY public.permissions DROP CONSTRAINT IF EXISTS permissions_pkey;
ALTER TABLE IF EXISTS ONLY public.permissions DROP CONSTRAINT IF EXISTS permissions_permission_key_key;
ALTER TABLE IF EXISTS ONLY public.payment_vouchers DROP CONSTRAINT IF EXISTS payment_vouchers_pkey;
ALTER TABLE IF EXISTS ONLY public.payment_vouchers DROP CONSTRAINT IF EXISTS payment_vouchers_entry_number_key;
ALTER TABLE IF EXISTS ONLY public.payment DROP CONSTRAINT IF EXISTS payment_pkey;
ALTER TABLE IF EXISTS ONLY public.payment_methods DROP CONSTRAINT IF EXISTS payment_methods_pkey;
ALTER TABLE IF EXISTS ONLY public.payment_methods DROP CONSTRAINT IF EXISTS payment_methods_method_code_key;
ALTER TABLE IF EXISTS ONLY public.payment_links DROP CONSTRAINT IF EXISTS payment_links_pkey;
ALTER TABLE IF EXISTS ONLY public.opening_balances DROP CONSTRAINT IF EXISTS opening_balances_pkey;
ALTER TABLE IF EXISTS ONLY public.opening_balances_history DROP CONSTRAINT IF EXISTS opening_balances_history_pkey;
ALTER TABLE IF EXISTS ONLY public.notifications DROP CONSTRAINT IF EXISTS notifications_pkey;
ALTER TABLE IF EXISTS ONLY public.navigation_pages DROP CONSTRAINT IF EXISTS navigation_pages_pkey;
ALTER TABLE IF EXISTS ONLY public.movements DROP CONSTRAINT IF EXISTS movements_pkey;
ALTER TABLE IF EXISTS ONLY public.money_transactions DROP CONSTRAINT IF EXISTS money_transactions_pkey;
ALTER TABLE IF EXISTS ONLY public.messages DROP CONSTRAINT IF EXISTS messages_pkey;
ALTER TABLE IF EXISTS ONLY public.message_read_status DROP CONSTRAINT IF EXISTS message_read_status_pkey;
ALTER TABLE IF EXISTS ONLY public.message_read_status DROP CONSTRAINT IF EXISTS message_read_status_message_id_reader_type_reader_id_key;
ALTER TABLE IF EXISTS ONLY public.main_accounts DROP CONSTRAINT IF EXISTS main_accounts_pkey;
ALTER TABLE IF EXISTS ONLY public.lineages DROP CONSTRAINT IF EXISTS lineages_pkey;
ALTER TABLE IF EXISTS ONLY public.lawyer_earnings DROP CONSTRAINT IF EXISTS lawyer_earnings_pkey;
ALTER TABLE IF EXISTS ONLY public.journal_entry_details DROP CONSTRAINT IF EXISTS journal_entry_details_pkey;
ALTER TABLE IF EXISTS ONLY public.journal_entries DROP CONSTRAINT IF EXISTS journal_entries_pkey;
ALTER TABLE IF EXISTS ONLY public.journal_entries DROP CONSTRAINT IF EXISTS journal_entries_entry_number_key;
ALTER TABLE IF EXISTS ONLY public.issues DROP CONSTRAINT IF EXISTS issues_pkey;
ALTER TABLE IF EXISTS ONLY public.issues DROP CONSTRAINT IF EXISTS issues_case_number_key;
ALTER TABLE IF EXISTS ONLY public.issue_types DROP CONSTRAINT IF EXISTS issue_types_pkey;
ALTER TABLE IF EXISTS ONLY public.invoices DROP CONSTRAINT IF EXISTS invoices_pkey;
ALTER TABLE IF EXISTS ONLY public.invoices DROP CONSTRAINT IF EXISTS invoices_invoice_number_key;
ALTER TABLE IF EXISTS ONLY public.invoice DROP CONSTRAINT IF EXISTS invoice_pkey;
ALTER TABLE IF EXISTS ONLY public.invoice_items DROP CONSTRAINT IF EXISTS invoice_items_pkey;
ALTER TABLE IF EXISTS ONLY public.hearings DROP CONSTRAINT IF EXISTS hearings_pkey;
ALTER TABLE IF EXISTS ONLY public.governorates DROP CONSTRAINT IF EXISTS governorates_pkey;
ALTER TABLE IF EXISTS ONLY public.governorates DROP CONSTRAINT IF EXISTS governorates_code_key;
ALTER TABLE IF EXISTS ONLY public.gl DROP CONSTRAINT IF EXISTS gl_pkey;
ALTER TABLE IF EXISTS ONLY public.footer_links DROP CONSTRAINT IF EXISTS footer_links_pkey;
ALTER TABLE IF EXISTS ONLY public.follows DROP CONSTRAINT IF EXISTS follows_pkey;
ALTER TABLE IF EXISTS ONLY public.entity DROP CONSTRAINT IF EXISTS entity_pkey;
ALTER TABLE IF EXISTS ONLY public.entity DROP CONSTRAINT IF EXISTS entity_control_code_key;
ALTER TABLE IF EXISTS ONLY public.entity_class DROP CONSTRAINT IF EXISTS entity_class_pkey;
ALTER TABLE IF EXISTS ONLY public.entity_class DROP CONSTRAINT IF EXISTS entity_class_class_key;
ALTER TABLE IF EXISTS ONLY public.employees DROP CONSTRAINT IF EXISTS employees_pkey;
ALTER TABLE IF EXISTS ONLY public.employees DROP CONSTRAINT IF EXISTS employees_id_number_key;
ALTER TABLE IF EXISTS ONLY public.documents DROP CONSTRAINT IF EXISTS documents_pkey;
ALTER TABLE IF EXISTS ONLY public.document_versions DROP CONSTRAINT IF EXISTS document_versions_pkey;
ALTER TABLE IF EXISTS ONLY public.document_shares DROP CONSTRAINT IF EXISTS document_shares_pkey;
ALTER TABLE IF EXISTS ONLY public.currencies DROP CONSTRAINT IF EXISTS currencies_pkey;
ALTER TABLE IF EXISTS ONLY public.currencies DROP CONSTRAINT IF EXISTS currencies_currency_code_key;
ALTER TABLE IF EXISTS ONLY public.courts DROP CONSTRAINT IF EXISTS courts_pkey;
ALTER TABLE IF EXISTS ONLY public.cost_centers DROP CONSTRAINT IF EXISTS cost_centers_pkey;
ALTER TABLE IF EXISTS ONLY public.cost_centers DROP CONSTRAINT IF EXISTS cost_centers_center_code_key;
ALTER TABLE IF EXISTS ONLY public.conversations DROP CONSTRAINT IF EXISTS conversations_pkey;
ALTER TABLE IF EXISTS ONLY public.company DROP CONSTRAINT IF EXISTS company_pkey;
ALTER TABLE IF EXISTS ONLY public.company DROP CONSTRAINT IF EXISTS company_control_code_key;
ALTER TABLE IF EXISTS ONLY public.companies DROP CONSTRAINT IF EXISTS companies_pkey;
ALTER TABLE IF EXISTS ONLY public.clients DROP CONSTRAINT IF EXISTS clients_username_key;
ALTER TABLE IF EXISTS ONLY public.clients DROP CONSTRAINT IF EXISTS clients_pkey;
ALTER TABLE IF EXISTS ONLY public.clients DROP CONSTRAINT IF EXISTS clients_id_number_key;
ALTER TABLE IF EXISTS ONLY public.client_sessions DROP CONSTRAINT IF EXISTS client_sessions_session_token_key;
ALTER TABLE IF EXISTS ONLY public.client_sessions DROP CONSTRAINT IF EXISTS client_sessions_pkey;
ALTER TABLE IF EXISTS ONLY public.client_requests DROP CONSTRAINT IF EXISTS client_requests_pkey;
ALTER TABLE IF EXISTS ONLY public.client_portal_accounts DROP CONSTRAINT IF EXISTS client_portal_accounts_username_key;
ALTER TABLE IF EXISTS ONLY public.client_portal_accounts DROP CONSTRAINT IF EXISTS client_portal_accounts_pkey;
ALTER TABLE IF EXISTS ONLY public.client_portal_accounts DROP CONSTRAINT IF EXISTS client_portal_accounts_email_key;
ALTER TABLE IF EXISTS ONLY public.client_portal_accounts DROP CONSTRAINT IF EXISTS client_portal_accounts_client_id_key;
ALTER TABLE IF EXISTS ONLY public.client_notifications DROP CONSTRAINT IF EXISTS client_notifications_pkey;
ALTER TABLE IF EXISTS ONLY public.chart_of_accounts DROP CONSTRAINT IF EXISTS chart_of_accounts_pkey;
ALTER TABLE IF EXISTS ONLY public.case_distribution DROP CONSTRAINT IF EXISTS case_distribution_pkey;
ALTER TABLE IF EXISTS ONLY public.budget DROP CONSTRAINT IF EXISTS budget_pkey;
ALTER TABLE IF EXISTS ONLY public.budget DROP CONSTRAINT IF EXISTS budget_budget_year_account_id_key;
ALTER TABLE IF EXISTS ONLY public.branches DROP CONSTRAINT IF EXISTS branches_pkey;
ALTER TABLE IF EXISTS ONLY public.ar DROP CONSTRAINT IF EXISTS ar_pkey;
ALTER TABLE IF EXISTS ONLY public.ar DROP CONSTRAINT IF EXISTS ar_invnumber_key;
ALTER TABLE IF EXISTS ONLY public.announcements DROP CONSTRAINT IF EXISTS announcements_pkey;
ALTER TABLE IF EXISTS ONLY public.ai_settings DROP CONSTRAINT IF EXISTS ai_settings_pkey;
ALTER TABLE IF EXISTS ONLY public.accounts DROP CONSTRAINT IF EXISTS accounts_pkey;
ALTER TABLE IF EXISTS ONLY public.accounts DROP CONSTRAINT IF EXISTS accounts_account_code_key;
ALTER TABLE IF EXISTS ONLY public.accounting_transactions DROP CONSTRAINT IF EXISTS accounting_transactions_transaction_number_key;
ALTER TABLE IF EXISTS ONLY public.accounting_transactions DROP CONSTRAINT IF EXISTS accounting_transactions_pkey;
ALTER TABLE IF EXISTS ONLY public.accounting_transaction_details DROP CONSTRAINT IF EXISTS accounting_transaction_details_pkey;
ALTER TABLE IF EXISTS ONLY public.acc_trans DROP CONSTRAINT IF EXISTS acc_trans_pkey;
ALTER TABLE IF EXISTS public.website_services ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.vouchers ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.users ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.user_roles ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.user_role_assignments ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.user_permissions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.timecard ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.time_entries ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.test_table ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.suppliers ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.serviceslow ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.services ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.service_distributions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.security_logs ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.receipt_vouchers ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.public_announcements ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.project ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.permissions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.payment_vouchers ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.payment_methods ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.payment ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.opening_balances_history ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.opening_balances ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.notifications ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.navigation_pages ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.movements ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.money_transactions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.messages ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.message_read_status ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.main_accounts ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.lineages ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.lawyer_earnings ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.journal_entry_details ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.journal_entries ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.issues ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.issue_types ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.invoices ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.invoice_items ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.invoice ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.hearings ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.governorates ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.gl ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.footer_links ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.follows ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.entity_class ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.entity ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.employees ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.documents ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.document_versions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.document_shares ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.currencies ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.courts ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.cost_centers ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.conversations ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.company ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.companies ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.clients ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.client_sessions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.client_requests ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.client_portal_accounts ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.client_notifications ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.chart_of_accounts ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.case_distribution ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.budget ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.branches ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.ar ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.announcements ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.accounts ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.accounting_transactions ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.accounting_transaction_details ALTER COLUMN id DROP DEFAULT;
ALTER TABLE IF EXISTS public.acc_trans ALTER COLUMN entry_id DROP DEFAULT;
DROP SEQUENCE IF EXISTS public.website_services_id_seq;
DROP TABLE IF EXISTS public.website_services;
DROP SEQUENCE IF EXISTS public.vouchers_id_seq;
DROP TABLE IF EXISTS public.vouchers;
DROP SEQUENCE IF EXISTS public.users_id_seq;
DROP TABLE IF EXISTS public.users;
DROP SEQUENCE IF EXISTS public.user_roles_id_seq;
DROP TABLE IF EXISTS public.user_roles;
DROP SEQUENCE IF EXISTS public.user_role_assignments_id_seq;
DROP TABLE IF EXISTS public.user_role_assignments;
DROP SEQUENCE IF EXISTS public.user_permissions_id_seq;
DROP TABLE IF EXISTS public.user_permissions;
DROP SEQUENCE IF EXISTS public.timecard_id_seq;
DROP TABLE IF EXISTS public.timecard;
DROP SEQUENCE IF EXISTS public.time_entries_id_seq;
DROP TABLE IF EXISTS public.time_entries;
DROP SEQUENCE IF EXISTS public.test_table_id_seq;
DROP TABLE IF EXISTS public.test_table;
DROP SEQUENCE IF EXISTS public.suppliers_id_seq;
DROP TABLE IF EXISTS public.suppliers;
DROP SEQUENCE IF EXISTS public.serviceslow_id_seq;
DROP TABLE IF EXISTS public.serviceslow;
DROP SEQUENCE IF EXISTS public.services_id_seq;
DROP TABLE IF EXISTS public.services;
DROP SEQUENCE IF EXISTS public.service_distributions_id_seq;
DROP TABLE IF EXISTS public.service_distributions;
DROP SEQUENCE IF EXISTS public.security_logs_id_seq;
DROP TABLE IF EXISTS public.security_logs;
DROP SEQUENCE IF EXISTS public.receipt_vouchers_id_seq;
DROP TABLE IF EXISTS public.receipt_vouchers;
DROP SEQUENCE IF EXISTS public.public_announcements_id_seq;
DROP TABLE IF EXISTS public.public_announcements;
DROP SEQUENCE IF EXISTS public.project_id_seq;
DROP TABLE IF EXISTS public.project;
DROP SEQUENCE IF EXISTS public.permissions_id_seq;
DROP TABLE IF EXISTS public.permissions;
DROP SEQUENCE IF EXISTS public.payment_vouchers_id_seq;
DROP TABLE IF EXISTS public.payment_vouchers;
DROP SEQUENCE IF EXISTS public.payment_methods_id_seq;
DROP TABLE IF EXISTS public.payment_methods;
DROP TABLE IF EXISTS public.payment_links;
DROP SEQUENCE IF EXISTS public.payment_id_seq;
DROP TABLE IF EXISTS public.payment;
DROP SEQUENCE IF EXISTS public.opening_balances_id_seq;
DROP SEQUENCE IF EXISTS public.opening_balances_history_id_seq;
DROP TABLE IF EXISTS public.opening_balances_history;
DROP TABLE IF EXISTS public.opening_balances;
DROP SEQUENCE IF EXISTS public.notifications_id_seq;
DROP TABLE IF EXISTS public.notifications;
DROP SEQUENCE IF EXISTS public.navigation_pages_id_seq;
DROP TABLE IF EXISTS public.navigation_pages;
DROP SEQUENCE IF EXISTS public.movements_id_seq;
DROP TABLE IF EXISTS public.movements;
DROP SEQUENCE IF EXISTS public.money_transactions_id_seq;
DROP TABLE IF EXISTS public.money_transactions;
DROP SEQUENCE IF EXISTS public.messages_id_seq;
DROP TABLE IF EXISTS public.messages;
DROP SEQUENCE IF EXISTS public.message_read_status_id_seq;
DROP TABLE IF EXISTS public.message_read_status;
DROP SEQUENCE IF EXISTS public.main_accounts_id_seq;
DROP TABLE IF EXISTS public.main_accounts;
DROP SEQUENCE IF EXISTS public.lineages_id_seq;
DROP TABLE IF EXISTS public.lineages;
DROP SEQUENCE IF EXISTS public.lawyer_earnings_id_seq;
DROP TABLE IF EXISTS public.lawyer_earnings;
DROP SEQUENCE IF EXISTS public.journal_entry_details_id_seq;
DROP TABLE IF EXISTS public.journal_entry_details;
DROP SEQUENCE IF EXISTS public.journal_entries_id_seq;
DROP TABLE IF EXISTS public.journal_entries;
DROP SEQUENCE IF EXISTS public.issues_id_seq;
DROP TABLE IF EXISTS public.issues;
DROP SEQUENCE IF EXISTS public.issue_types_id_seq;
DROP TABLE IF EXISTS public.issue_types;
DROP SEQUENCE IF EXISTS public.invoices_id_seq;
DROP TABLE IF EXISTS public.invoices;
DROP SEQUENCE IF EXISTS public.invoice_items_id_seq;
DROP TABLE IF EXISTS public.invoice_items;
DROP SEQUENCE IF EXISTS public.invoice_id_seq;
DROP TABLE IF EXISTS public.invoice;
DROP SEQUENCE IF EXISTS public.hearings_id_seq;
DROP TABLE IF EXISTS public.hearings;
DROP SEQUENCE IF EXISTS public.governorates_id_seq;
DROP TABLE IF EXISTS public.governorates;
DROP SEQUENCE IF EXISTS public.gl_id_seq;
DROP TABLE IF EXISTS public.gl;
DROP SEQUENCE IF EXISTS public.footer_links_id_seq;
DROP TABLE IF EXISTS public.footer_links;
DROP SEQUENCE IF EXISTS public.follows_id_seq;
DROP TABLE IF EXISTS public.follows;
DROP SEQUENCE IF EXISTS public.entity_id_seq;
DROP SEQUENCE IF EXISTS public.entity_class_id_seq;
DROP TABLE IF EXISTS public.entity_class;
DROP TABLE IF EXISTS public.entity;
DROP SEQUENCE IF EXISTS public.employees_id_seq;
DROP TABLE IF EXISTS public.employees;
DROP SEQUENCE IF EXISTS public.documents_id_seq;
DROP TABLE IF EXISTS public.documents;
DROP SEQUENCE IF EXISTS public.document_versions_id_seq;
DROP TABLE IF EXISTS public.document_versions;
DROP SEQUENCE IF EXISTS public.document_shares_id_seq;
DROP TABLE IF EXISTS public.document_shares;
DROP SEQUENCE IF EXISTS public.currencies_id_seq;
DROP TABLE IF EXISTS public.currencies;
DROP SEQUENCE IF EXISTS public.courts_id_seq;
DROP TABLE IF EXISTS public.courts;
DROP SEQUENCE IF EXISTS public.cost_centers_id_seq;
DROP TABLE IF EXISTS public.cost_centers;
DROP SEQUENCE IF EXISTS public.conversations_id_seq;
DROP TABLE IF EXISTS public.conversations;
DROP SEQUENCE IF EXISTS public.company_id_seq;
DROP TABLE IF EXISTS public.company;
DROP SEQUENCE IF EXISTS public.companies_id_seq;
DROP TABLE IF EXISTS public.companies;
DROP SEQUENCE IF EXISTS public.clients_id_seq;
DROP TABLE IF EXISTS public.clients;
DROP SEQUENCE IF EXISTS public.client_sessions_id_seq;
DROP TABLE IF EXISTS public.client_sessions;
DROP SEQUENCE IF EXISTS public.client_requests_id_seq;
DROP TABLE IF EXISTS public.client_requests;
DROP SEQUENCE IF EXISTS public.client_portal_accounts_id_seq;
DROP TABLE IF EXISTS public.client_portal_accounts;
DROP SEQUENCE IF EXISTS public.client_notifications_id_seq;
DROP TABLE IF EXISTS public.client_notifications;
DROP SEQUENCE IF EXISTS public.chart_of_accounts_id_seq;
DROP TABLE IF EXISTS public.chart_of_accounts;
DROP SEQUENCE IF EXISTS public.case_distribution_id_seq;
DROP TABLE IF EXISTS public.case_distribution;
DROP SEQUENCE IF EXISTS public.budget_id_seq;
DROP TABLE IF EXISTS public.budget;
DROP SEQUENCE IF EXISTS public.branches_id_seq;
DROP TABLE IF EXISTS public.branches;
DROP SEQUENCE IF EXISTS public.ar_id_seq;
DROP TABLE IF EXISTS public.ar;
DROP SEQUENCE IF EXISTS public.announcements_id_seq;
DROP TABLE IF EXISTS public.announcements;
DROP TABLE IF EXISTS public.ai_settings;
DROP SEQUENCE IF EXISTS public.accounts_id_seq;
DROP TABLE IF EXISTS public.accounts;
DROP SEQUENCE IF EXISTS public.accounting_transactions_id_seq;
DROP TABLE IF EXISTS public.accounting_transactions;
DROP SEQUENCE IF EXISTS public.accounting_transaction_details_id_seq;
DROP TABLE IF EXISTS public.accounting_transaction_details;
DROP SEQUENCE IF EXISTS public.acc_trans_entry_id_seq;
DROP TABLE IF EXISTS public.acc_trans;
DROP FUNCTION IF EXISTS public.update_parent_balance_on_supplier_change();
DROP FUNCTION IF EXISTS public.update_parent_balance_on_employee_change();
DROP FUNCTION IF EXISTS public.update_parent_balance_on_client_change();
DROP FUNCTION IF EXISTS public.update_parent_account_balance(p_account_id integer);
DROP FUNCTION IF EXISTS public.update_employee_account_name();
DROP FUNCTION IF EXISTS public.update_conversation_last_message();
DROP FUNCTION IF EXISTS public.update_client_account_name();
DROP FUNCTION IF EXISTS public.recalculate_main_accounts();
DROP FUNCTION IF EXISTS public.get_user_combined_permissions(user_id_param integer);
DROP FUNCTION IF EXISTS public.get_account_balance(p_account_code character varying);
DROP FUNCTION IF EXISTS public.generate_voucher_number(v_type character varying);
DROP FUNCTION IF EXISTS public.generate_account_code(parent_account_id integer);
DROP FUNCTION IF EXISTS public.create_supplier_account();
DROP FUNCTION IF EXISTS public.create_sub_account(p_main_account_id integer, p_table_name character varying, p_record_id integer, p_record_name character varying);
DROP FUNCTION IF EXISTS public.create_employee_account();
DROP FUNCTION IF EXISTS public.create_client_account();
DROP FUNCTION IF EXISTS public.calculate_voucher_total(v_id integer);
DROP FUNCTION IF EXISTS public.calculate_parent_account_balance(p_account_id integer);
DROP FUNCTION IF EXISTS public.auto_link_employee_account();
DROP FUNCTION IF EXISTS public.auto_link_client_account();
-- *not* dropping schema, since initdb creates it
--
-- Name: public; Type: SCHEMA; Schema: -; Owner: postgres
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO postgres;

--
-- Name: auto_link_client_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.auto_link_client_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
      DECLARE
        settings_id INTEGER;
      BEGIN
        -- البحث عن إعدادات ربط العملاء
        SELECT id INTO settings_id
        FROM account_linking_settings
        WHERE table_name = 'clients' AND is_enabled = true
        LIMIT 1;

        -- إذا تم العثور على الإعدادات، قم بالربط
        IF settings_id IS NOT NULL THEN
          NEW.account_id = settings_id;
        END IF;

        RETURN NEW;
      END;
      $$;


ALTER FUNCTION public.auto_link_client_account() OWNER TO postgres;

--
-- Name: auto_link_employee_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.auto_link_employee_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
      DECLARE
        settings_id INTEGER;
      BEGIN
        -- البحث عن إعدادات ربط الموظفين
        SELECT id INTO settings_id
        FROM account_linking_settings
        WHERE table_name = 'employees' AND is_enabled = true
        LIMIT 1;

        -- إذا تم العثور على الإعدادات، قم بالربط
        IF settings_id IS NOT NULL THEN
          NEW.account_id = settings_id;
        END IF;

        RETURN NEW;
      END;
      $$;


ALTER FUNCTION public.auto_link_employee_account() OWNER TO postgres;

--
-- Name: calculate_parent_account_balance(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.calculate_parent_account_balance(p_account_id integer) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
DECLARE
    total_balance DECIMAL(15,2) := 0;
BEGIN
    -- حساب مجموع أرصدة العملاء المرتبطين
    SELECT COALESCE(SUM(
        CASE 
            WHEN al.linked_table = 'clients' THEN COALESCE(c.current_balance, 0)
            WHEN al.linked_table = 'employees' THEN COALESCE(e.current_balance, 0) 
            WHEN al.linked_table = 'suppliers' THEN COALESCE(s.current_balance, 0)
            ELSE 0
        END
    ), 0) INTO total_balance
    FROM account_links al
    LEFT JOIN clients c ON al.linked_table = 'clients' AND al.linked_record_id = c.id
    LEFT JOIN employees e ON al.linked_table = 'employees' AND al.linked_record_id = e.id  
    LEFT JOIN suppliers s ON al.linked_table = 'suppliers' AND al.linked_record_id = s.id
    WHERE al.main_account_id = p_account_id AND al.is_active = true;
    
    RETURN total_balance;
END;
$$;


ALTER FUNCTION public.calculate_parent_account_balance(p_account_id integer) OWNER TO postgres;

--
-- Name: calculate_voucher_total(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.calculate_voucher_total(v_id integer) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
      DECLARE
        total_debit DECIMAL(15,2);
        total_credit DECIMAL(15,2);
        final_total DECIMAL(15,2);
      BEGIN
        SELECT 
          COALESCE(SUM(debit_amount), 0),
          COALESCE(SUM(credit_amount), 0)
        INTO total_debit, total_credit
        FROM financial_transactions
        WHERE voucher_id = v_id;
        
        final_total := GREATEST(total_debit, total_credit);
        
        UPDATE vouchers_master 
        SET total_amount = final_total,
            updated_date = CURRENT_TIMESTAMP
        WHERE id = v_id;
        
        RETURN final_total;
      END;
      $$;


ALTER FUNCTION public.calculate_voucher_total(v_id integer) OWNER TO postgres;

--
-- Name: create_client_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_client_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    parent_account_id INTEGER;
    new_account_code VARCHAR(20);
    new_account_id INTEGER;
BEGIN
    -- فقط إذا كان العميل نشط
    IF NEW.status != 'active' THEN
        RETURN NEW;
    END IF;
    
    -- الحصول على معرف الحساب الرئيسي للعملاء
    SELECT id INTO parent_account_id 
    FROM chart_of_accounts 
    WHERE account_code = '1121' AND linked_table = 'clients';
    
    -- إنشاء رمز الحساب الجديد
    new_account_code := '1121' || LPAD(NEW.id::text, 3, '0');
    
    -- إنشاء الحساب في دليل الحسابات
    INSERT INTO chart_of_accounts (
        account_code, account_name, account_level, account_type, account_nature,
        parent_id, allow_transactions, is_active, created_date
    ) VALUES (
        new_account_code,
        NEW.name,
        5,
        'أصول',
        'مدين',
        parent_account_id,
        true,
        true,
        NOW()
    ) ON CONFLICT (account_code) DO UPDATE SET
        account_name = NEW.name,
        is_active = true
    RETURNING id INTO new_account_id;
    
    -- إذا لم يتم إنشاء حساب جديد، احصل على المعرف الموجود
    IF new_account_id IS NULL THEN
        SELECT id INTO new_account_id 
        FROM chart_of_accounts 
        WHERE account_code = new_account_code;
    END IF;
    
    -- تحديث العميل بمعرف الحساب
    NEW.account_id := new_account_id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.create_client_account() OWNER TO postgres;

--
-- Name: create_employee_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_employee_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    parent_account_id INTEGER;
    new_account_code VARCHAR(20);
    new_account_id INTEGER;
BEGIN
    -- فقط إذا كان الموظف نشط
    IF NEW.status != 'active' THEN
        RETURN NEW;
    END IF;
    
    -- الحصول على معرف الحساب الرئيسي للموظفين
    SELECT id INTO parent_account_id 
    FROM chart_of_accounts 
    WHERE account_code = '1151' AND linked_table = 'employees';
    
    -- إنشاء رمز الحساب الجديد
    new_account_code := '1151' || LPAD(NEW.id::text, 3, '0');
    
    -- إنشاء الحساب في دليل الحسابات
    INSERT INTO chart_of_accounts (
        account_code, account_name, account_level, account_type, account_nature,
        parent_id, allow_transactions, is_active, created_date
    ) VALUES (
        new_account_code,
        NEW.name,
        5,
        'أصول',
        'مدين',
        parent_account_id,
        true,
        true,
        NOW()
    ) ON CONFLICT (account_code) DO UPDATE SET
        account_name = NEW.name,
        is_active = true
    RETURNING id INTO new_account_id;
    
    -- إذا لم يتم إنشاء حساب جديد، احصل على المعرف الموجود
    IF new_account_id IS NULL THEN
        SELECT id INTO new_account_id 
        FROM chart_of_accounts 
        WHERE account_code = new_account_code;
    END IF;
    
    -- تحديث الموظف بمعرف الحساب
    NEW.account_id := new_account_id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.create_employee_account() OWNER TO postgres;

--
-- Name: create_sub_account(integer, character varying, integer, character varying); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_sub_account(p_main_account_id integer, p_table_name character varying, p_record_id integer, p_record_name character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
      DECLARE
        v_prefix VARCHAR;
        v_sub_code VARCHAR;
        v_sub_name VARCHAR;
      BEGIN
        SELECT sub_account_prefix INTO v_prefix 
        FROM chart_of_accounts 
        WHERE id = p_main_account_id;
        
        v_sub_code := (SELECT account_code FROM chart_of_accounts WHERE id = p_main_account_id) 
                     || '-' || LPAD(p_record_id::TEXT, 4, '0');
        v_sub_name := 'حساب ' || p_record_name;
        
        INSERT INTO account_sub_links 
        (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
        VALUES (p_main_account_id, p_table_name, p_record_id, v_sub_code, v_sub_name, 'النظام')
        ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING;
        
        RETURN v_sub_code;
      END;
      $$;


ALTER FUNCTION public.create_sub_account(p_main_account_id integer, p_table_name character varying, p_record_id integer, p_record_name character varying) OWNER TO postgres;

--
-- Name: create_supplier_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_supplier_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    parent_account_id INTEGER;
    new_account_code VARCHAR(20);
BEGIN
    -- التحقق من وجود جدول الموردين
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'suppliers') THEN
        RETURN NEW;
    END IF;
    
    -- الحصول على معرف الحساب الرئيسي للموردين
    SELECT id INTO parent_account_id 
    FROM chart_of_accounts 
    WHERE account_code = '2111' AND linked_table = 'suppliers';
    
    -- إنشاء رمز الحساب الجديد
    new_account_code := '2111' || LPAD(NEW.id::text, 3, '0');
    
    -- إنشاء الحساب في دليل الحسابات
    INSERT INTO chart_of_accounts (
        account_code, account_name, account_level, account_type, account_nature,
        parent_id, allow_transactions, is_active, created_date
    ) VALUES (
        new_account_code,
        NEW.name,
        5,
        'خصوم',
        'دائن',
        parent_account_id,
        true,
        true,
        NOW()
    ) ON CONFLICT (account_code) DO UPDATE SET
        account_name = NEW.name,
        is_active = true;
    
    -- ربط المورد بالحساب الجديد
    UPDATE suppliers 
    SET account_id = (
        SELECT id FROM chart_of_accounts WHERE account_code = new_account_code
    )
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.create_supplier_account() OWNER TO postgres;

--
-- Name: generate_account_code(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.generate_account_code(parent_account_id integer) RETURNS character varying
    LANGUAGE plpgsql
    AS $_$
      DECLARE
        parent_code VARCHAR(20);
        used_numbers INTEGER[];
        next_number INTEGER := 1;
        used_num INTEGER;
      BEGIN
        -- الحصول على رمز الحساب الأب
        SELECT account_code INTO parent_code
        FROM chart_of_accounts 
        WHERE id = parent_account_id;
        
        IF parent_code IS NULL THEN
          RAISE EXCEPTION 'الحساب الأب غير موجود';
        END IF;
        
        -- الحصول على الأرقام المستخدمة للحسابات الفرعية المباشرة
        SELECT ARRAY(
          SELECT CAST(SUBSTRING(account_code FROM LENGTH(parent_code) + 1) AS INTEGER)
          FROM chart_of_accounts 
          WHERE parent_id = parent_account_id
            AND account_code ~ ('^' || parent_code || '[0-9]+$')
            AND LENGTH(account_code) = LENGTH(parent_code) + 1
          ORDER BY CAST(SUBSTRING(account_code FROM LENGTH(parent_code) + 1) AS INTEGER)
        ) INTO used_numbers;
        
        -- العثور على أول رقم متاح
        FOREACH used_num IN ARRAY used_numbers
        LOOP
          IF next_number = used_num THEN
            next_number := next_number + 1;
          ELSE
            EXIT;
          END IF;
        END LOOP;
        
        RETURN parent_code || next_number;
      END;
      $_$;


ALTER FUNCTION public.generate_account_code(parent_account_id integer) OWNER TO postgres;

--
-- Name: generate_voucher_number(character varying); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.generate_voucher_number(v_type character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
      DECLARE
        prefix VARCHAR(10);
        next_number INTEGER;
        current_year VARCHAR(4);
      BEGIN
        current_year := EXTRACT(YEAR FROM CURRENT_DATE)::VARCHAR;
        
        CASE v_type
          WHEN 'سند صرف' THEN prefix := 'PAY';
          WHEN 'سند قبض' THEN prefix := 'REC';
          WHEN 'قيد يومي' THEN prefix := 'JE';
          ELSE prefix := 'VOC';
        END CASE;
        
        SELECT COALESCE(MAX(
          CAST(SUBSTRING(voucher_number FROM LENGTH(prefix || current_year) + 1) AS INTEGER)
        ), 0) + 1
        INTO next_number
        FROM vouchers_master 
        WHERE voucher_type = v_type 
          AND voucher_number LIKE prefix || current_year || '%';
        
        RETURN prefix || current_year || LPAD(next_number::VARCHAR, 4, '0');
      END;
      $$;


ALTER FUNCTION public.generate_voucher_number(v_type character varying) OWNER TO postgres;

--
-- Name: get_account_balance(character varying); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_account_balance(p_account_code character varying) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
      BEGIN
        RETURN (SELECT COALESCE(current_balance, 0) FROM chart_of_accounts WHERE account_code = p_account_code);
      END;
      $$;


ALTER FUNCTION public.get_account_balance(p_account_code character varying) OWNER TO postgres;

--
-- Name: get_user_combined_permissions(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_user_combined_permissions(user_id_param integer) RETURNS text[]
    LANGUAGE plpgsql
    AS $$
      DECLARE
        combined_permissions TEXT[];
      BEGIN
        -- جلب الصلاحيات المباشرة للمستخدم
        SELECT ARRAY(
          SELECT DISTINCT up.permission_key
          FROM user_permissions up
          WHERE up.user_id = user_id_param AND up.is_active = true
        ) INTO combined_permissions;
        
        -- إضافة صلاحيات الأدوار
        SELECT ARRAY(
          SELECT DISTINCT unnest(ur.permissions)
          FROM user_role_assignments ura
          JOIN user_roles ur ON ura.role_name = ur.role_name
          WHERE ura.user_id = user_id_param 
            AND ura.is_active = true 
            AND ur.is_active = true
        ) INTO combined_permissions;
        
        -- إرجاع الصلاحيات المجمعة بدون تكرار
        RETURN ARRAY(SELECT DISTINCT unnest(combined_permissions));
      END;
      $$;


ALTER FUNCTION public.get_user_combined_permissions(user_id_param integer) OWNER TO postgres;

--
-- Name: recalculate_main_accounts(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.recalculate_main_accounts() RETURNS text
    LANGUAGE plpgsql
    AS $$
      DECLARE
        main_acc RECORD;
        sub_totals RECORD;
        updated_count INTEGER := 0;
      BEGIN
        FOR main_acc IN 
          SELECT id, account_code, account_name
          FROM chart_of_accounts 
          WHERE is_main_account = TRUE
        LOOP
          -- حساب مجموع الحسابات الفرعية
          WITH RECURSIVE sub_accounts AS (
            SELECT id, opening_balance, current_balance
            FROM chart_of_accounts 
            WHERE parent_id = main_acc.id
            
            UNION ALL
            
            SELECT c.id, c.opening_balance, c.current_balance
            FROM chart_of_accounts c
            INNER JOIN sub_accounts sa ON c.parent_id = sa.id
          )
          SELECT 
            COALESCE(SUM(opening_balance), 0) as total_opening,
            COALESCE(SUM(current_balance), 0) as total_current
          INTO sub_totals
          FROM sub_accounts;
          
          -- تحديث الحساب الرئيسي
          UPDATE chart_of_accounts 
          SET 
            opening_balance = sub_totals.total_opening,
            current_balance = sub_totals.total_current,
            updated_date = CURRENT_TIMESTAMP
          WHERE id = main_acc.id;
          
          updated_count := updated_count + 1;
        END LOOP;
        
        RETURN 'تم تحديث ' || updated_count || ' حساب رئيسي';
      END;
      $$;


ALTER FUNCTION public.recalculate_main_accounts() OWNER TO postgres;

--
-- Name: update_client_account_name(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_client_account_name() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- تحديث اسم الحساب في دليل الحسابات
    UPDATE chart_of_accounts 
    SET account_name = NEW.name,
        updated_date = NOW()
    WHERE id = NEW.account_id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_client_account_name() OWNER TO postgres;

--
-- Name: update_conversation_last_message(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_conversation_last_message() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
      BEGIN
        UPDATE conversations
        SET last_message_at = NEW.created_at,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.conversation_id;
        RETURN NEW;
      END;
      $$;


ALTER FUNCTION public.update_conversation_last_message() OWNER TO postgres;

--
-- Name: update_employee_account_name(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_employee_account_name() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- تحديث اسم الحساب في دليل الحسابات
    UPDATE chart_of_accounts 
    SET account_name = NEW.name,
        updated_date = NOW()
    WHERE id = NEW.account_id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_employee_account_name() OWNER TO postgres;

--
-- Name: update_parent_account_balance(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_parent_account_balance(p_account_id integer) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE chart_of_accounts 
    SET current_balance = calculate_parent_account_balance(p_account_id),
        updated_date = CURRENT_TIMESTAMP
    WHERE id = p_account_id;
END;
$$;


ALTER FUNCTION public.update_parent_account_balance(p_account_id integer) OWNER TO postgres;

--
-- Name: update_parent_balance_on_client_change(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_parent_balance_on_client_change() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- تحديث رصيد الحساب الأب للعميل
    IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'clients' AND linked_record_id = NEW.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'clients' AND linked_record_id = NEW.id LIMIT 1);
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'clients' AND linked_record_id = OLD.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'clients' AND linked_record_id = OLD.id LIMIT 1);
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION public.update_parent_balance_on_client_change() OWNER TO postgres;

--
-- Name: update_parent_balance_on_employee_change(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_parent_balance_on_employee_change() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'employees' AND linked_record_id = NEW.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'employees' AND linked_record_id = NEW.id LIMIT 1);
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'employees' AND linked_record_id = OLD.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'employees' AND linked_record_id = OLD.id LIMIT 1);
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION public.update_parent_balance_on_employee_change() OWNER TO postgres;

--
-- Name: update_parent_balance_on_supplier_change(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_parent_balance_on_supplier_change() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'suppliers' AND linked_record_id = NEW.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'suppliers' AND linked_record_id = NEW.id LIMIT 1);
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'suppliers' AND linked_record_id = OLD.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'suppliers' AND linked_record_id = OLD.id LIMIT 1);
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION public.update_parent_balance_on_supplier_change() OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: acc_trans; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.acc_trans (
    trans_id integer NOT NULL,
    chart_id integer NOT NULL,
    amount numeric(20,8) NOT NULL,
    transdate date NOT NULL,
    source text,
    cleared boolean DEFAULT false,
    fx_transaction boolean DEFAULT false,
    memo text,
    invoice_id integer,
    approved boolean DEFAULT false,
    entry_id integer NOT NULL,
    voucher_id integer
);


ALTER TABLE public.acc_trans OWNER TO postgres;

--
-- Name: acc_trans_entry_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.acc_trans_entry_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.acc_trans_entry_id_seq OWNER TO postgres;

--
-- Name: acc_trans_entry_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.acc_trans_entry_id_seq OWNED BY public.acc_trans.entry_id;


--
-- Name: accounting_transaction_details; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounting_transaction_details (
    id integer NOT NULL,
    transaction_id integer,
    account_id integer NOT NULL,
    account_name character varying(255) NOT NULL,
    account_code character varying(50),
    debit_amount numeric(15,2) DEFAULT 0,
    credit_amount numeric(15,2) DEFAULT 0,
    description text,
    line_order integer DEFAULT 1,
    CONSTRAINT check_debit_or_credit CHECK ((((debit_amount > (0)::numeric) AND (credit_amount = (0)::numeric)) OR ((credit_amount > (0)::numeric) AND (debit_amount = (0)::numeric))))
);


ALTER TABLE public.accounting_transaction_details OWNER TO postgres;

--
-- Name: accounting_transaction_details_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounting_transaction_details_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accounting_transaction_details_id_seq OWNER TO postgres;

--
-- Name: accounting_transaction_details_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounting_transaction_details_id_seq OWNED BY public.accounting_transaction_details.id;


--
-- Name: accounting_transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounting_transactions (
    id integer NOT NULL,
    transaction_number character varying(50) NOT NULL,
    transaction_type character varying(20) NOT NULL,
    transaction_date date NOT NULL,
    description text NOT NULL,
    party_name character varying(255),
    party_type character varying(50),
    reference_number character varying(100),
    total_amount numeric(15,2) DEFAULT 0 NOT NULL,
    status character varying(20) DEFAULT 'draft'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT accounting_transactions_status_check CHECK (((status)::text = ANY ((ARRAY['draft'::character varying, 'approved'::character varying, 'cancelled'::character varying])::text[]))),
    CONSTRAINT accounting_transactions_transaction_type_check CHECK (((transaction_type)::text = ANY ((ARRAY['receipt'::character varying, 'payment'::character varying, 'journal'::character varying])::text[])))
);


ALTER TABLE public.accounting_transactions OWNER TO postgres;

--
-- Name: accounting_transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounting_transactions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accounting_transactions_id_seq OWNER TO postgres;

--
-- Name: accounting_transactions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounting_transactions_id_seq OWNED BY public.accounting_transactions.id;


--
-- Name: accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounts (
    id integer NOT NULL,
    account_code character varying(20) NOT NULL,
    account_name character varying(255) NOT NULL,
    account_type character varying(50) NOT NULL,
    parent_id integer,
    balance_type character varying(10) NOT NULL,
    opening_balance numeric(15,2) DEFAULT 0,
    current_balance numeric(15,2) DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    account_category character varying(20) DEFAULT 'فرعي'::character varying,
    account_level integer DEFAULT 1,
    allow_posting boolean DEFAULT true,
    description text,
    CONSTRAINT accounts_account_category_check CHECK (((account_category)::text = ANY (ARRAY[('رئيسي'::character varying)::text, ('فرعي'::character varying)::text]))),
    CONSTRAINT accounts_balance_type_check CHECK (((balance_type)::text = ANY (ARRAY[('مدين'::character varying)::text, ('دائن'::character varying)::text])))
);


ALTER TABLE public.accounts OWNER TO postgres;

--
-- Name: accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accounts_id_seq OWNER TO postgres;

--
-- Name: accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounts_id_seq OWNED BY public.accounts.id;


--
-- Name: ai_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ai_settings (
    id integer DEFAULT 1 NOT NULL,
    enabled boolean DEFAULT true,
    model character varying(100) DEFAULT 'codegeex2'::character varying,
    delay_seconds integer DEFAULT 2,
    working_hours_only boolean DEFAULT false,
    working_hours_start time without time zone DEFAULT '00:00:00'::time without time zone,
    working_hours_end time without time zone DEFAULT '23:59:00'::time without time zone,
    working_days text[] DEFAULT ARRAY['الأحد'::text, 'الاثنين'::text, 'الثلاثاء'::text, 'الأربعاء'::text, 'الخميس'::text, 'الجمعة'::text, 'السبت'::text],
    max_responses_per_conversation integer DEFAULT 10,
    keywords_trigger text[] DEFAULT ARRAY['مساعدة'::text, 'استفسار'::text, 'سؤال'::text, 'معلومات'::text, 'خدمة'::text, 'مرحبا'::text, 'السلام'::text, 'أهلا'::text],
    excluded_keywords text[] DEFAULT ARRAY['عاجل'::text, 'طارئ'::text, 'مهم جداً'::text],
    auto_responses jsonb DEFAULT '{"help": "يمكنني مساعدتك في:\\n• الاستفسارات القانونية العامة\\n• معلومات عن خدمات المكتب\\n• توجيهك للمحامي المناسب", "greeting": "مرحباً! أنا المساعد الذكي للمكتب.", "signature": "🤖 المساعد الذكي للمكتب", "disclaimer": "للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا."}'::jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT ai_settings_single_row CHECK ((id = 1))
);


ALTER TABLE public.ai_settings OWNER TO postgres;

--
-- Name: TABLE ai_settings; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.ai_settings IS 'إعدادات نظام الذكاء الاصطناعي والرد التلقائي';


--
-- Name: COLUMN ai_settings.enabled; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.enabled IS 'تفعيل أو إيقاف النظام';


--
-- Name: COLUMN ai_settings.model; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.model IS 'النموذج المستخدم للذكاء الاصطناعي';


--
-- Name: COLUMN ai_settings.delay_seconds; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.delay_seconds IS 'تأخير الرد بالثواني';


--
-- Name: COLUMN ai_settings.working_hours_only; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.working_hours_only IS 'العمل في ساعات محددة فقط';


--
-- Name: COLUMN ai_settings.working_days; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.working_days IS 'أيام العمل';


--
-- Name: COLUMN ai_settings.keywords_trigger; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.keywords_trigger IS 'الكلمات المحفزة للرد';


--
-- Name: COLUMN ai_settings.excluded_keywords; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.excluded_keywords IS 'الكلمات المستبعدة';


--
-- Name: COLUMN ai_settings.auto_responses; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.auto_responses IS 'الردود التلقائية المخصصة';


--
-- Name: announcements; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.announcements (
    id integer NOT NULL,
    announcement_1 text,
    announcement_2 text,
    announcement_3 text,
    announcement_4 text,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.announcements OWNER TO postgres;

--
-- Name: announcements_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.announcements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.announcements_id_seq OWNER TO postgres;

--
-- Name: announcements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.announcements_id_seq OWNED BY public.announcements.id;


--
-- Name: ar; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ar (
    id integer NOT NULL,
    invnumber text,
    transdate date NOT NULL,
    entity_id integer,
    taxincluded boolean DEFAULT false,
    amount numeric(20,8) NOT NULL,
    netamount numeric(20,8) NOT NULL,
    paid numeric(20,8) DEFAULT 0,
    datepaid date,
    duedate date,
    invoice boolean DEFAULT true,
    shippingpoint text,
    terms text,
    notes text,
    curr character varying(3) DEFAULT 'SAR'::character varying,
    ordnumber text,
    employee_id integer,
    till character varying(20),
    quonumber text,
    intnotes text,
    department_id integer,
    shipvia text,
    language_code character varying(6),
    ponumber text,
    on_hold boolean DEFAULT false,
    reverse boolean DEFAULT false,
    approved boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    workflow_id integer,
    crdate date DEFAULT CURRENT_DATE
);


ALTER TABLE public.ar OWNER TO postgres;

--
-- Name: ar_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ar_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ar_id_seq OWNER TO postgres;

--
-- Name: ar_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ar_id_seq OWNED BY public.ar.id;


--
-- Name: branches; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.branches (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    governorate_id integer,
    address text,
    phone character varying(50),
    manager_name character varying(255),
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.branches OWNER TO postgres;

--
-- Name: branches_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.branches_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.branches_id_seq OWNER TO postgres;

--
-- Name: branches_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.branches_id_seq OWNED BY public.branches.id;


--
-- Name: budget; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.budget (
    id integer NOT NULL,
    budget_year integer NOT NULL,
    account_id integer NOT NULL,
    budgeted_amount numeric(15,2) NOT NULL,
    actual_amount numeric(15,2) DEFAULT 0,
    variance_amount numeric(15,2) DEFAULT 0,
    variance_percentage numeric(5,2) DEFAULT 0,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.budget OWNER TO postgres;

--
-- Name: budget_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.budget_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.budget_id_seq OWNER TO postgres;

--
-- Name: budget_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.budget_id_seq OWNED BY public.budget.id;


--
-- Name: case_distribution; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_distribution (
    id integer NOT NULL,
    issue_id integer,
    lineage_id integer,
    admin_amount numeric(15,2) DEFAULT 0,
    remaining_amount numeric(15,2) DEFAULT 0,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.case_distribution OWNER TO postgres;

--
-- Name: case_distribution_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.case_distribution_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.case_distribution_id_seq OWNER TO postgres;

--
-- Name: case_distribution_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.case_distribution_id_seq OWNED BY public.case_distribution.id;


--
-- Name: chart_of_accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.chart_of_accounts (
    id integer NOT NULL,
    account_code character varying(20) NOT NULL,
    account_name character varying(255) NOT NULL,
    account_name_en character varying(255),
    level_1_code character varying(2),
    level_2_code character varying(4),
    level_3_code character varying(6),
    level_4_code character varying(8),
    account_level integer NOT NULL,
    parent_id integer,
    account_type character varying(50) NOT NULL,
    account_nature character varying(20) DEFAULT 'مدين'::character varying,
    is_active boolean DEFAULT true,
    allow_transactions boolean DEFAULT false,
    linked_table character varying(100),
    auto_create_sub_accounts boolean DEFAULT false,
    opening_balance numeric(15,2) DEFAULT 0,
    current_balance numeric(15,2) DEFAULT 0,
    description text,
    notes text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_linked_record boolean DEFAULT false,
    original_table character varying(50),
    linked_record_id integer,
    linked_entity_type character varying(50),
    linked_entity_id integer,
    CONSTRAINT chart_of_accounts_account_level_check CHECK (((account_level >= 1) AND (account_level <= 5)))
);


ALTER TABLE public.chart_of_accounts OWNER TO postgres;

--
-- Name: chart_of_accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.chart_of_accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.chart_of_accounts_id_seq OWNER TO postgres;

--
-- Name: chart_of_accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.chart_of_accounts_id_seq OWNED BY public.chart_of_accounts.id;


--
-- Name: client_notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.client_notifications (
    id integer NOT NULL,
    client_id integer NOT NULL,
    title character varying(255) NOT NULL,
    message text NOT NULL,
    type character varying(50) DEFAULT 'info'::character varying,
    case_id integer,
    document_id integer,
    is_read boolean DEFAULT false,
    read_at timestamp without time zone,
    sent_via character varying(50) DEFAULT 'portal'::character varying,
    sent_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.client_notifications OWNER TO postgres;

--
-- Name: client_notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.client_notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.client_notifications_id_seq OWNER TO postgres;

--
-- Name: client_notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.client_notifications_id_seq OWNED BY public.client_notifications.id;


--
-- Name: client_portal_accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.client_portal_accounts (
    id integer NOT NULL,
    client_id integer NOT NULL,
    username character varying(100) NOT NULL,
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    is_active boolean DEFAULT true,
    is_verified boolean DEFAULT false,
    verification_token character varying(255),
    reset_token character varying(255),
    reset_token_expires timestamp without time zone,
    language character varying(10) DEFAULT 'ar'::character varying,
    timezone character varying(50) DEFAULT 'Asia/Riyadh'::character varying,
    notification_preferences jsonb DEFAULT '{}'::jsonb,
    last_login timestamp without time zone,
    login_attempts integer DEFAULT 0,
    locked_until timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.client_portal_accounts OWNER TO postgres;

--
-- Name: client_portal_accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.client_portal_accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.client_portal_accounts_id_seq OWNER TO postgres;

--
-- Name: client_portal_accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.client_portal_accounts_id_seq OWNED BY public.client_portal_accounts.id;


--
-- Name: client_requests; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.client_requests (
    id integer NOT NULL,
    client_id integer NOT NULL,
    case_id integer,
    request_type character varying(100) NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    priority character varying(20) DEFAULT 'medium'::character varying,
    status character varying(50) DEFAULT 'pending'::character varying,
    assigned_to integer,
    response text,
    due_date date,
    completed_date date,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.client_requests OWNER TO postgres;

--
-- Name: client_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.client_requests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.client_requests_id_seq OWNER TO postgres;

--
-- Name: client_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.client_requests_id_seq OWNED BY public.client_requests.id;


--
-- Name: client_sessions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.client_sessions (
    id integer NOT NULL,
    client_id integer NOT NULL,
    session_token character varying(255) NOT NULL,
    ip_address inet,
    user_agent text,
    expires_at timestamp without time zone NOT NULL,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.client_sessions OWNER TO postgres;

--
-- Name: client_sessions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.client_sessions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.client_sessions_id_seq OWNER TO postgres;

--
-- Name: client_sessions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.client_sessions_id_seq OWNED BY public.client_sessions.id;


--
-- Name: clients; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.clients (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    phone character varying(20),
    email character varying(255),
    address text,
    id_number character varying(20),
    status character varying(20) DEFAULT 'active'::character varying,
    cases_count integer DEFAULT 0,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    client_type character varying(100),
    username character varying(50),
    password_hash character varying(255),
    last_login timestamp without time zone,
    is_online boolean DEFAULT false,
    login_attempts integer DEFAULT 0,
    locked_until timestamp without time zone,
    account_id integer,
    current_balance numeric(15,2) DEFAULT 0
);


ALTER TABLE public.clients OWNER TO postgres;

--
-- Name: clients_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.clients_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.clients_id_seq OWNER TO postgres;

--
-- Name: clients_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.clients_id_seq OWNED BY public.clients.id;


--
-- Name: companies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.companies (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    address text,
    phone character varying(50),
    email character varying(255),
    website character varying(255),
    established_date date,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    legal_name character varying(255),
    registration_number character varying(100),
    city character varying(100),
    country character varying(100),
    logo_right_text text,
    logo_left_text text,
    logo_url text,
    legal_form character varying(100),
    capital numeric(15,2) DEFAULT 0,
    description text,
    is_active boolean DEFAULT true,
    tax_number character varying(100),
    logo_image_url text,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    latitude numeric(10,8),
    longitude numeric(11,8),
    map_zoom integer DEFAULT 15,
    working_hours text
);


ALTER TABLE public.companies OWNER TO postgres;

--
-- Name: COLUMN companies.latitude; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.companies.latitude IS 'خط العرض للموقع الجغرافي للشركة';


--
-- Name: COLUMN companies.longitude; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.companies.longitude IS 'خط الطول للموقع الجغرافي للشركة';


--
-- Name: COLUMN companies.working_hours; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.companies.working_hours IS 'ساعات العمل للشركة';


--
-- Name: companies_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.companies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.companies_id_seq OWNER TO postgres;

--
-- Name: companies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.companies_id_seq OWNED BY public.companies.id;


--
-- Name: company; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.company (
    id integer NOT NULL,
    entity_id integer,
    legal_name text NOT NULL,
    tax_id character varying(20),
    sales_tax_id character varying(20),
    license_number character varying(50),
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    control_code character varying(20),
    country_id integer,
    sic_code character varying(20)
);


ALTER TABLE public.company OWNER TO postgres;

--
-- Name: company_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.company_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_id_seq OWNER TO postgres;

--
-- Name: company_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.company_id_seq OWNED BY public.company.id;


--
-- Name: conversations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.conversations (
    id integer NOT NULL,
    client_id integer,
    user_id integer,
    title character varying(255),
    status character varying(20) DEFAULT 'active'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    last_message_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.conversations OWNER TO postgres;

--
-- Name: conversations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.conversations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.conversations_id_seq OWNER TO postgres;

--
-- Name: conversations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.conversations_id_seq OWNED BY public.conversations.id;


--
-- Name: cost_centers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cost_centers (
    id integer NOT NULL,
    center_code character varying(20) NOT NULL,
    center_name character varying(255) NOT NULL,
    parent_id integer,
    center_level integer DEFAULT 1,
    is_active boolean DEFAULT true,
    description text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.cost_centers OWNER TO postgres;

--
-- Name: cost_centers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cost_centers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cost_centers_id_seq OWNER TO postgres;

--
-- Name: cost_centers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cost_centers_id_seq OWNED BY public.cost_centers.id;


--
-- Name: courts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.courts (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    type character varying(100),
    governorate_id integer,
    address text,
    phone character varying(50),
    employee_id integer,
    issue_id integer,
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.courts OWNER TO postgres;

--
-- Name: courts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.courts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.courts_id_seq OWNER TO postgres;

--
-- Name: courts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.courts_id_seq OWNED BY public.courts.id;


--
-- Name: currencies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.currencies (
    id integer NOT NULL,
    currency_code character varying(10) NOT NULL,
    currency_name character varying(100) NOT NULL,
    symbol character varying(10),
    is_active boolean DEFAULT true,
    exchange_rate numeric(10,4) DEFAULT 1.0000,
    is_base_currency boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.currencies OWNER TO postgres;

--
-- Name: currencies_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.currencies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.currencies_id_seq OWNER TO postgres;

--
-- Name: currencies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.currencies_id_seq OWNED BY public.currencies.id;


--
-- Name: document_shares; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.document_shares (
    id integer NOT NULL,
    document_id integer,
    shared_with_user integer,
    shared_with_client integer,
    permission_level character varying(50) DEFAULT 'read'::character varying,
    shared_by integer,
    expires_at timestamp without time zone,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.document_shares OWNER TO postgres;

--
-- Name: document_shares_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.document_shares_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.document_shares_id_seq OWNER TO postgres;

--
-- Name: document_shares_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.document_shares_id_seq OWNED BY public.document_shares.id;


--
-- Name: document_versions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.document_versions (
    id integer NOT NULL,
    document_id integer,
    version_number integer NOT NULL,
    file_name character varying(255) NOT NULL,
    file_path character varying(500) NOT NULL,
    file_size bigint,
    changes_description text,
    uploaded_by integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.document_versions OWNER TO postgres;

--
-- Name: document_versions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.document_versions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.document_versions_id_seq OWNER TO postgres;

--
-- Name: document_versions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.document_versions_id_seq OWNED BY public.document_versions.id;


--
-- Name: documents; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.documents (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    file_name character varying(255) NOT NULL,
    file_path character varying(500) NOT NULL,
    file_size bigint,
    file_type character varying(100),
    mime_type character varying(100),
    case_id integer,
    client_id integer,
    employee_id integer,
    category character varying(100),
    subcategory character varying(100),
    tags text[],
    content_text text,
    keywords text[],
    access_level character varying(50) DEFAULT 'private'::character varying,
    is_confidential boolean DEFAULT false,
    uploaded_by integer,
    version_number integer DEFAULT 1,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.documents OWNER TO postgres;

--
-- Name: documents_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.documents_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.documents_id_seq OWNER TO postgres;

--
-- Name: documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.documents_id_seq OWNED BY public.documents.id;


--
-- Name: employees; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.employees (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    "position" character varying(255),
    department character varying(255),
    phone character varying(20),
    email character varying(255),
    address text,
    id_number character varying(20),
    salary numeric(10,2),
    hire_date date,
    status character varying(20) DEFAULT 'active'::character varying,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    department_id integer,
    branch_id integer DEFAULT 1,
    governorate_id integer DEFAULT 1,
    employee_number character varying(50),
    account_id integer,
    current_balance numeric(15,2) DEFAULT 0
);


ALTER TABLE public.employees OWNER TO postgres;

--
-- Name: employees_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.employees_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.employees_id_seq OWNER TO postgres;

--
-- Name: employees_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.employees_id_seq OWNED BY public.employees.id;


--
-- Name: entity; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.entity (
    id integer NOT NULL,
    name text NOT NULL,
    entity_class integer NOT NULL,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    control_code character varying(20),
    country_id integer
);


ALTER TABLE public.entity OWNER TO postgres;

--
-- Name: entity_class; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.entity_class (
    id integer NOT NULL,
    class character varying(20) NOT NULL,
    in_use boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.entity_class OWNER TO postgres;

--
-- Name: entity_class_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.entity_class_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.entity_class_id_seq OWNER TO postgres;

--
-- Name: entity_class_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.entity_class_id_seq OWNED BY public.entity_class.id;


--
-- Name: entity_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.entity_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.entity_id_seq OWNER TO postgres;

--
-- Name: entity_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.entity_id_seq OWNED BY public.entity.id;


--
-- Name: follows; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.follows (
    id integer NOT NULL,
    case_id integer,
    case_number character varying(50),
    case_title character varying(255),
    client_name character varying(255),
    service_type character varying(50),
    description text,
    date_field date,
    status character varying(50) DEFAULT 'pending'::character varying,
    priority character varying(20) DEFAULT 'medium'::character varying,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    service_id integer,
    user_id integer,
    next_hearing_date date,
    earned_amount numeric(15,2) DEFAULT 0,
    is_approved boolean DEFAULT false,
    approved_by integer,
    approved_date date,
    next_hearing_id integer,
    report text,
    next_hearing_time time without time zone,
    court_name character varying(255),
    hearing_type character varying(100)
);


ALTER TABLE public.follows OWNER TO postgres;

--
-- Name: follows_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.follows_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.follows_id_seq OWNER TO postgres;

--
-- Name: follows_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.follows_id_seq OWNED BY public.follows.id;


--
-- Name: footer_links; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.footer_links (
    id integer NOT NULL,
    category character varying(100) NOT NULL,
    name character varying(200) NOT NULL,
    href character varying(500) NOT NULL,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.footer_links OWNER TO postgres;

--
-- Name: footer_links_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.footer_links_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.footer_links_id_seq OWNER TO postgres;

--
-- Name: footer_links_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.footer_links_id_seq OWNED BY public.footer_links.id;


--
-- Name: gl; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.gl (
    id integer NOT NULL,
    reference text,
    description text,
    transdate date NOT NULL,
    person_id integer,
    notes text,
    approved boolean DEFAULT false,
    approved_by integer,
    approved_at timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    workflow_id integer
);


ALTER TABLE public.gl OWNER TO postgres;

--
-- Name: gl_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.gl_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.gl_id_seq OWNER TO postgres;

--
-- Name: gl_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.gl_id_seq OWNED BY public.gl.id;


--
-- Name: governorates; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.governorates (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    code character varying(10) NOT NULL,
    region character varying(100),
    population integer,
    is_capital boolean DEFAULT false,
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.governorates OWNER TO postgres;

--
-- Name: governorates_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.governorates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.governorates_id_seq OWNER TO postgres;

--
-- Name: governorates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.governorates_id_seq OWNED BY public.governorates.id;


--
-- Name: hearings; Type: TABLE; Schema: public; Owner: yemen
--

CREATE TABLE public.hearings (
    id integer NOT NULL,
    issue_id integer,
    hearing_date date NOT NULL,
    hearing_time time without time zone,
    court_name character varying(255),
    hearing_type character varying(100),
    notes text,
    status character varying(50) DEFAULT 'scheduled'::character varying,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.hearings OWNER TO yemen;

--
-- Name: hearings_id_seq; Type: SEQUENCE; Schema: public; Owner: yemen
--

CREATE SEQUENCE public.hearings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.hearings_id_seq OWNER TO yemen;

--
-- Name: hearings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: yemen
--

ALTER SEQUENCE public.hearings_id_seq OWNED BY public.hearings.id;


--
-- Name: invoice; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.invoice (
    id integer NOT NULL,
    trans_id integer,
    parts_id integer,
    description text,
    qty numeric(20,8),
    allocated numeric(20,8) DEFAULT 0,
    sellprice numeric(20,8),
    fxsellprice numeric(20,8),
    discount numeric(4,4) DEFAULT 0,
    assemblyitem boolean DEFAULT false,
    unit character varying(20),
    deliverydate date,
    serialnumber text,
    notes text
);


ALTER TABLE public.invoice OWNER TO postgres;

--
-- Name: invoice_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.invoice_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.invoice_id_seq OWNER TO postgres;

--
-- Name: invoice_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.invoice_id_seq OWNED BY public.invoice.id;


--
-- Name: invoice_items; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.invoice_items (
    id integer NOT NULL,
    invoice_id integer,
    description text NOT NULL,
    quantity numeric(10,2) DEFAULT 1,
    unit_price numeric(10,2) NOT NULL,
    total_price numeric(12,2) NOT NULL,
    time_entry_id integer,
    case_id integer,
    item_type character varying(50) DEFAULT 'service'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.invoice_items OWNER TO postgres;

--
-- Name: invoice_items_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.invoice_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.invoice_items_id_seq OWNER TO postgres;

--
-- Name: invoice_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.invoice_items_id_seq OWNED BY public.invoice_items.id;


--
-- Name: invoices; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.invoices (
    id integer NOT NULL,
    invoice_number character varying(50) NOT NULL,
    client_id integer NOT NULL,
    client_name character varying(255),
    client_address text,
    invoice_date date DEFAULT CURRENT_DATE,
    due_date date,
    subtotal numeric(12,2) DEFAULT 0,
    tax_rate numeric(5,2) DEFAULT 0,
    tax_amount numeric(12,2) DEFAULT 0,
    discount_amount numeric(12,2) DEFAULT 0,
    total_amount numeric(12,2) DEFAULT 0,
    status character varying(50) DEFAULT 'draft'::character varying,
    payment_status character varying(50) DEFAULT 'unpaid'::character varying,
    paid_amount numeric(12,2) DEFAULT 0,
    payment_date date,
    notes text,
    terms_conditions text,
    created_by integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.invoices OWNER TO postgres;

--
-- Name: invoices_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.invoices_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.invoices_id_seq OWNER TO postgres;

--
-- Name: invoices_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.invoices_id_seq OWNED BY public.invoices.id;


--
-- Name: issue_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.issue_types (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    color character varying(50),
    cases_count integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.issue_types OWNER TO postgres;

--
-- Name: issue_types_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.issue_types_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.issue_types_id_seq OWNER TO postgres;

--
-- Name: issue_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.issue_types_id_seq OWNED BY public.issue_types.id;


--
-- Name: issues; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.issues (
    id integer NOT NULL,
    case_number character varying(50) NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    client_id integer,
    issue_type_id integer,
    status character varying(50) DEFAULT 'pending'::character varying,
    amount numeric(12,2),
    next_hearing date,
    notes text,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    court_id integer,
    case_amount numeric(15,2),
    start_date date,
    end_date date,
    contract_method character varying(20) DEFAULT 'بالجلسة'::character varying,
    contract_date date DEFAULT CURRENT_DATE,
    client_name character varying(255) DEFAULT ''::character varying,
    court_name character varying(255) DEFAULT NULL::character varying,
    issue_type character varying(100) DEFAULT NULL::character varying,
    next_hearing_date date,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_contract_method CHECK (((contract_method)::text = ANY (ARRAY[('بالجلسة'::character varying)::text, ('بالعقد'::character varying)::text])))
);


ALTER TABLE public.issues OWNER TO postgres;

--
-- Name: issues_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.issues_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.issues_id_seq OWNER TO postgres;

--
-- Name: issues_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.issues_id_seq OWNED BY public.issues.id;


--
-- Name: journal_entries; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.journal_entries (
    id integer NOT NULL,
    entry_number character varying(50) NOT NULL,
    entry_date date NOT NULL,
    description text NOT NULL,
    total_debit numeric(15,2) DEFAULT 0 NOT NULL,
    total_credit numeric(15,2) DEFAULT 0 NOT NULL,
    status character varying(20) DEFAULT 'draft'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    entry_type character varying(20) DEFAULT 'journal'::character varying,
    party_name character varying(255),
    party_type character varying(50),
    reference_number character varying(100),
    CONSTRAINT journal_entries_entry_type_check CHECK (((entry_type)::text = ANY ((ARRAY['receipt'::character varying, 'payment'::character varying, 'journal'::character varying])::text[])))
);


ALTER TABLE public.journal_entries OWNER TO postgres;

--
-- Name: journal_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.journal_entries_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.journal_entries_id_seq OWNER TO postgres;

--
-- Name: journal_entries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.journal_entries_id_seq OWNED BY public.journal_entries.id;


--
-- Name: journal_entry_details; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.journal_entry_details (
    id integer NOT NULL,
    journal_entry_id integer,
    account_id integer,
    account_name character varying(255),
    debit_amount numeric(15,2) DEFAULT 0,
    credit_amount numeric(15,2) DEFAULT 0,
    description text,
    line_order integer DEFAULT 1,
    account_code character varying(50)
);


ALTER TABLE public.journal_entry_details OWNER TO postgres;

--
-- Name: journal_entry_details_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.journal_entry_details_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.journal_entry_details_id_seq OWNER TO postgres;

--
-- Name: journal_entry_details_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.journal_entry_details_id_seq OWNED BY public.journal_entry_details.id;


--
-- Name: lawyer_earnings; Type: TABLE; Schema: public; Owner: yemen
--

CREATE TABLE public.lawyer_earnings (
    id integer NOT NULL,
    lawyer_id integer NOT NULL,
    case_id integer,
    service_id integer,
    follow_id integer,
    allocated_amount numeric(15,2) DEFAULT 0,
    earned_amount numeric(15,2) DEFAULT 0,
    earning_date date DEFAULT CURRENT_DATE,
    notes text,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.lawyer_earnings OWNER TO yemen;

--
-- Name: lawyer_earnings_id_seq; Type: SEQUENCE; Schema: public; Owner: yemen
--

CREATE SEQUENCE public.lawyer_earnings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.lawyer_earnings_id_seq OWNER TO yemen;

--
-- Name: lawyer_earnings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: yemen
--

ALTER SEQUENCE public.lawyer_earnings_id_seq OWNED BY public.lawyer_earnings.id;


--
-- Name: lineages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.lineages (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    created_date date DEFAULT CURRENT_DATE,
    admin_percentage numeric(5,2) DEFAULT 0,
    commission_percentage numeric(5,2) DEFAULT 0
);


ALTER TABLE public.lineages OWNER TO postgres;

--
-- Name: lineages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.lineages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.lineages_id_seq OWNER TO postgres;

--
-- Name: lineages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.lineages_id_seq OWNED BY public.lineages.id;


--
-- Name: main_accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.main_accounts (
    id integer NOT NULL,
    account_name character varying(255) NOT NULL,
    account_code character varying(20),
    chart_account_id integer,
    is_required boolean DEFAULT true,
    description text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    table_name character varying(50),
    record_id integer,
    balance numeric(15,2) DEFAULT 0,
    is_active boolean DEFAULT true
);


ALTER TABLE public.main_accounts OWNER TO postgres;

--
-- Name: main_accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.main_accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.main_accounts_id_seq OWNER TO postgres;

--
-- Name: main_accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.main_accounts_id_seq OWNED BY public.main_accounts.id;


--
-- Name: message_read_status; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.message_read_status (
    id integer NOT NULL,
    message_id integer,
    reader_type character varying(10) NOT NULL,
    reader_id integer NOT NULL,
    read_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT message_read_status_reader_type_check CHECK (((reader_type)::text = ANY ((ARRAY['user'::character varying, 'client'::character varying, 'ai'::character varying])::text[])))
);


ALTER TABLE public.message_read_status OWNER TO postgres;

--
-- Name: message_read_status_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.message_read_status_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.message_read_status_id_seq OWNER TO postgres;

--
-- Name: message_read_status_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.message_read_status_id_seq OWNED BY public.message_read_status.id;


--
-- Name: messages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.messages (
    id integer NOT NULL,
    conversation_id integer,
    sender_type character varying(10) NOT NULL,
    sender_id integer NOT NULL,
    message_text text,
    message_type character varying(20) DEFAULT 'text'::character varying,
    file_url character varying(500),
    file_name character varying(255),
    file_size integer,
    reply_to_message_id integer,
    is_read boolean DEFAULT false,
    is_edited boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT messages_message_type_check CHECK (((message_type)::text = ANY (ARRAY[('text'::character varying)::text, ('image'::character varying)::text, ('file'::character varying)::text, ('reply'::character varying)::text]))),
    CONSTRAINT messages_sender_type_check CHECK (((sender_type)::text = ANY ((ARRAY['user'::character varying, 'client'::character varying, 'ai'::character varying])::text[])))
);


ALTER TABLE public.messages OWNER TO postgres;

--
-- Name: messages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.messages_id_seq OWNER TO postgres;

--
-- Name: messages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.messages_id_seq OWNED BY public.messages.id;


--
-- Name: money_transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.money_transactions (
    id integer NOT NULL,
    account_id integer NOT NULL,
    amount_type integer NOT NULL,
    amount numeric(15,2) NOT NULL,
    currency character varying(10) DEFAULT 'rial'::character varying,
    transaction_date date NOT NULL,
    user_id integer NOT NULL,
    related_id integer,
    issue_id integer,
    description text,
    reference_number character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT money_transactions_amount_type_check CHECK ((amount_type = ANY (ARRAY[1, '-1'::integer]))),
    CONSTRAINT money_transactions_currency_check CHECK (((currency)::text = ANY (ARRAY[('rial'::character varying)::text, ('dollar'::character varying)::text, ('saudi'::character varying)::text])))
);


ALTER TABLE public.money_transactions OWNER TO postgres;

--
-- Name: money_transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.money_transactions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.money_transactions_id_seq OWNER TO postgres;

--
-- Name: money_transactions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.money_transactions_id_seq OWNED BY public.money_transactions.id;


--
-- Name: movements; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.movements (
    id integer NOT NULL,
    case_id integer,
    case_number character varying(50),
    movement_type character varying(50),
    amount numeric(12,2),
    description text,
    movement_date date DEFAULT CURRENT_DATE,
    status character varying(50) DEFAULT 'completed'::character varying,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.movements OWNER TO postgres;

--
-- Name: movements_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.movements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.movements_id_seq OWNER TO postgres;

--
-- Name: movements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.movements_id_seq OWNED BY public.movements.id;


--
-- Name: navigation_pages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.navigation_pages (
    id integer NOT NULL,
    page_title character varying(255) NOT NULL,
    page_url character varying(500) NOT NULL,
    page_description text,
    category character varying(100),
    keywords text,
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.navigation_pages OWNER TO postgres;

--
-- Name: navigation_pages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.navigation_pages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.navigation_pages_id_seq OWNER TO postgres;

--
-- Name: navigation_pages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.navigation_pages_id_seq OWNED BY public.navigation_pages.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    recipient_type character varying(10) NOT NULL,
    recipient_id integer NOT NULL,
    sender_type character varying(10) NOT NULL,
    sender_id integer NOT NULL,
    notification_type character varying(20) DEFAULT 'message'::character varying,
    title character varying(255),
    content text,
    related_id integer,
    is_read boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT notifications_notification_type_check CHECK (((notification_type)::text = ANY (ARRAY[('message'::character varying)::text, ('mention'::character varying)::text, ('reply'::character varying)::text]))),
    CONSTRAINT notifications_recipient_type_check CHECK (((recipient_type)::text = ANY ((ARRAY['user'::character varying, 'client'::character varying, 'ai'::character varying])::text[]))),
    CONSTRAINT notifications_sender_type_check CHECK (((sender_type)::text = ANY ((ARRAY['user'::character varying, 'client'::character varying, 'ai'::character varying])::text[])))
);


ALTER TABLE public.notifications OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.notifications_id_seq OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- Name: opening_balances; Type: TABLE; Schema: public; Owner: yemen
--

CREATE TABLE public.opening_balances (
    id integer NOT NULL,
    account_id character varying(50) NOT NULL,
    debit_balance numeric(15,2) DEFAULT 0,
    credit_balance numeric(15,2) DEFAULT 0,
    balance_date date NOT NULL,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.opening_balances OWNER TO yemen;

--
-- Name: opening_balances_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.opening_balances_history (
    id integer NOT NULL,
    opening_balance_id integer,
    account_name character varying(255),
    account_code character varying(50),
    old_debit_amount numeric(15,2) DEFAULT 0,
    old_credit_amount numeric(15,2) DEFAULT 0,
    new_debit_amount numeric(15,2) DEFAULT 0,
    new_credit_amount numeric(15,2) DEFAULT 0,
    old_balance_type character varying(20),
    new_balance_type character varying(20),
    change_type character varying(50),
    changed_by character varying(255),
    change_reason text,
    change_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.opening_balances_history OWNER TO postgres;

--
-- Name: opening_balances_history_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.opening_balances_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.opening_balances_history_id_seq OWNER TO postgres;

--
-- Name: opening_balances_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.opening_balances_history_id_seq OWNED BY public.opening_balances_history.id;


--
-- Name: opening_balances_id_seq; Type: SEQUENCE; Schema: public; Owner: yemen
--

CREATE SEQUENCE public.opening_balances_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.opening_balances_id_seq OWNER TO yemen;

--
-- Name: opening_balances_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: yemen
--

ALTER SEQUENCE public.opening_balances_id_seq OWNED BY public.opening_balances.id;


--
-- Name: payment; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payment (
    id integer NOT NULL,
    reference text,
    payment_class integer NOT NULL,
    payment_date date NOT NULL,
    closed boolean DEFAULT false,
    entity_id integer,
    employee_id integer,
    currency character varying(3) DEFAULT 'SAR'::character varying,
    notes text,
    department_id integer,
    gl_id integer,
    approved boolean DEFAULT false,
    workflow_id integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.payment OWNER TO postgres;

--
-- Name: payment_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.payment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.payment_id_seq OWNER TO postgres;

--
-- Name: payment_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.payment_id_seq OWNED BY public.payment.id;


--
-- Name: payment_links; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payment_links (
    payment_id integer NOT NULL,
    entry_id integer NOT NULL,
    type integer NOT NULL
);


ALTER TABLE public.payment_links OWNER TO postgres;

--
-- Name: payment_methods; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payment_methods (
    id integer NOT NULL,
    method_name character varying(100) NOT NULL,
    method_code character varying(20) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.payment_methods OWNER TO postgres;

--
-- Name: payment_methods_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.payment_methods_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.payment_methods_id_seq OWNER TO postgres;

--
-- Name: payment_methods_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.payment_methods_id_seq OWNED BY public.payment_methods.id;


--
-- Name: payment_vouchers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payment_vouchers (
    id integer NOT NULL,
    entry_number character varying(50) NOT NULL,
    voucher_number character varying(50),
    entry_date date NOT NULL,
    voucher_date date,
    payee_name character varying(255) NOT NULL,
    payee_type character varying(50) DEFAULT 'external'::character varying,
    debit_account_id integer,
    credit_account_id integer,
    amount numeric(15,2) NOT NULL,
    currency_id integer DEFAULT 1,
    payment_method_id integer,
    cost_center_id integer,
    description text,
    reference_number character varying(100),
    case_id integer,
    service_id integer,
    status character varying(20) DEFAULT 'draft'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.payment_vouchers OWNER TO postgres;

--
-- Name: payment_vouchers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.payment_vouchers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.payment_vouchers_id_seq OWNER TO postgres;

--
-- Name: payment_vouchers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.payment_vouchers_id_seq OWNED BY public.payment_vouchers.id;


--
-- Name: permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.permissions (
    id integer NOT NULL,
    permission_key character varying(100) NOT NULL,
    permission_name character varying(200) NOT NULL,
    category character varying(100) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.permissions OWNER TO postgres;

--
-- Name: permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.permissions_id_seq OWNER TO postgres;

--
-- Name: permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.permissions_id_seq OWNED BY public.permissions.id;


--
-- Name: project; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project (
    id integer NOT NULL,
    projectnumber text,
    description text,
    startdate date,
    enddate date,
    parts_id integer,
    production numeric(20,8) DEFAULT 0,
    completed numeric(20,8) DEFAULT 0,
    customer_id integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.project OWNER TO postgres;

--
-- Name: project_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.project_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.project_id_seq OWNER TO postgres;

--
-- Name: project_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.project_id_seq OWNED BY public.project.id;


--
-- Name: public_announcements; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.public_announcements (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    content text NOT NULL,
    type character varying(50) NOT NULL,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT now(),
    updated_date timestamp without time zone DEFAULT now(),
    CONSTRAINT public_announcements_type_check CHECK (((type)::text = ANY ((ARRAY['public_1'::character varying, 'public_2'::character varying])::text[])))
);


ALTER TABLE public.public_announcements OWNER TO postgres;

--
-- Name: public_announcements_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.public_announcements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.public_announcements_id_seq OWNER TO postgres;

--
-- Name: public_announcements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.public_announcements_id_seq OWNED BY public.public_announcements.id;


--
-- Name: receipt_vouchers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.receipt_vouchers (
    id integer NOT NULL,
    entry_number character varying(50) NOT NULL,
    entry_date date NOT NULL,
    payer_name character varying(255) NOT NULL,
    payer_type character varying(50) DEFAULT 'external'::character varying,
    debit_account_id integer,
    credit_account_id integer,
    amount numeric(15,2) NOT NULL,
    description text,
    reference_number character varying(100),
    status character varying(20) DEFAULT 'draft'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.receipt_vouchers OWNER TO postgres;

--
-- Name: receipt_vouchers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.receipt_vouchers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.receipt_vouchers_id_seq OWNER TO postgres;

--
-- Name: receipt_vouchers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.receipt_vouchers_id_seq OWNED BY public.receipt_vouchers.id;


--
-- Name: security_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.security_logs (
    id integer NOT NULL,
    user_id integer,
    action character varying(255) NOT NULL,
    ip_address character varying(45),
    user_agent text,
    device_id character varying(255),
    success boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.security_logs OWNER TO postgres;

--
-- Name: security_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.security_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.security_logs_id_seq OWNER TO postgres;

--
-- Name: security_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.security_logs_id_seq OWNED BY public.security_logs.id;


--
-- Name: service_distributions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.service_distributions (
    id integer NOT NULL,
    case_distribution_id integer,
    service_id integer,
    percentage numeric(5,2) DEFAULT 0,
    amount numeric(15,2) DEFAULT 0,
    lawyer_id integer,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.service_distributions OWNER TO postgres;

--
-- Name: service_distributions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.service_distributions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.service_distributions_id_seq OWNER TO postgres;

--
-- Name: service_distributions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.service_distributions_id_seq OWNED BY public.service_distributions.id;


--
-- Name: services; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.services (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    lineage_id integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.services OWNER TO postgres;

--
-- Name: services_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.services_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.services_id_seq OWNER TO postgres;

--
-- Name: services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.services_id_seq OWNED BY public.services.id;


--
-- Name: serviceslow; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.serviceslow (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    slug character varying(255) NOT NULL,
    description text,
    content text,
    icon_name character varying(100) DEFAULT 'Scale'::character varying,
    icon_color character varying(50) DEFAULT '#2563eb'::character varying,
    image_url character varying(500),
    is_active boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    meta_title character varying(255),
    meta_description text,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.serviceslow OWNER TO postgres;

--
-- Name: TABLE serviceslow; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.serviceslow IS 'جدول خدمات الموقع الرئيسي - منفصل عن جدول services المستخدم في نظام إدارة القضايا';


--
-- Name: serviceslow_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.serviceslow_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.serviceslow_id_seq OWNER TO postgres;

--
-- Name: serviceslow_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.serviceslow_id_seq OWNED BY public.serviceslow.id;


--
-- Name: suppliers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.suppliers (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    company_name character varying(255),
    phone character varying(50),
    email character varying(255),
    address text,
    tax_number character varying(100),
    commercial_register character varying(100),
    contact_person character varying(255),
    payment_terms character varying(100),
    credit_limit numeric(15,2) DEFAULT 0,
    current_balance numeric(15,2) DEFAULT 0,
    status character varying(20) DEFAULT 'active'::character varying,
    account_id integer,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.suppliers OWNER TO postgres;

--
-- Name: suppliers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.suppliers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.suppliers_id_seq OWNER TO postgres;

--
-- Name: suppliers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.suppliers_id_seq OWNED BY public.suppliers.id;


--
-- Name: test_table; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.test_table (
    id integer NOT NULL,
    name character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.test_table OWNER TO postgres;

--
-- Name: test_table_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.test_table_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.test_table_id_seq OWNER TO postgres;

--
-- Name: test_table_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.test_table_id_seq OWNED BY public.test_table.id;


--
-- Name: time_entries; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.time_entries (
    id integer NOT NULL,
    case_id integer,
    client_id integer,
    employee_id integer NOT NULL,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone,
    duration_minutes integer,
    task_description text NOT NULL,
    task_category character varying(100),
    hourly_rate numeric(10,2),
    billable_amount numeric(12,2),
    is_billable boolean DEFAULT true,
    is_billed boolean DEFAULT false,
    status character varying(50) DEFAULT 'active'::character varying,
    notes text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.time_entries OWNER TO postgres;

--
-- Name: time_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.time_entries_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.time_entries_id_seq OWNER TO postgres;

--
-- Name: time_entries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.time_entries_id_seq OWNED BY public.time_entries.id;


--
-- Name: timecard; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.timecard (
    id integer NOT NULL,
    employee_id integer,
    project_id integer,
    business_unit_id integer,
    transdate date NOT NULL,
    description text,
    qty numeric(20,8) NOT NULL,
    sellprice numeric(20,8),
    fxsellprice numeric(20,8),
    curr character varying(3) DEFAULT 'SAR'::character varying,
    allocated numeric(20,8) DEFAULT 0,
    notes text,
    jctype integer,
    total numeric(20,8),
    non_billable boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.timecard OWNER TO postgres;

--
-- Name: timecard_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.timecard_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.timecard_id_seq OWNER TO postgres;

--
-- Name: timecard_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.timecard_id_seq OWNED BY public.timecard.id;


--
-- Name: user_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_permissions (
    id integer NOT NULL,
    user_id integer,
    permission_key character varying(100) NOT NULL,
    granted_by integer,
    granted_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_active boolean DEFAULT true
);


ALTER TABLE public.user_permissions OWNER TO postgres;

--
-- Name: user_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_permissions_id_seq OWNER TO postgres;

--
-- Name: user_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_permissions_id_seq OWNED BY public.user_permissions.id;


--
-- Name: user_role_assignments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_role_assignments (
    id integer NOT NULL,
    user_id integer,
    role_name character varying(50),
    assigned_by integer,
    assigned_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_active boolean DEFAULT true
);


ALTER TABLE public.user_role_assignments OWNER TO postgres;

--
-- Name: user_role_assignments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_role_assignments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_role_assignments_id_seq OWNER TO postgres;

--
-- Name: user_role_assignments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_role_assignments_id_seq OWNED BY public.user_role_assignments.id;


--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_roles (
    id integer NOT NULL,
    role_name character varying(50) NOT NULL,
    display_name character varying(100) NOT NULL,
    description text,
    permissions text[] DEFAULT '{}'::text[],
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.user_roles OWNER TO postgres;

--
-- Name: user_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_roles_id_seq OWNER TO postgres;

--
-- Name: user_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_roles_id_seq OWNED BY public.user_roles.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username character varying(255) NOT NULL,
    email character varying(255),
    password_hash character varying(255),
    status character varying(20) DEFAULT 'active'::character varying,
    last_login timestamp without time zone,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    device_id character varying(255),
    employee_id integer,
    is_active boolean DEFAULT true,
    last_logout timestamp without time zone,
    last_failed_login timestamp without time zone,
    is_online boolean DEFAULT false,
    login_attempts integer DEFAULT 0,
    locked_until timestamp without time zone,
    role character varying(50) DEFAULT 'user'::character varying,
    permissions text[] DEFAULT '{}'::text[],
    user_type character varying(20) DEFAULT 'user'::character varying
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: vouchers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.vouchers (
    id integer NOT NULL,
    voucher_number character varying(50) NOT NULL,
    voucher_type character varying(20) NOT NULL,
    voucher_date date NOT NULL,
    description text,
    total_amount numeric(15,2) DEFAULT 0 NOT NULL,
    reference_number character varying(100),
    status character varying(20) DEFAULT 'مسودة'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT vouchers_voucher_type_check CHECK (((voucher_type)::text = ANY (ARRAY[('قبض'::character varying)::text, ('صرف'::character varying)::text, ('قيد'::character varying)::text])))
);


ALTER TABLE public.vouchers OWNER TO postgres;

--
-- Name: vouchers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.vouchers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.vouchers_id_seq OWNER TO postgres;

--
-- Name: vouchers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.vouchers_id_seq OWNED BY public.vouchers.id;


--
-- Name: website_services; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.website_services (
    id integer NOT NULL,
    title text NOT NULL,
    slug text NOT NULL,
    description text,
    content text,
    icon_name text DEFAULT 'Scale'::text,
    icon_color text DEFAULT '#2563eb'::text,
    image_url text,
    is_active boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    meta_title text,
    meta_description text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.website_services OWNER TO postgres;

--
-- Name: website_services_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.website_services_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.website_services_id_seq OWNER TO postgres;

--
-- Name: website_services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.website_services_id_seq OWNED BY public.website_services.id;


--
-- Name: acc_trans entry_id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.acc_trans ALTER COLUMN entry_id SET DEFAULT nextval('public.acc_trans_entry_id_seq'::regclass);


--
-- Name: accounting_transaction_details id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transaction_details ALTER COLUMN id SET DEFAULT nextval('public.accounting_transaction_details_id_seq'::regclass);


--
-- Name: accounting_transactions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transactions ALTER COLUMN id SET DEFAULT nextval('public.accounting_transactions_id_seq'::regclass);


--
-- Name: accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts ALTER COLUMN id SET DEFAULT nextval('public.accounts_id_seq'::regclass);


--
-- Name: announcements id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.announcements ALTER COLUMN id SET DEFAULT nextval('public.announcements_id_seq'::regclass);


--
-- Name: ar id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ar ALTER COLUMN id SET DEFAULT nextval('public.ar_id_seq'::regclass);


--
-- Name: branches id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.branches ALTER COLUMN id SET DEFAULT nextval('public.branches_id_seq'::regclass);


--
-- Name: budget id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget ALTER COLUMN id SET DEFAULT nextval('public.budget_id_seq'::regclass);


--
-- Name: case_distribution id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_distribution ALTER COLUMN id SET DEFAULT nextval('public.case_distribution_id_seq'::regclass);


--
-- Name: chart_of_accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chart_of_accounts ALTER COLUMN id SET DEFAULT nextval('public.chart_of_accounts_id_seq'::regclass);


--
-- Name: client_notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications ALTER COLUMN id SET DEFAULT nextval('public.client_notifications_id_seq'::regclass);


--
-- Name: client_portal_accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts ALTER COLUMN id SET DEFAULT nextval('public.client_portal_accounts_id_seq'::regclass);


--
-- Name: client_requests id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests ALTER COLUMN id SET DEFAULT nextval('public.client_requests_id_seq'::regclass);


--
-- Name: client_sessions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_sessions ALTER COLUMN id SET DEFAULT nextval('public.client_sessions_id_seq'::regclass);


--
-- Name: clients id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients ALTER COLUMN id SET DEFAULT nextval('public.clients_id_seq'::regclass);


--
-- Name: companies id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.companies ALTER COLUMN id SET DEFAULT nextval('public.companies_id_seq'::regclass);


--
-- Name: company id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company ALTER COLUMN id SET DEFAULT nextval('public.company_id_seq'::regclass);


--
-- Name: conversations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversations ALTER COLUMN id SET DEFAULT nextval('public.conversations_id_seq'::regclass);


--
-- Name: cost_centers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cost_centers ALTER COLUMN id SET DEFAULT nextval('public.cost_centers_id_seq'::regclass);


--
-- Name: courts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.courts ALTER COLUMN id SET DEFAULT nextval('public.courts_id_seq'::regclass);


--
-- Name: currencies id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.currencies ALTER COLUMN id SET DEFAULT nextval('public.currencies_id_seq'::regclass);


--
-- Name: document_shares id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares ALTER COLUMN id SET DEFAULT nextval('public.document_shares_id_seq'::regclass);


--
-- Name: document_versions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_versions ALTER COLUMN id SET DEFAULT nextval('public.document_versions_id_seq'::regclass);


--
-- Name: documents id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents ALTER COLUMN id SET DEFAULT nextval('public.documents_id_seq'::regclass);


--
-- Name: employees id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees ALTER COLUMN id SET DEFAULT nextval('public.employees_id_seq'::regclass);


--
-- Name: entity id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity ALTER COLUMN id SET DEFAULT nextval('public.entity_id_seq'::regclass);


--
-- Name: entity_class id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_class ALTER COLUMN id SET DEFAULT nextval('public.entity_class_id_seq'::regclass);


--
-- Name: follows id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows ALTER COLUMN id SET DEFAULT nextval('public.follows_id_seq'::regclass);


--
-- Name: footer_links id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.footer_links ALTER COLUMN id SET DEFAULT nextval('public.footer_links_id_seq'::regclass);


--
-- Name: gl id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.gl ALTER COLUMN id SET DEFAULT nextval('public.gl_id_seq'::regclass);


--
-- Name: governorates id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.governorates ALTER COLUMN id SET DEFAULT nextval('public.governorates_id_seq'::regclass);


--
-- Name: hearings id; Type: DEFAULT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.hearings ALTER COLUMN id SET DEFAULT nextval('public.hearings_id_seq'::regclass);


--
-- Name: invoice id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice ALTER COLUMN id SET DEFAULT nextval('public.invoice_id_seq'::regclass);


--
-- Name: invoice_items id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items ALTER COLUMN id SET DEFAULT nextval('public.invoice_items_id_seq'::regclass);


--
-- Name: invoices id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices ALTER COLUMN id SET DEFAULT nextval('public.invoices_id_seq'::regclass);


--
-- Name: issue_types id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issue_types ALTER COLUMN id SET DEFAULT nextval('public.issue_types_id_seq'::regclass);


--
-- Name: issues id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues ALTER COLUMN id SET DEFAULT nextval('public.issues_id_seq'::regclass);


--
-- Name: journal_entries id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entries ALTER COLUMN id SET DEFAULT nextval('public.journal_entries_id_seq'::regclass);


--
-- Name: journal_entry_details id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entry_details ALTER COLUMN id SET DEFAULT nextval('public.journal_entry_details_id_seq'::regclass);


--
-- Name: lawyer_earnings id; Type: DEFAULT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.lawyer_earnings ALTER COLUMN id SET DEFAULT nextval('public.lawyer_earnings_id_seq'::regclass);


--
-- Name: lineages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.lineages ALTER COLUMN id SET DEFAULT nextval('public.lineages_id_seq'::regclass);


--
-- Name: main_accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.main_accounts ALTER COLUMN id SET DEFAULT nextval('public.main_accounts_id_seq'::regclass);


--
-- Name: message_read_status id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message_read_status ALTER COLUMN id SET DEFAULT nextval('public.message_read_status_id_seq'::regclass);


--
-- Name: messages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages ALTER COLUMN id SET DEFAULT nextval('public.messages_id_seq'::regclass);


--
-- Name: money_transactions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.money_transactions ALTER COLUMN id SET DEFAULT nextval('public.money_transactions_id_seq'::regclass);


--
-- Name: movements id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movements ALTER COLUMN id SET DEFAULT nextval('public.movements_id_seq'::regclass);


--
-- Name: navigation_pages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.navigation_pages ALTER COLUMN id SET DEFAULT nextval('public.navigation_pages_id_seq'::regclass);


--
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- Name: opening_balances id; Type: DEFAULT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.opening_balances ALTER COLUMN id SET DEFAULT nextval('public.opening_balances_id_seq'::regclass);


--
-- Name: opening_balances_history id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.opening_balances_history ALTER COLUMN id SET DEFAULT nextval('public.opening_balances_history_id_seq'::regclass);


--
-- Name: payment id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment ALTER COLUMN id SET DEFAULT nextval('public.payment_id_seq'::regclass);


--
-- Name: payment_methods id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_methods ALTER COLUMN id SET DEFAULT nextval('public.payment_methods_id_seq'::regclass);


--
-- Name: payment_vouchers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers ALTER COLUMN id SET DEFAULT nextval('public.payment_vouchers_id_seq'::regclass);


--
-- Name: permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions ALTER COLUMN id SET DEFAULT nextval('public.permissions_id_seq'::regclass);


--
-- Name: project id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project ALTER COLUMN id SET DEFAULT nextval('public.project_id_seq'::regclass);


--
-- Name: public_announcements id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.public_announcements ALTER COLUMN id SET DEFAULT nextval('public.public_announcements_id_seq'::regclass);


--
-- Name: receipt_vouchers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.receipt_vouchers ALTER COLUMN id SET DEFAULT nextval('public.receipt_vouchers_id_seq'::regclass);


--
-- Name: security_logs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.security_logs ALTER COLUMN id SET DEFAULT nextval('public.security_logs_id_seq'::regclass);


--
-- Name: service_distributions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_distributions ALTER COLUMN id SET DEFAULT nextval('public.service_distributions_id_seq'::regclass);


--
-- Name: services id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services ALTER COLUMN id SET DEFAULT nextval('public.services_id_seq'::regclass);


--
-- Name: serviceslow id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.serviceslow ALTER COLUMN id SET DEFAULT nextval('public.serviceslow_id_seq'::regclass);


--
-- Name: suppliers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.suppliers ALTER COLUMN id SET DEFAULT nextval('public.suppliers_id_seq'::regclass);


--
-- Name: test_table id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_table ALTER COLUMN id SET DEFAULT nextval('public.test_table_id_seq'::regclass);


--
-- Name: time_entries id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries ALTER COLUMN id SET DEFAULT nextval('public.time_entries_id_seq'::regclass);


--
-- Name: timecard id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.timecard ALTER COLUMN id SET DEFAULT nextval('public.timecard_id_seq'::regclass);


--
-- Name: user_permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions ALTER COLUMN id SET DEFAULT nextval('public.user_permissions_id_seq'::regclass);


--
-- Name: user_role_assignments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments ALTER COLUMN id SET DEFAULT nextval('public.user_role_assignments_id_seq'::regclass);


--
-- Name: user_roles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles ALTER COLUMN id SET DEFAULT nextval('public.user_roles_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: vouchers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vouchers ALTER COLUMN id SET DEFAULT nextval('public.vouchers_id_seq'::regclass);


--
-- Name: website_services id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.website_services ALTER COLUMN id SET DEFAULT nextval('public.website_services_id_seq'::regclass);


--
-- Name: acc_trans acc_trans_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.acc_trans
    ADD CONSTRAINT acc_trans_pkey PRIMARY KEY (entry_id);


--
-- Name: accounting_transaction_details accounting_transaction_details_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transaction_details
    ADD CONSTRAINT accounting_transaction_details_pkey PRIMARY KEY (id);


--
-- Name: accounting_transactions accounting_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transactions
    ADD CONSTRAINT accounting_transactions_pkey PRIMARY KEY (id);


--
-- Name: accounting_transactions accounting_transactions_transaction_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transactions
    ADD CONSTRAINT accounting_transactions_transaction_number_key UNIQUE (transaction_number);


--
-- Name: accounts accounts_account_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_account_code_key UNIQUE (account_code);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: ai_settings ai_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_settings
    ADD CONSTRAINT ai_settings_pkey PRIMARY KEY (id);


--
-- Name: announcements announcements_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.announcements
    ADD CONSTRAINT announcements_pkey PRIMARY KEY (id);


--
-- Name: ar ar_invnumber_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ar
    ADD CONSTRAINT ar_invnumber_key UNIQUE (invnumber);


--
-- Name: ar ar_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ar
    ADD CONSTRAINT ar_pkey PRIMARY KEY (id);


--
-- Name: branches branches_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.branches
    ADD CONSTRAINT branches_pkey PRIMARY KEY (id);


--
-- Name: budget budget_budget_year_account_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget
    ADD CONSTRAINT budget_budget_year_account_id_key UNIQUE (budget_year, account_id);


--
-- Name: budget budget_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget
    ADD CONSTRAINT budget_pkey PRIMARY KEY (id);


--
-- Name: case_distribution case_distribution_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_distribution
    ADD CONSTRAINT case_distribution_pkey PRIMARY KEY (id);


--
-- Name: chart_of_accounts chart_of_accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chart_of_accounts
    ADD CONSTRAINT chart_of_accounts_pkey PRIMARY KEY (id);


--
-- Name: client_notifications client_notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications
    ADD CONSTRAINT client_notifications_pkey PRIMARY KEY (id);


--
-- Name: client_portal_accounts client_portal_accounts_client_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_client_id_key UNIQUE (client_id);


--
-- Name: client_portal_accounts client_portal_accounts_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_email_key UNIQUE (email);


--
-- Name: client_portal_accounts client_portal_accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_pkey PRIMARY KEY (id);


--
-- Name: client_portal_accounts client_portal_accounts_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_username_key UNIQUE (username);


--
-- Name: client_requests client_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests
    ADD CONSTRAINT client_requests_pkey PRIMARY KEY (id);


--
-- Name: client_sessions client_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT client_sessions_pkey PRIMARY KEY (id);


--
-- Name: client_sessions client_sessions_session_token_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT client_sessions_session_token_key UNIQUE (session_token);


--
-- Name: clients clients_id_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_id_number_key UNIQUE (id_number);


--
-- Name: clients clients_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_pkey PRIMARY KEY (id);


--
-- Name: clients clients_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_username_key UNIQUE (username);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (id);


--
-- Name: company company_control_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_control_code_key UNIQUE (control_code);


--
-- Name: company company_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_pkey PRIMARY KEY (id);


--
-- Name: conversations conversations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_pkey PRIMARY KEY (id);


--
-- Name: cost_centers cost_centers_center_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cost_centers
    ADD CONSTRAINT cost_centers_center_code_key UNIQUE (center_code);


--
-- Name: cost_centers cost_centers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cost_centers
    ADD CONSTRAINT cost_centers_pkey PRIMARY KEY (id);


--
-- Name: courts courts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.courts
    ADD CONSTRAINT courts_pkey PRIMARY KEY (id);


--
-- Name: currencies currencies_currency_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.currencies
    ADD CONSTRAINT currencies_currency_code_key UNIQUE (currency_code);


--
-- Name: currencies currencies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.currencies
    ADD CONSTRAINT currencies_pkey PRIMARY KEY (id);


--
-- Name: document_shares document_shares_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_pkey PRIMARY KEY (id);


--
-- Name: document_versions document_versions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_versions
    ADD CONSTRAINT document_versions_pkey PRIMARY KEY (id);


--
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (id);


--
-- Name: employees employees_id_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_id_number_key UNIQUE (id_number);


--
-- Name: employees employees_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_pkey PRIMARY KEY (id);


--
-- Name: entity_class entity_class_class_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_class
    ADD CONSTRAINT entity_class_class_key UNIQUE (class);


--
-- Name: entity_class entity_class_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_class
    ADD CONSTRAINT entity_class_pkey PRIMARY KEY (id);


--
-- Name: entity entity_control_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity
    ADD CONSTRAINT entity_control_code_key UNIQUE (control_code);


--
-- Name: entity entity_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity
    ADD CONSTRAINT entity_pkey PRIMARY KEY (id);


--
-- Name: follows follows_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_pkey PRIMARY KEY (id);


--
-- Name: footer_links footer_links_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.footer_links
    ADD CONSTRAINT footer_links_pkey PRIMARY KEY (id);


--
-- Name: gl gl_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.gl
    ADD CONSTRAINT gl_pkey PRIMARY KEY (id);


--
-- Name: governorates governorates_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.governorates
    ADD CONSTRAINT governorates_code_key UNIQUE (code);


--
-- Name: governorates governorates_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.governorates
    ADD CONSTRAINT governorates_pkey PRIMARY KEY (id);


--
-- Name: hearings hearings_pkey; Type: CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.hearings
    ADD CONSTRAINT hearings_pkey PRIMARY KEY (id);


--
-- Name: invoice_items invoice_items_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items
    ADD CONSTRAINT invoice_items_pkey PRIMARY KEY (id);


--
-- Name: invoice invoice_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice
    ADD CONSTRAINT invoice_pkey PRIMARY KEY (id);


--
-- Name: invoices invoices_invoice_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_invoice_number_key UNIQUE (invoice_number);


--
-- Name: invoices invoices_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_pkey PRIMARY KEY (id);


--
-- Name: issue_types issue_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issue_types
    ADD CONSTRAINT issue_types_pkey PRIMARY KEY (id);


--
-- Name: issues issues_case_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT issues_case_number_key UNIQUE (case_number);


--
-- Name: issues issues_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT issues_pkey PRIMARY KEY (id);


--
-- Name: journal_entries journal_entries_entry_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entries
    ADD CONSTRAINT journal_entries_entry_number_key UNIQUE (entry_number);


--
-- Name: journal_entries journal_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entries
    ADD CONSTRAINT journal_entries_pkey PRIMARY KEY (id);


--
-- Name: journal_entry_details journal_entry_details_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entry_details
    ADD CONSTRAINT journal_entry_details_pkey PRIMARY KEY (id);


--
-- Name: lawyer_earnings lawyer_earnings_pkey; Type: CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.lawyer_earnings
    ADD CONSTRAINT lawyer_earnings_pkey PRIMARY KEY (id);


--
-- Name: lineages lineages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.lineages
    ADD CONSTRAINT lineages_pkey PRIMARY KEY (id);


--
-- Name: main_accounts main_accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.main_accounts
    ADD CONSTRAINT main_accounts_pkey PRIMARY KEY (id);


--
-- Name: message_read_status message_read_status_message_id_reader_type_reader_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message_read_status
    ADD CONSTRAINT message_read_status_message_id_reader_type_reader_id_key UNIQUE (message_id, reader_type, reader_id);


--
-- Name: message_read_status message_read_status_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message_read_status
    ADD CONSTRAINT message_read_status_pkey PRIMARY KEY (id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id);


--
-- Name: money_transactions money_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.money_transactions
    ADD CONSTRAINT money_transactions_pkey PRIMARY KEY (id);


--
-- Name: movements movements_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movements
    ADD CONSTRAINT movements_pkey PRIMARY KEY (id);


--
-- Name: navigation_pages navigation_pages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.navigation_pages
    ADD CONSTRAINT navigation_pages_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: opening_balances_history opening_balances_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.opening_balances_history
    ADD CONSTRAINT opening_balances_history_pkey PRIMARY KEY (id);


--
-- Name: opening_balances opening_balances_pkey; Type: CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.opening_balances
    ADD CONSTRAINT opening_balances_pkey PRIMARY KEY (id);


--
-- Name: payment_links payment_links_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_links
    ADD CONSTRAINT payment_links_pkey PRIMARY KEY (payment_id, entry_id);


--
-- Name: payment_methods payment_methods_method_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_methods
    ADD CONSTRAINT payment_methods_method_code_key UNIQUE (method_code);


--
-- Name: payment_methods payment_methods_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_methods
    ADD CONSTRAINT payment_methods_pkey PRIMARY KEY (id);


--
-- Name: payment payment_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment
    ADD CONSTRAINT payment_pkey PRIMARY KEY (id);


--
-- Name: payment_vouchers payment_vouchers_entry_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers
    ADD CONSTRAINT payment_vouchers_entry_number_key UNIQUE (entry_number);


--
-- Name: payment_vouchers payment_vouchers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers
    ADD CONSTRAINT payment_vouchers_pkey PRIMARY KEY (id);


--
-- Name: permissions permissions_permission_key_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_permission_key_key UNIQUE (permission_key);


--
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- Name: project project_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project
    ADD CONSTRAINT project_pkey PRIMARY KEY (id);


--
-- Name: project project_projectnumber_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project
    ADD CONSTRAINT project_projectnumber_key UNIQUE (projectnumber);


--
-- Name: public_announcements public_announcements_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.public_announcements
    ADD CONSTRAINT public_announcements_pkey PRIMARY KEY (id);


--
-- Name: receipt_vouchers receipt_vouchers_entry_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.receipt_vouchers
    ADD CONSTRAINT receipt_vouchers_entry_number_key UNIQUE (entry_number);


--
-- Name: receipt_vouchers receipt_vouchers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.receipt_vouchers
    ADD CONSTRAINT receipt_vouchers_pkey PRIMARY KEY (id);


--
-- Name: security_logs security_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.security_logs
    ADD CONSTRAINT security_logs_pkey PRIMARY KEY (id);


--
-- Name: service_distributions service_distributions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_distributions
    ADD CONSTRAINT service_distributions_pkey PRIMARY KEY (id);


--
-- Name: services services_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_pkey PRIMARY KEY (id);


--
-- Name: serviceslow serviceslow_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.serviceslow
    ADD CONSTRAINT serviceslow_pkey PRIMARY KEY (id);


--
-- Name: serviceslow serviceslow_slug_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.serviceslow
    ADD CONSTRAINT serviceslow_slug_key UNIQUE (slug);


--
-- Name: suppliers suppliers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.suppliers
    ADD CONSTRAINT suppliers_pkey PRIMARY KEY (id);


--
-- Name: test_table test_table_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_table
    ADD CONSTRAINT test_table_pkey PRIMARY KEY (id);


--
-- Name: time_entries time_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_pkey PRIMARY KEY (id);


--
-- Name: timecard timecard_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.timecard
    ADD CONSTRAINT timecard_pkey PRIMARY KEY (id);


--
-- Name: chart_of_accounts unique_level_code; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chart_of_accounts
    ADD CONSTRAINT unique_level_code UNIQUE (account_code);


--
-- Name: user_permissions user_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_pkey PRIMARY KEY (id);


--
-- Name: user_permissions user_permissions_user_id_permission_key_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_user_id_permission_key_key UNIQUE (user_id, permission_key);


--
-- Name: user_role_assignments user_role_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_pkey PRIMARY KEY (id);


--
-- Name: user_role_assignments user_role_assignments_user_id_role_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_user_id_role_name_key UNIQUE (user_id, role_name);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_role_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_name_key UNIQUE (role_name);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: vouchers vouchers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vouchers
    ADD CONSTRAINT vouchers_pkey PRIMARY KEY (id);


--
-- Name: vouchers vouchers_voucher_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vouchers
    ADD CONSTRAINT vouchers_voucher_number_key UNIQUE (voucher_number);


--
-- Name: website_services website_services_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.website_services
    ADD CONSTRAINT website_services_pkey PRIMARY KEY (id);


--
-- Name: website_services website_services_slug_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.website_services
    ADD CONSTRAINT website_services_slug_key UNIQUE (slug);


--
-- Name: idx_acc_trans_chart_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_acc_trans_chart_id ON public.acc_trans USING btree (chart_id);


--
-- Name: idx_acc_trans_trans_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_acc_trans_trans_id ON public.acc_trans USING btree (trans_id);


--
-- Name: idx_acc_trans_transdate; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_acc_trans_transdate ON public.acc_trans USING btree (transdate);


--
-- Name: idx_accounting_transaction_details_account; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transaction_details_account ON public.accounting_transaction_details USING btree (account_id);


--
-- Name: idx_accounting_transaction_details_transaction; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transaction_details_transaction ON public.accounting_transaction_details USING btree (transaction_id);


--
-- Name: idx_accounting_transactions_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transactions_date ON public.accounting_transactions USING btree (transaction_date);


--
-- Name: idx_accounting_transactions_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transactions_status ON public.accounting_transactions USING btree (status);


--
-- Name: idx_accounting_transactions_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transactions_type ON public.accounting_transactions USING btree (transaction_type);


--
-- Name: idx_accounts_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounts_code ON public.accounts USING btree (account_code);


--
-- Name: idx_accounts_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounts_type ON public.accounts USING btree (account_type);


--
-- Name: idx_ai_settings_enabled; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ai_settings_enabled ON public.ai_settings USING btree (enabled);


--
-- Name: idx_announcements_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_announcements_active ON public.announcements USING btree (is_active);


--
-- Name: idx_announcements_created_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_announcements_created_date ON public.announcements USING btree (created_date);


--
-- Name: idx_ar_entity_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ar_entity_id ON public.ar USING btree (entity_id);


--
-- Name: idx_ar_transdate; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ar_transdate ON public.ar USING btree (transdate);


--
-- Name: idx_chart_account_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_account_code ON public.chart_of_accounts USING btree (account_code);


--
-- Name: idx_chart_account_level; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_account_level ON public.chart_of_accounts USING btree (account_level);


--
-- Name: idx_chart_accounts_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_accounts_code ON public.chart_of_accounts USING btree (account_code);


--
-- Name: idx_chart_accounts_linked_table; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_accounts_linked_table ON public.chart_of_accounts USING btree (linked_table);


--
-- Name: idx_chart_accounts_parent; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_accounts_parent ON public.chart_of_accounts USING btree (parent_id);


--
-- Name: idx_chart_accounts_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_accounts_type ON public.chart_of_accounts USING btree (account_type);


--
-- Name: idx_chart_linked_record; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_linked_record ON public.chart_of_accounts USING btree (is_linked_record, original_table, linked_record_id);


--
-- Name: idx_chart_linked_table; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_linked_table ON public.chart_of_accounts USING btree (linked_table);


--
-- Name: idx_chart_parent_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_parent_id ON public.chart_of_accounts USING btree (parent_id);


--
-- Name: idx_client_notifications_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_client_notifications_client_id ON public.client_notifications USING btree (client_id);


--
-- Name: idx_client_notifications_read; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_client_notifications_read ON public.client_notifications USING btree (is_read);


--
-- Name: idx_clients_account_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_account_id ON public.clients USING btree (account_id);


--
-- Name: idx_clients_account_id_not_null; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_account_id_not_null ON public.clients USING btree (account_id) WHERE (account_id IS NOT NULL);


--
-- Name: idx_clients_last_login; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_last_login ON public.clients USING btree (last_login);


--
-- Name: idx_clients_online; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_online ON public.clients USING btree (is_online);


--
-- Name: idx_clients_username; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_username ON public.clients USING btree (username);


--
-- Name: idx_conversations_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_conversations_client_id ON public.conversations USING btree (client_id);


--
-- Name: idx_conversations_last_message; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_conversations_last_message ON public.conversations USING btree (last_message_at DESC);


--
-- Name: idx_conversations_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_conversations_status ON public.conversations USING btree (status);


--
-- Name: idx_conversations_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_conversations_user_id ON public.conversations USING btree (user_id);


--
-- Name: idx_cost_centers_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cost_centers_active ON public.cost_centers USING btree (is_active);


--
-- Name: idx_cost_centers_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cost_centers_code ON public.cost_centers USING btree (center_code);


--
-- Name: idx_cost_centers_parent; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cost_centers_parent ON public.cost_centers USING btree (parent_id);


--
-- Name: idx_currencies_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_currencies_code ON public.currencies USING btree (currency_code);


--
-- Name: idx_documents_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_case_id ON public.documents USING btree (case_id);


--
-- Name: idx_documents_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_category ON public.documents USING btree (category);


--
-- Name: idx_documents_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_client_id ON public.documents USING btree (client_id);


--
-- Name: idx_documents_content_text; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_content_text ON public.documents USING gin (to_tsvector('arabic'::regconfig, content_text));


--
-- Name: idx_documents_tags; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_tags ON public.documents USING gin (tags);


--
-- Name: idx_employees_account_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_employees_account_id ON public.employees USING btree (account_id);


--
-- Name: idx_employees_account_id_not_null; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_employees_account_id_not_null ON public.employees USING btree (account_id) WHERE (account_id IS NOT NULL);


--
-- Name: idx_follows_approved; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_follows_approved ON public.follows USING btree (is_approved);


--
-- Name: idx_follows_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_follows_user_id ON public.follows USING btree (user_id);


--
-- Name: idx_gl_transdate; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_gl_transdate ON public.gl USING btree (transdate);


--
-- Name: idx_invoices_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_invoices_client_id ON public.invoices USING btree (client_id);


--
-- Name: idx_invoices_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_invoices_date ON public.invoices USING btree (invoice_date);


--
-- Name: idx_invoices_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_invoices_status ON public.invoices USING btree (status);


--
-- Name: idx_issues_case_number; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_case_number ON public.issues USING btree (case_number);


--
-- Name: idx_issues_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_client_id ON public.issues USING btree (client_id);


--
-- Name: idx_issues_court_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_court_id ON public.issues USING btree (court_id);


--
-- Name: idx_issues_created_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_created_date ON public.issues USING btree (created_date);


--
-- Name: idx_issues_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_status ON public.issues USING btree (status);


--
-- Name: idx_journal_entries_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_journal_entries_date ON public.journal_entries USING btree (entry_date);


--
-- Name: idx_journal_entries_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_journal_entries_status ON public.journal_entries USING btree (status);


--
-- Name: idx_journal_entries_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_journal_entries_type ON public.journal_entries USING btree (entry_type);


--
-- Name: idx_journal_entry_details_account; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_journal_entry_details_account ON public.journal_entry_details USING btree (account_id);


--
-- Name: idx_lawyer_earnings_case_id; Type: INDEX; Schema: public; Owner: yemen
--

CREATE INDEX idx_lawyer_earnings_case_id ON public.lawyer_earnings USING btree (case_id);


--
-- Name: idx_lawyer_earnings_lawyer_id; Type: INDEX; Schema: public; Owner: yemen
--

CREATE INDEX idx_lawyer_earnings_lawyer_id ON public.lawyer_earnings USING btree (lawyer_id);


--
-- Name: idx_lawyer_earnings_service_id; Type: INDEX; Schema: public; Owner: yemen
--

CREATE INDEX idx_lawyer_earnings_service_id ON public.lawyer_earnings USING btree (service_id);


--
-- Name: idx_main_accounts_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_main_accounts_code ON public.main_accounts USING btree (account_code);


--
-- Name: idx_main_accounts_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_main_accounts_name ON public.main_accounts USING btree (account_name);


--
-- Name: idx_message_read_status_message; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_message_read_status_message ON public.message_read_status USING btree (message_id);


--
-- Name: idx_message_read_status_reader; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_message_read_status_reader ON public.message_read_status USING btree (reader_type, reader_id);


--
-- Name: idx_messages_conversation_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_conversation_id ON public.messages USING btree (conversation_id);


--
-- Name: idx_messages_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_created_at ON public.messages USING btree (created_at DESC);


--
-- Name: idx_messages_is_read; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_is_read ON public.messages USING btree (is_read);


--
-- Name: idx_messages_reply_to; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_reply_to ON public.messages USING btree (reply_to_message_id);


--
-- Name: idx_messages_sender; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_sender ON public.messages USING btree (sender_type, sender_id);


--
-- Name: idx_notifications_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_notifications_created_at ON public.notifications USING btree (created_at DESC);


--
-- Name: idx_notifications_is_read; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_notifications_is_read ON public.notifications USING btree (is_read);


--
-- Name: idx_notifications_recipient; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_notifications_recipient ON public.notifications USING btree (recipient_type, recipient_id);


--
-- Name: idx_payment_entity_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payment_entity_id ON public.payment USING btree (entity_id);


--
-- Name: idx_payment_vouchers_entry_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payment_vouchers_entry_date ON public.payment_vouchers USING btree (entry_date);


--
-- Name: idx_payment_vouchers_payee_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payment_vouchers_payee_type ON public.payment_vouchers USING btree (payee_type);


--
-- Name: idx_payment_vouchers_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payment_vouchers_status ON public.payment_vouchers USING btree (status);


--
-- Name: idx_project_customer_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_project_customer_id ON public.project USING btree (customer_id);


--
-- Name: idx_serviceslow_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_serviceslow_active ON public.serviceslow USING btree (is_active);


--
-- Name: idx_serviceslow_slug; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_serviceslow_slug ON public.serviceslow USING btree (slug);


--
-- Name: idx_serviceslow_sort; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_serviceslow_sort ON public.serviceslow USING btree (sort_order);


--
-- Name: idx_suppliers_account_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_suppliers_account_id ON public.suppliers USING btree (account_id);


--
-- Name: idx_time_entries_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_time_entries_case_id ON public.time_entries USING btree (case_id);


--
-- Name: idx_time_entries_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_time_entries_date ON public.time_entries USING btree (start_time);


--
-- Name: idx_time_entries_employee_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_time_entries_employee_id ON public.time_entries USING btree (employee_id);


--
-- Name: idx_timecard_employee_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_timecard_employee_id ON public.timecard USING btree (employee_id);


--
-- Name: idx_timecard_project_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_timecard_project_id ON public.timecard USING btree (project_id);


--
-- Name: idx_user_role_assignments_role_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_user_role_assignments_role_name ON public.user_role_assignments USING btree (role_name);


--
-- Name: idx_user_role_assignments_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_user_role_assignments_user_id ON public.user_role_assignments USING btree (user_id);


--
-- Name: idx_users_device_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_device_id ON public.users USING btree (device_id);


--
-- Name: idx_users_last_login; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_last_login ON public.users USING btree (last_login);


--
-- Name: idx_users_online; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_online ON public.users USING btree (is_online);


--
-- Name: idx_users_username; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_username ON public.users USING btree (username);


--
-- Name: idx_vouchers_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_vouchers_date ON public.vouchers USING btree (voucher_date);


--
-- Name: idx_vouchers_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_vouchers_type ON public.vouchers USING btree (voucher_type);


--
-- Name: clients trigger_auto_link_client_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_auto_link_client_account BEFORE INSERT ON public.clients FOR EACH ROW EXECUTE FUNCTION public.auto_link_client_account();


--
-- Name: employees trigger_auto_link_employee_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_auto_link_employee_account BEFORE INSERT ON public.employees FOR EACH ROW EXECUTE FUNCTION public.auto_link_employee_account();


--
-- Name: clients trigger_create_client_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_create_client_account BEFORE INSERT ON public.clients FOR EACH ROW WHEN (((new.status)::text = 'active'::text)) EXECUTE FUNCTION public.create_client_account();


--
-- Name: employees trigger_create_employee_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_create_employee_account BEFORE INSERT ON public.employees FOR EACH ROW WHEN (((new.status)::text = 'active'::text)) EXECUTE FUNCTION public.create_employee_account();


--
-- Name: suppliers trigger_create_supplier_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_create_supplier_account AFTER INSERT ON public.suppliers FOR EACH ROW WHEN (((new.status)::text = 'active'::text)) EXECUTE FUNCTION public.create_supplier_account();


--
-- Name: clients trigger_update_client_account_name; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_client_account_name AFTER UPDATE OF name ON public.clients FOR EACH ROW WHEN ((((old.name)::text IS DISTINCT FROM (new.name)::text) AND (new.account_id IS NOT NULL))) EXECUTE FUNCTION public.update_client_account_name();


--
-- Name: messages trigger_update_conversation_last_message; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_conversation_last_message AFTER INSERT ON public.messages FOR EACH ROW EXECUTE FUNCTION public.update_conversation_last_message();


--
-- Name: employees trigger_update_employee_account_name; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_employee_account_name AFTER UPDATE OF name ON public.employees FOR EACH ROW WHEN ((((old.name)::text IS DISTINCT FROM (new.name)::text) AND (new.account_id IS NOT NULL))) EXECUTE FUNCTION public.update_employee_account_name();


--
-- Name: clients trigger_update_parent_balance_clients; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_parent_balance_clients AFTER INSERT OR DELETE OR UPDATE OF current_balance ON public.clients FOR EACH ROW EXECUTE FUNCTION public.update_parent_balance_on_client_change();


--
-- Name: employees trigger_update_parent_balance_employees; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_parent_balance_employees AFTER INSERT OR DELETE OR UPDATE OF current_balance ON public.employees FOR EACH ROW EXECUTE FUNCTION public.update_parent_balance_on_employee_change();


--
-- Name: suppliers trigger_update_parent_balance_suppliers; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_parent_balance_suppliers AFTER INSERT OR DELETE OR UPDATE OF current_balance ON public.suppliers FOR EACH ROW EXECUTE FUNCTION public.update_parent_balance_on_supplier_change();


--
-- Name: acc_trans acc_trans_trans_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.acc_trans
    ADD CONSTRAINT acc_trans_trans_id_fkey FOREIGN KEY (trans_id) REFERENCES public.gl(id);


--
-- Name: accounting_transaction_details accounting_transaction_details_transaction_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transaction_details
    ADD CONSTRAINT accounting_transaction_details_transaction_id_fkey FOREIGN KEY (transaction_id) REFERENCES public.accounting_transactions(id) ON DELETE CASCADE;


--
-- Name: accounts accounts_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.accounts(id);


--
-- Name: branches branches_governorate_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.branches
    ADD CONSTRAINT branches_governorate_id_fkey FOREIGN KEY (governorate_id) REFERENCES public.governorates(id);


--
-- Name: case_distribution case_distribution_lineage_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_distribution
    ADD CONSTRAINT case_distribution_lineage_id_fkey FOREIGN KEY (lineage_id) REFERENCES public.lineages(id) ON DELETE SET NULL;


--
-- Name: chart_of_accounts chart_of_accounts_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chart_of_accounts
    ADD CONSTRAINT chart_of_accounts_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.chart_of_accounts(id);


--
-- Name: client_notifications client_notifications_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications
    ADD CONSTRAINT client_notifications_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- Name: client_notifications client_notifications_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications
    ADD CONSTRAINT client_notifications_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: client_notifications client_notifications_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications
    ADD CONSTRAINT client_notifications_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id);


--
-- Name: client_portal_accounts client_portal_accounts_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: client_requests client_requests_assigned_to_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests
    ADD CONSTRAINT client_requests_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.employees(id);


--
-- Name: client_requests client_requests_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests
    ADD CONSTRAINT client_requests_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- Name: client_requests client_requests_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests
    ADD CONSTRAINT client_requests_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: client_sessions client_sessions_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT client_sessions_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: clients clients_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.chart_of_accounts(id);


--
-- Name: conversations conversations_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id) ON DELETE CASCADE;


--
-- Name: conversations conversations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: cost_centers cost_centers_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cost_centers
    ADD CONSTRAINT cost_centers_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.cost_centers(id) ON DELETE SET NULL;


--
-- Name: document_shares document_shares_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: document_shares document_shares_shared_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_shared_by_fkey FOREIGN KEY (shared_by) REFERENCES public.users(id);


--
-- Name: document_shares document_shares_shared_with_client_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_shared_with_client_fkey FOREIGN KEY (shared_with_client) REFERENCES public.clients(id);


--
-- Name: document_shares document_shares_shared_with_user_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_shared_with_user_fkey FOREIGN KEY (shared_with_user) REFERENCES public.users(id);


--
-- Name: document_versions document_versions_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_versions
    ADD CONSTRAINT document_versions_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- Name: document_versions document_versions_uploaded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_versions
    ADD CONSTRAINT document_versions_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- Name: documents documents_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- Name: documents documents_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: documents documents_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.employees(id);


--
-- Name: documents documents_uploaded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- Name: employees employees_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.chart_of_accounts(id);


--
-- Name: employees employees_branch_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_branch_id_fkey FOREIGN KEY (branch_id) REFERENCES public.branches(id);


--
-- Name: employees employees_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.courts(id);


--
-- Name: employees employees_governorate_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_governorate_id_fkey FOREIGN KEY (governorate_id) REFERENCES public.governorates(id);


--
-- Name: issues fk_issues_client_id; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT fk_issues_client_id FOREIGN KEY (client_id) REFERENCES public.clients(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: follows follows_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- Name: follows follows_next_hearing_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_next_hearing_id_fkey FOREIGN KEY (next_hearing_id) REFERENCES public.hearings(id) ON DELETE SET NULL;


--
-- Name: hearings hearings_issue_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.hearings
    ADD CONSTRAINT hearings_issue_id_fkey FOREIGN KEY (issue_id) REFERENCES public.issues(id);


--
-- Name: invoice_items invoice_items_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items
    ADD CONSTRAINT invoice_items_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- Name: invoice_items invoice_items_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items
    ADD CONSTRAINT invoice_items_invoice_id_fkey FOREIGN KEY (invoice_id) REFERENCES public.invoices(id) ON DELETE CASCADE;


--
-- Name: invoice_items invoice_items_time_entry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items
    ADD CONSTRAINT invoice_items_time_entry_id_fkey FOREIGN KEY (time_entry_id) REFERENCES public.time_entries(id);


--
-- Name: invoice invoice_trans_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice
    ADD CONSTRAINT invoice_trans_id_fkey FOREIGN KEY (trans_id) REFERENCES public.ar(id);


--
-- Name: invoices invoices_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: invoices invoices_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: issues issues_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT issues_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: issues issues_issue_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT issues_issue_type_id_fkey FOREIGN KEY (issue_type_id) REFERENCES public.issue_types(id);


--
-- Name: journal_entry_details journal_entry_details_journal_entry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entry_details
    ADD CONSTRAINT journal_entry_details_journal_entry_id_fkey FOREIGN KEY (journal_entry_id) REFERENCES public.journal_entries(id) ON DELETE CASCADE;


--
-- Name: lawyer_earnings lawyer_earnings_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.lawyer_earnings
    ADD CONSTRAINT lawyer_earnings_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- Name: lawyer_earnings lawyer_earnings_follow_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.lawyer_earnings
    ADD CONSTRAINT lawyer_earnings_follow_id_fkey FOREIGN KEY (follow_id) REFERENCES public.follows(id);


--
-- Name: main_accounts main_accounts_chart_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.main_accounts
    ADD CONSTRAINT main_accounts_chart_account_id_fkey FOREIGN KEY (chart_account_id) REFERENCES public.chart_of_accounts(id);


--
-- Name: message_read_status message_read_status_message_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message_read_status
    ADD CONSTRAINT message_read_status_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id) ON DELETE CASCADE;


--
-- Name: messages messages_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(id) ON DELETE CASCADE;


--
-- Name: messages messages_reply_to_message_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_reply_to_message_id_fkey FOREIGN KEY (reply_to_message_id) REFERENCES public.messages(id) ON DELETE SET NULL;


--
-- Name: movements movements_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movements
    ADD CONSTRAINT movements_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- Name: payment payment_gl_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment
    ADD CONSTRAINT payment_gl_id_fkey FOREIGN KEY (gl_id) REFERENCES public.gl(id);


--
-- Name: payment_links payment_links_entry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_links
    ADD CONSTRAINT payment_links_entry_id_fkey FOREIGN KEY (entry_id) REFERENCES public.acc_trans(entry_id);


--
-- Name: payment_links payment_links_payment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_links
    ADD CONSTRAINT payment_links_payment_id_fkey FOREIGN KEY (payment_id) REFERENCES public.payment(id);


--
-- Name: payment_vouchers payment_vouchers_credit_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers
    ADD CONSTRAINT payment_vouchers_credit_account_id_fkey FOREIGN KEY (credit_account_id) REFERENCES public.chart_of_accounts(id);


--
-- Name: payment_vouchers payment_vouchers_debit_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers
    ADD CONSTRAINT payment_vouchers_debit_account_id_fkey FOREIGN KEY (debit_account_id) REFERENCES public.chart_of_accounts(id);


--
-- Name: service_distributions service_distributions_case_distribution_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_distributions
    ADD CONSTRAINT service_distributions_case_distribution_id_fkey FOREIGN KEY (case_distribution_id) REFERENCES public.case_distribution(id) ON DELETE CASCADE;


--
-- Name: services services_lineage_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_lineage_id_fkey FOREIGN KEY (lineage_id) REFERENCES public.lineages(id);


--
-- Name: suppliers suppliers_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.suppliers
    ADD CONSTRAINT suppliers_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.main_accounts(id);


--
-- Name: time_entries time_entries_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- Name: time_entries time_entries_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- Name: time_entries time_entries_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.employees(id);


--
-- Name: timecard timecard_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.timecard
    ADD CONSTRAINT timecard_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.project(id);


--
-- Name: user_permissions user_permissions_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id);


--
-- Name: user_permissions user_permissions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_role_assignments user_role_assignments_assigned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.users(id);


--
-- Name: user_role_assignments user_role_assignments_role_name_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_role_name_fkey FOREIGN KEY (role_name) REFERENCES public.user_roles(role_name) ON DELETE CASCADE;


--
-- Name: user_role_assignments user_role_assignments_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: users users_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.employees(id);


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;
GRANT ALL ON SCHEMA public TO PUBLIC;


--
-- PostgreSQL database dump complete
--

