--
-- PostgreSQL database dump
--

-- Dumped from database version 14.18 (Ubuntu 14.18-1.pgdg24.04+1)
-- Dumped by pg_dump version 16.9 (Ubuntu 16.9-0ubuntu0.24.04.1)

-- Started on 2025-08-27 17:54:25 +03

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

DROP DATABASE IF EXISTS mohammi;
--
-- TOC entry 4766 (class 1262 OID 16385)
-- Name: mohammi; Type: DATABASE; Schema: -; Owner: postgres
--

CREATE DATABASE mohammi WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.UTF-8';


ALTER DATABASE mohammi OWNER TO postgres;

\connect mohammi

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 4 (class 2615 OID 2200)
-- Name: public; Type: SCHEMA; Schema: -; Owner: postgres
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO postgres;

--
-- TOC entry 355 (class 1255 OID 16386)
-- Name: auto_link_client_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.auto_link_client_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
      DECLARE
        settings_id INTEGER;
      BEGIN
        -- البحث عن إعدادات ربط العملاء
        SELECT id INTO settings_id
        FROM account_linking_settings
        WHERE table_name = 'clients' AND is_enabled = true
        LIMIT 1;

        -- إذا تم العثور على الإعدادات، قم بالربط
        IF settings_id IS NOT NULL THEN
          NEW.account_id = settings_id;
        END IF;

        RETURN NEW;
      END;
      $$;


ALTER FUNCTION public.auto_link_client_account() OWNER TO postgres;

--
-- TOC entry 357 (class 1255 OID 16387)
-- Name: auto_link_employee_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.auto_link_employee_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
      DECLARE
        settings_id INTEGER;
      BEGIN
        -- البحث عن إعدادات ربط الموظفين
        SELECT id INTO settings_id
        FROM account_linking_settings
        WHERE table_name = 'employees' AND is_enabled = true
        LIMIT 1;

        -- إذا تم العثور على الإعدادات، قم بالربط
        IF settings_id IS NOT NULL THEN
          NEW.account_id = settings_id;
        END IF;

        RETURN NEW;
      END;
      $$;


ALTER FUNCTION public.auto_link_employee_account() OWNER TO postgres;

--
-- TOC entry 375 (class 1255 OID 18540)
-- Name: calculate_parent_account_balance(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.calculate_parent_account_balance(p_account_id integer) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
DECLARE
    total_balance DECIMAL(15,2) := 0;
BEGIN
    -- حساب مجموع أرصدة العملاء المرتبطين
    SELECT COALESCE(SUM(
        CASE 
            WHEN al.linked_table = 'clients' THEN COALESCE(c.current_balance, 0)
            WHEN al.linked_table = 'employees' THEN COALESCE(e.current_balance, 0) 
            WHEN al.linked_table = 'suppliers' THEN COALESCE(s.current_balance, 0)
            ELSE 0
        END
    ), 0) INTO total_balance
    FROM account_links al
    LEFT JOIN clients c ON al.linked_table = 'clients' AND al.linked_record_id = c.id
    LEFT JOIN employees e ON al.linked_table = 'employees' AND al.linked_record_id = e.id  
    LEFT JOIN suppliers s ON al.linked_table = 'suppliers' AND al.linked_record_id = s.id
    WHERE al.main_account_id = p_account_id AND al.is_active = true;
    
    RETURN total_balance;
END;
$$;


ALTER FUNCTION public.calculate_parent_account_balance(p_account_id integer) OWNER TO postgres;

--
-- TOC entry 358 (class 1255 OID 16388)
-- Name: calculate_voucher_total(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.calculate_voucher_total(v_id integer) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
      DECLARE
        total_debit DECIMAL(15,2);
        total_credit DECIMAL(15,2);
        final_total DECIMAL(15,2);
      BEGIN
        SELECT 
          COALESCE(SUM(debit_amount), 0),
          COALESCE(SUM(credit_amount), 0)
        INTO total_debit, total_credit
        FROM financial_transactions
        WHERE voucher_id = v_id;
        
        final_total := GREATEST(total_debit, total_credit);
        
        UPDATE vouchers_master 
        SET total_amount = final_total,
            updated_date = CURRENT_TIMESTAMP
        WHERE id = v_id;
        
        RETURN final_total;
      END;
      $$;


ALTER FUNCTION public.calculate_voucher_total(v_id integer) OWNER TO postgres;

--
-- TOC entry 384 (class 1255 OID 18616)
-- Name: create_client_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_client_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    parent_account_id INTEGER;
    new_account_code VARCHAR(20);
    new_account_id INTEGER;
BEGIN
    -- فقط إذا كان العميل نشط
    IF NEW.status != 'active' THEN
        RETURN NEW;
    END IF;
    
    -- الحصول على معرف الحساب الرئيسي للعملاء
    SELECT id INTO parent_account_id 
    FROM chart_of_accounts 
    WHERE account_code = '1121' AND linked_table = 'clients';
    
    -- إنشاء رمز الحساب الجديد
    new_account_code := '1121' || LPAD(NEW.id::text, 3, '0');
    
    -- إنشاء الحساب في دليل الحسابات
    INSERT INTO chart_of_accounts (
        account_code, account_name, account_level, account_type, account_nature,
        parent_id, allow_transactions, is_active, created_date
    ) VALUES (
        new_account_code,
        NEW.name,
        5,
        'أصول',
        'مدين',
        parent_account_id,
        true,
        true,
        NOW()
    ) ON CONFLICT (account_code) DO UPDATE SET
        account_name = NEW.name,
        is_active = true
    RETURNING id INTO new_account_id;
    
    -- إذا لم يتم إنشاء حساب جديد، احصل على المعرف الموجود
    IF new_account_id IS NULL THEN
        SELECT id INTO new_account_id 
        FROM chart_of_accounts 
        WHERE account_code = new_account_code;
    END IF;
    
    -- تحديث العميل بمعرف الحساب
    NEW.account_id := new_account_id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.create_client_account() OWNER TO postgres;

--
-- TOC entry 380 (class 1255 OID 18617)
-- Name: create_employee_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_employee_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    parent_account_id INTEGER;
    new_account_code VARCHAR(20);
    new_account_id INTEGER;
BEGIN
    -- فقط إذا كان الموظف نشط
    IF NEW.status != 'active' THEN
        RETURN NEW;
    END IF;
    
    -- الحصول على معرف الحساب الرئيسي للموظفين
    SELECT id INTO parent_account_id 
    FROM chart_of_accounts 
    WHERE account_code = '1151' AND linked_table = 'employees';
    
    -- إنشاء رمز الحساب الجديد
    new_account_code := '1151' || LPAD(NEW.id::text, 3, '0');
    
    -- إنشاء الحساب في دليل الحسابات
    INSERT INTO chart_of_accounts (
        account_code, account_name, account_level, account_type, account_nature,
        parent_id, allow_transactions, is_active, created_date
    ) VALUES (
        new_account_code,
        NEW.name,
        5,
        'أصول',
        'مدين',
        parent_account_id,
        true,
        true,
        NOW()
    ) ON CONFLICT (account_code) DO UPDATE SET
        account_name = NEW.name,
        is_active = true
    RETURNING id INTO new_account_id;
    
    -- إذا لم يتم إنشاء حساب جديد، احصل على المعرف الموجود
    IF new_account_id IS NULL THEN
        SELECT id INTO new_account_id 
        FROM chart_of_accounts 
        WHERE account_code = new_account_code;
    END IF;
    
    -- تحديث الموظف بمعرف الحساب
    NEW.account_id := new_account_id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.create_employee_account() OWNER TO postgres;

--
-- TOC entry 374 (class 1255 OID 17785)
-- Name: create_sub_account(integer, character varying, integer, character varying); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_sub_account(p_main_account_id integer, p_table_name character varying, p_record_id integer, p_record_name character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
      DECLARE
        v_prefix VARCHAR;
        v_sub_code VARCHAR;
        v_sub_name VARCHAR;
      BEGIN
        SELECT sub_account_prefix INTO v_prefix 
        FROM chart_of_accounts 
        WHERE id = p_main_account_id;
        
        v_sub_code := (SELECT account_code FROM chart_of_accounts WHERE id = p_main_account_id) 
                     || '-' || LPAD(p_record_id::TEXT, 4, '0');
        v_sub_name := 'حساب ' || p_record_name;
        
        INSERT INTO account_sub_links 
        (main_account_id, linked_table, linked_record_id, sub_account_code, sub_account_name, created_by)
        VALUES (p_main_account_id, p_table_name, p_record_id, v_sub_code, v_sub_name, 'النظام')
        ON CONFLICT (main_account_id, linked_table, linked_record_id) DO NOTHING;
        
        RETURN v_sub_code;
      END;
      $$;


ALTER FUNCTION public.create_sub_account(p_main_account_id integer, p_table_name character varying, p_record_id integer, p_record_name character varying) OWNER TO postgres;

--
-- TOC entry 381 (class 1255 OID 18618)
-- Name: create_supplier_account(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.create_supplier_account() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    parent_account_id INTEGER;
    new_account_code VARCHAR(20);
BEGIN
    -- التحقق من وجود جدول الموردين
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'suppliers') THEN
        RETURN NEW;
    END IF;
    
    -- الحصول على معرف الحساب الرئيسي للموردين
    SELECT id INTO parent_account_id 
    FROM chart_of_accounts 
    WHERE account_code = '2111' AND linked_table = 'suppliers';
    
    -- إنشاء رمز الحساب الجديد
    new_account_code := '2111' || LPAD(NEW.id::text, 3, '0');
    
    -- إنشاء الحساب في دليل الحسابات
    INSERT INTO chart_of_accounts (
        account_code, account_name, account_level, account_type, account_nature,
        parent_id, allow_transactions, is_active, created_date
    ) VALUES (
        new_account_code,
        NEW.name,
        5,
        'خصوم',
        'دائن',
        parent_account_id,
        true,
        true,
        NOW()
    ) ON CONFLICT (account_code) DO UPDATE SET
        account_name = NEW.name,
        is_active = true;
    
    -- ربط المورد بالحساب الجديد
    UPDATE suppliers 
    SET account_id = (
        SELECT id FROM chart_of_accounts WHERE account_code = new_account_code
    )
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.create_supplier_account() OWNER TO postgres;

--
-- TOC entry 362 (class 1255 OID 16389)
-- Name: generate_account_code(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.generate_account_code(parent_account_id integer) RETURNS character varying
    LANGUAGE plpgsql
    AS $_$
      DECLARE
        parent_code VARCHAR(20);
        used_numbers INTEGER[];
        next_number INTEGER := 1;
        used_num INTEGER;
      BEGIN
        -- الحصول على رمز الحساب الأب
        SELECT account_code INTO parent_code
        FROM chart_of_accounts 
        WHERE id = parent_account_id;
        
        IF parent_code IS NULL THEN
          RAISE EXCEPTION 'الحساب الأب غير موجود';
        END IF;
        
        -- الحصول على الأرقام المستخدمة للحسابات الفرعية المباشرة
        SELECT ARRAY(
          SELECT CAST(SUBSTRING(account_code FROM LENGTH(parent_code) + 1) AS INTEGER)
          FROM chart_of_accounts 
          WHERE parent_id = parent_account_id
            AND account_code ~ ('^' || parent_code || '[0-9]+$')
            AND LENGTH(account_code) = LENGTH(parent_code) + 1
          ORDER BY CAST(SUBSTRING(account_code FROM LENGTH(parent_code) + 1) AS INTEGER)
        ) INTO used_numbers;
        
        -- العثور على أول رقم متاح
        FOREACH used_num IN ARRAY used_numbers
        LOOP
          IF next_number = used_num THEN
            next_number := next_number + 1;
          ELSE
            EXIT;
          END IF;
        END LOOP;
        
        RETURN parent_code || next_number;
      END;
      $_$;


ALTER FUNCTION public.generate_account_code(parent_account_id integer) OWNER TO postgres;

--
-- TOC entry 371 (class 1255 OID 16390)
-- Name: generate_voucher_number(character varying); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.generate_voucher_number(v_type character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
      DECLARE
        prefix VARCHAR(10);
        next_number INTEGER;
        current_year VARCHAR(4);
      BEGIN
        current_year := EXTRACT(YEAR FROM CURRENT_DATE)::VARCHAR;
        
        CASE v_type
          WHEN 'سند صرف' THEN prefix := 'PAY';
          WHEN 'سند قبض' THEN prefix := 'REC';
          WHEN 'قيد يومي' THEN prefix := 'JE';
          ELSE prefix := 'VOC';
        END CASE;
        
        SELECT COALESCE(MAX(
          CAST(SUBSTRING(voucher_number FROM LENGTH(prefix || current_year) + 1) AS INTEGER)
        ), 0) + 1
        INTO next_number
        FROM vouchers_master 
        WHERE voucher_type = v_type 
          AND voucher_number LIKE prefix || current_year || '%';
        
        RETURN prefix || current_year || LPAD(next_number::VARCHAR, 4, '0');
      END;
      $$;


ALTER FUNCTION public.generate_voucher_number(v_type character varying) OWNER TO postgres;

--
-- TOC entry 356 (class 1255 OID 17787)
-- Name: get_account_balance(character varying); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_account_balance(p_account_code character varying) RETURNS numeric
    LANGUAGE plpgsql
    AS $$
      BEGIN
        RETURN (SELECT COALESCE(current_balance, 0) FROM chart_of_accounts WHERE account_code = p_account_code);
      END;
      $$;


ALTER FUNCTION public.get_account_balance(p_account_code character varying) OWNER TO postgres;

--
-- TOC entry 385 (class 1255 OID 19070)
-- Name: get_user_combined_permissions(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.get_user_combined_permissions(user_id_param integer) RETURNS text[]
    LANGUAGE plpgsql
    AS $$
      DECLARE
        combined_permissions TEXT[];
      BEGIN
        -- جلب الصلاحيات المباشرة للمستخدم
        SELECT ARRAY(
          SELECT DISTINCT up.permission_key
          FROM user_permissions up
          WHERE up.user_id = user_id_param AND up.is_active = true
        ) INTO combined_permissions;
        
        -- إضافة صلاحيات الأدوار
        SELECT ARRAY(
          SELECT DISTINCT unnest(ur.permissions)
          FROM user_role_assignments ura
          JOIN user_roles ur ON ura.role_name = ur.role_name
          WHERE ura.user_id = user_id_param 
            AND ura.is_active = true 
            AND ur.is_active = true
        ) INTO combined_permissions;
        
        -- إرجاع الصلاحيات المجمعة بدون تكرار
        RETURN ARRAY(SELECT DISTINCT unnest(combined_permissions));
      END;
      $$;


ALTER FUNCTION public.get_user_combined_permissions(user_id_param integer) OWNER TO postgres;

--
-- TOC entry 372 (class 1255 OID 16391)
-- Name: recalculate_main_accounts(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.recalculate_main_accounts() RETURNS text
    LANGUAGE plpgsql
    AS $$
      DECLARE
        main_acc RECORD;
        sub_totals RECORD;
        updated_count INTEGER := 0;
      BEGIN
        FOR main_acc IN 
          SELECT id, account_code, account_name
          FROM chart_of_accounts 
          WHERE is_main_account = TRUE
        LOOP
          -- حساب مجموع الحسابات الفرعية
          WITH RECURSIVE sub_accounts AS (
            SELECT id, opening_balance, current_balance
            FROM chart_of_accounts 
            WHERE parent_id = main_acc.id
            
            UNION ALL
            
            SELECT c.id, c.opening_balance, c.current_balance
            FROM chart_of_accounts c
            INNER JOIN sub_accounts sa ON c.parent_id = sa.id
          )
          SELECT 
            COALESCE(SUM(opening_balance), 0) as total_opening,
            COALESCE(SUM(current_balance), 0) as total_current
          INTO sub_totals
          FROM sub_accounts;
          
          -- تحديث الحساب الرئيسي
          UPDATE chart_of_accounts 
          SET 
            opening_balance = sub_totals.total_opening,
            current_balance = sub_totals.total_current,
            updated_date = CURRENT_TIMESTAMP
          WHERE id = main_acc.id;
          
          updated_count := updated_count + 1;
        END LOOP;
        
        RETURN 'تم تحديث ' || updated_count || ' حساب رئيسي';
      END;
      $$;


ALTER FUNCTION public.recalculate_main_accounts() OWNER TO postgres;

--
-- TOC entry 382 (class 1255 OID 18622)
-- Name: update_client_account_name(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_client_account_name() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- تحديث اسم الحساب في دليل الحسابات
    UPDATE chart_of_accounts 
    SET account_name = NEW.name,
        updated_date = NOW()
    WHERE id = NEW.account_id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_client_account_name() OWNER TO postgres;

--
-- TOC entry 373 (class 1255 OID 16392)
-- Name: update_conversation_last_message(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_conversation_last_message() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
      BEGIN
        UPDATE conversations
        SET last_message_at = NEW.created_at,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.conversation_id;
        RETURN NEW;
      END;
      $$;


ALTER FUNCTION public.update_conversation_last_message() OWNER TO postgres;

--
-- TOC entry 383 (class 1255 OID 18623)
-- Name: update_employee_account_name(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_employee_account_name() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- تحديث اسم الحساب في دليل الحسابات
    UPDATE chart_of_accounts 
    SET account_name = NEW.name,
        updated_date = NOW()
    WHERE id = NEW.account_id;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_employee_account_name() OWNER TO postgres;

--
-- TOC entry 376 (class 1255 OID 18541)
-- Name: update_parent_account_balance(integer); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_parent_account_balance(p_account_id integer) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    UPDATE chart_of_accounts 
    SET current_balance = calculate_parent_account_balance(p_account_id),
        updated_date = CURRENT_TIMESTAMP
    WHERE id = p_account_id;
END;
$$;


ALTER FUNCTION public.update_parent_account_balance(p_account_id integer) OWNER TO postgres;

--
-- TOC entry 377 (class 1255 OID 18542)
-- Name: update_parent_balance_on_client_change(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_parent_balance_on_client_change() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- تحديث رصيد الحساب الأب للعميل
    IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'clients' AND linked_record_id = NEW.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'clients' AND linked_record_id = NEW.id LIMIT 1);
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'clients' AND linked_record_id = OLD.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'clients' AND linked_record_id = OLD.id LIMIT 1);
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION public.update_parent_balance_on_client_change() OWNER TO postgres;

--
-- TOC entry 378 (class 1255 OID 18544)
-- Name: update_parent_balance_on_employee_change(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_parent_balance_on_employee_change() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'employees' AND linked_record_id = NEW.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'employees' AND linked_record_id = NEW.id LIMIT 1);
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'employees' AND linked_record_id = OLD.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'employees' AND linked_record_id = OLD.id LIMIT 1);
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION public.update_parent_balance_on_employee_change() OWNER TO postgres;

--
-- TOC entry 379 (class 1255 OID 18546)
-- Name: update_parent_balance_on_supplier_change(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_parent_balance_on_supplier_change() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    IF TG_OP = 'UPDATE' OR TG_OP = 'INSERT' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'suppliers' AND linked_record_id = NEW.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'suppliers' AND linked_record_id = NEW.id LIMIT 1);
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        UPDATE chart_of_accounts 
        SET current_balance = calculate_parent_account_balance(
            (SELECT main_account_id FROM account_links 
             WHERE linked_table = 'suppliers' AND linked_record_id = OLD.id LIMIT 1)
        )
        WHERE id = (SELECT main_account_id FROM account_links 
                   WHERE linked_table = 'suppliers' AND linked_record_id = OLD.id LIMIT 1);
        RETURN OLD;
    END IF;
    
    RETURN NULL;
END;
$$;


ALTER FUNCTION public.update_parent_balance_on_supplier_change() OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 211 (class 1259 OID 16393)
-- Name: acc_trans; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.acc_trans (
    trans_id integer NOT NULL,
    chart_id integer NOT NULL,
    amount numeric(20,8) NOT NULL,
    transdate date NOT NULL,
    source text,
    cleared boolean DEFAULT false,
    fx_transaction boolean DEFAULT false,
    memo text,
    invoice_id integer,
    approved boolean DEFAULT false,
    entry_id integer NOT NULL,
    voucher_id integer
);


ALTER TABLE public.acc_trans OWNER TO postgres;

--
-- TOC entry 212 (class 1259 OID 16401)
-- Name: acc_trans_entry_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.acc_trans_entry_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.acc_trans_entry_id_seq OWNER TO postgres;

--
-- TOC entry 4768 (class 0 OID 0)
-- Dependencies: 212
-- Name: acc_trans_entry_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.acc_trans_entry_id_seq OWNED BY public.acc_trans.entry_id;


--
-- TOC entry 330 (class 1259 OID 18724)
-- Name: accounting_transaction_details; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounting_transaction_details (
    id integer NOT NULL,
    transaction_id integer,
    account_id integer NOT NULL,
    account_name character varying(255) NOT NULL,
    account_code character varying(50),
    debit_amount numeric(15,2) DEFAULT 0,
    credit_amount numeric(15,2) DEFAULT 0,
    description text,
    line_order integer DEFAULT 1,
    CONSTRAINT check_debit_or_credit CHECK ((((debit_amount > (0)::numeric) AND (credit_amount = (0)::numeric)) OR ((credit_amount > (0)::numeric) AND (debit_amount = (0)::numeric))))
);


ALTER TABLE public.accounting_transaction_details OWNER TO postgres;

--
-- TOC entry 329 (class 1259 OID 18723)
-- Name: accounting_transaction_details_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounting_transaction_details_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accounting_transaction_details_id_seq OWNER TO postgres;

--
-- TOC entry 4769 (class 0 OID 0)
-- Dependencies: 329
-- Name: accounting_transaction_details_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounting_transaction_details_id_seq OWNED BY public.accounting_transaction_details.id;


--
-- TOC entry 328 (class 1259 OID 18706)
-- Name: accounting_transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounting_transactions (
    id integer NOT NULL,
    transaction_number character varying(50) NOT NULL,
    transaction_type character varying(20) NOT NULL,
    transaction_date date NOT NULL,
    description text NOT NULL,
    party_name character varying(255),
    party_type character varying(50),
    reference_number character varying(100),
    total_amount numeric(15,2) DEFAULT 0 NOT NULL,
    status character varying(20) DEFAULT 'draft'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT accounting_transactions_status_check CHECK (((status)::text = ANY ((ARRAY['draft'::character varying, 'approved'::character varying, 'cancelled'::character varying])::text[]))),
    CONSTRAINT accounting_transactions_transaction_type_check CHECK (((transaction_type)::text = ANY ((ARRAY['receipt'::character varying, 'payment'::character varying, 'journal'::character varying])::text[])))
);


ALTER TABLE public.accounting_transactions OWNER TO postgres;

--
-- TOC entry 327 (class 1259 OID 18705)
-- Name: accounting_transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounting_transactions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accounting_transactions_id_seq OWNER TO postgres;

--
-- TOC entry 4770 (class 0 OID 0)
-- Dependencies: 327
-- Name: accounting_transactions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounting_transactions_id_seq OWNED BY public.accounting_transactions.id;


--
-- TOC entry 213 (class 1259 OID 16410)
-- Name: accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.accounts (
    id integer NOT NULL,
    account_code character varying(20) NOT NULL,
    account_name character varying(255) NOT NULL,
    account_type character varying(50) NOT NULL,
    parent_id integer,
    balance_type character varying(10) NOT NULL,
    opening_balance numeric(15,2) DEFAULT 0,
    current_balance numeric(15,2) DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    account_category character varying(20) DEFAULT 'فرعي'::character varying,
    account_level integer DEFAULT 1,
    allow_posting boolean DEFAULT true,
    description text,
    CONSTRAINT accounts_account_category_check CHECK (((account_category)::text = ANY (ARRAY[('رئيسي'::character varying)::text, ('فرعي'::character varying)::text]))),
    CONSTRAINT accounts_balance_type_check CHECK (((balance_type)::text = ANY (ARRAY[('مدين'::character varying)::text, ('دائن'::character varying)::text])))
);


ALTER TABLE public.accounts OWNER TO postgres;

--
-- TOC entry 214 (class 1259 OID 16424)
-- Name: accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.accounts_id_seq OWNER TO postgres;

--
-- TOC entry 4771 (class 0 OID 0)
-- Dependencies: 214
-- Name: accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.accounts_id_seq OWNED BY public.accounts.id;


--
-- TOC entry 320 (class 1259 OID 18393)
-- Name: ai_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ai_settings (
    id integer DEFAULT 1 NOT NULL,
    enabled boolean DEFAULT true,
    model character varying(100) DEFAULT 'codegeex2'::character varying,
    delay_seconds integer DEFAULT 2,
    working_hours_only boolean DEFAULT false,
    working_hours_start time without time zone DEFAULT '00:00:00'::time without time zone,
    working_hours_end time without time zone DEFAULT '23:59:00'::time without time zone,
    working_days text[] DEFAULT ARRAY['الأحد'::text, 'الاثنين'::text, 'الثلاثاء'::text, 'الأربعاء'::text, 'الخميس'::text, 'الجمعة'::text, 'السبت'::text],
    max_responses_per_conversation integer DEFAULT 10,
    keywords_trigger text[] DEFAULT ARRAY['مساعدة'::text, 'استفسار'::text, 'سؤال'::text, 'معلومات'::text, 'خدمة'::text, 'مرحبا'::text, 'السلام'::text, 'أهلا'::text],
    excluded_keywords text[] DEFAULT ARRAY['عاجل'::text, 'طارئ'::text, 'مهم جداً'::text],
    auto_responses jsonb DEFAULT '{"help": "يمكنني مساعدتك في:\\n• الاستفسارات القانونية العامة\\n• معلومات عن خدمات المكتب\\n• توجيهك للمحامي المناسب", "greeting": "مرحباً! أنا المساعد الذكي للمكتب.", "signature": "🤖 المساعد الذكي للمكتب", "disclaimer": "للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا."}'::jsonb,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT ai_settings_single_row CHECK ((id = 1))
);


ALTER TABLE public.ai_settings OWNER TO postgres;

--
-- TOC entry 4772 (class 0 OID 0)
-- Dependencies: 320
-- Name: TABLE ai_settings; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.ai_settings IS 'إعدادات نظام الذكاء الاصطناعي والرد التلقائي';


--
-- TOC entry 4773 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN ai_settings.enabled; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.enabled IS 'تفعيل أو إيقاف النظام';


--
-- TOC entry 4774 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN ai_settings.model; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.model IS 'النموذج المستخدم للذكاء الاصطناعي';


--
-- TOC entry 4775 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN ai_settings.delay_seconds; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.delay_seconds IS 'تأخير الرد بالثواني';


--
-- TOC entry 4776 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN ai_settings.working_hours_only; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.working_hours_only IS 'العمل في ساعات محددة فقط';


--
-- TOC entry 4777 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN ai_settings.working_days; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.working_days IS 'أيام العمل';


--
-- TOC entry 4778 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN ai_settings.keywords_trigger; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.keywords_trigger IS 'الكلمات المحفزة للرد';


--
-- TOC entry 4779 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN ai_settings.excluded_keywords; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.excluded_keywords IS 'الكلمات المستبعدة';


--
-- TOC entry 4780 (class 0 OID 0)
-- Dependencies: 320
-- Name: COLUMN ai_settings.auto_responses; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_settings.auto_responses IS 'الردود التلقائية المخصصة';


--
-- TOC entry 215 (class 1259 OID 16425)
-- Name: announcements; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.announcements (
    id integer NOT NULL,
    announcement_1 text,
    announcement_2 text,
    announcement_3 text,
    announcement_4 text,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.announcements OWNER TO postgres;

--
-- TOC entry 216 (class 1259 OID 16433)
-- Name: announcements_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.announcements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.announcements_id_seq OWNER TO postgres;

--
-- TOC entry 4781 (class 0 OID 0)
-- Dependencies: 216
-- Name: announcements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.announcements_id_seq OWNED BY public.announcements.id;


--
-- TOC entry 217 (class 1259 OID 16434)
-- Name: ar; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ar (
    id integer NOT NULL,
    invnumber text,
    transdate date NOT NULL,
    entity_id integer,
    taxincluded boolean DEFAULT false,
    amount numeric(20,8) NOT NULL,
    netamount numeric(20,8) NOT NULL,
    paid numeric(20,8) DEFAULT 0,
    datepaid date,
    duedate date,
    invoice boolean DEFAULT true,
    shippingpoint text,
    terms text,
    notes text,
    curr character varying(3) DEFAULT 'SAR'::character varying,
    ordnumber text,
    employee_id integer,
    till character varying(20),
    quonumber text,
    intnotes text,
    department_id integer,
    shipvia text,
    language_code character varying(6),
    ponumber text,
    on_hold boolean DEFAULT false,
    reverse boolean DEFAULT false,
    approved boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    workflow_id integer,
    crdate date DEFAULT CURRENT_DATE
);


ALTER TABLE public.ar OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 16448)
-- Name: ar_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ar_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ar_id_seq OWNER TO postgres;

--
-- TOC entry 4782 (class 0 OID 0)
-- Dependencies: 218
-- Name: ar_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ar_id_seq OWNED BY public.ar.id;


--
-- TOC entry 219 (class 1259 OID 16449)
-- Name: branches; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.branches (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    governorate_id integer,
    address text,
    phone character varying(50),
    manager_name character varying(255),
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.branches OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 16456)
-- Name: branches_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.branches_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.branches_id_seq OWNER TO postgres;

--
-- TOC entry 4783 (class 0 OID 0)
-- Dependencies: 220
-- Name: branches_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.branches_id_seq OWNED BY public.branches.id;


--
-- TOC entry 221 (class 1259 OID 16457)
-- Name: budget; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.budget (
    id integer NOT NULL,
    budget_year integer NOT NULL,
    account_id integer NOT NULL,
    budgeted_amount numeric(15,2) NOT NULL,
    actual_amount numeric(15,2) DEFAULT 0,
    variance_amount numeric(15,2) DEFAULT 0,
    variance_percentage numeric(5,2) DEFAULT 0,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.budget OWNER TO postgres;

--
-- TOC entry 222 (class 1259 OID 16467)
-- Name: budget_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.budget_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.budget_id_seq OWNER TO postgres;

--
-- TOC entry 4784 (class 0 OID 0)
-- Dependencies: 222
-- Name: budget_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.budget_id_seq OWNED BY public.budget.id;


--
-- TOC entry 223 (class 1259 OID 16468)
-- Name: case_distribution; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_distribution (
    id integer NOT NULL,
    issue_id integer,
    lineage_id integer,
    admin_amount numeric(15,2) DEFAULT 0,
    remaining_amount numeric(15,2) DEFAULT 0,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.case_distribution OWNER TO postgres;

--
-- TOC entry 224 (class 1259 OID 16474)
-- Name: case_distribution_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.case_distribution_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.case_distribution_id_seq OWNER TO postgres;

--
-- TOC entry 4785 (class 0 OID 0)
-- Dependencies: 224
-- Name: case_distribution_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.case_distribution_id_seq OWNED BY public.case_distribution.id;


--
-- TOC entry 225 (class 1259 OID 16475)
-- Name: chart_of_accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.chart_of_accounts (
    id integer NOT NULL,
    account_code character varying(20) NOT NULL,
    account_name character varying(255) NOT NULL,
    account_name_en character varying(255),
    level_1_code character varying(2),
    level_2_code character varying(4),
    level_3_code character varying(6),
    level_4_code character varying(8),
    account_level integer NOT NULL,
    parent_id integer,
    account_type character varying(50) NOT NULL,
    account_nature character varying(20) DEFAULT 'مدين'::character varying,
    is_active boolean DEFAULT true,
    allow_transactions boolean DEFAULT false,
    linked_table character varying(100),
    auto_create_sub_accounts boolean DEFAULT false,
    opening_balance numeric(15,2) DEFAULT 0,
    current_balance numeric(15,2) DEFAULT 0,
    description text,
    notes text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_linked_record boolean DEFAULT false,
    original_table character varying(50),
    linked_record_id integer,
    linked_entity_type character varying(50),
    linked_entity_id integer,
    CONSTRAINT chart_of_accounts_account_level_check CHECK (((account_level >= 1) AND (account_level <= 5)))
);


ALTER TABLE public.chart_of_accounts OWNER TO postgres;

--
-- TOC entry 226 (class 1259 OID 16490)
-- Name: chart_of_accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.chart_of_accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.chart_of_accounts_id_seq OWNER TO postgres;

--
-- TOC entry 4786 (class 0 OID 0)
-- Dependencies: 226
-- Name: chart_of_accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.chart_of_accounts_id_seq OWNED BY public.chart_of_accounts.id;


--
-- TOC entry 311 (class 1259 OID 17629)
-- Name: client_notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.client_notifications (
    id integer NOT NULL,
    client_id integer NOT NULL,
    title character varying(255) NOT NULL,
    message text NOT NULL,
    type character varying(50) DEFAULT 'info'::character varying,
    case_id integer,
    document_id integer,
    is_read boolean DEFAULT false,
    read_at timestamp without time zone,
    sent_via character varying(50) DEFAULT 'portal'::character varying,
    sent_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.client_notifications OWNER TO postgres;

--
-- TOC entry 310 (class 1259 OID 17628)
-- Name: client_notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.client_notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.client_notifications_id_seq OWNER TO postgres;

--
-- TOC entry 4787 (class 0 OID 0)
-- Dependencies: 310
-- Name: client_notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.client_notifications_id_seq OWNED BY public.client_notifications.id;


--
-- TOC entry 309 (class 1259 OID 17601)
-- Name: client_portal_accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.client_portal_accounts (
    id integer NOT NULL,
    client_id integer NOT NULL,
    username character varying(100) NOT NULL,
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    is_active boolean DEFAULT true,
    is_verified boolean DEFAULT false,
    verification_token character varying(255),
    reset_token character varying(255),
    reset_token_expires timestamp without time zone,
    language character varying(10) DEFAULT 'ar'::character varying,
    timezone character varying(50) DEFAULT 'Asia/Riyadh'::character varying,
    notification_preferences jsonb DEFAULT '{}'::jsonb,
    last_login timestamp without time zone,
    login_attempts integer DEFAULT 0,
    locked_until timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.client_portal_accounts OWNER TO postgres;

--
-- TOC entry 308 (class 1259 OID 17600)
-- Name: client_portal_accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.client_portal_accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.client_portal_accounts_id_seq OWNER TO postgres;

--
-- TOC entry 4788 (class 0 OID 0)
-- Dependencies: 308
-- Name: client_portal_accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.client_portal_accounts_id_seq OWNED BY public.client_portal_accounts.id;


--
-- TOC entry 313 (class 1259 OID 17658)
-- Name: client_requests; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.client_requests (
    id integer NOT NULL,
    client_id integer NOT NULL,
    case_id integer,
    request_type character varying(100) NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    priority character varying(20) DEFAULT 'medium'::character varying,
    status character varying(50) DEFAULT 'pending'::character varying,
    assigned_to integer,
    response text,
    due_date date,
    completed_date date,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.client_requests OWNER TO postgres;

--
-- TOC entry 312 (class 1259 OID 17657)
-- Name: client_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.client_requests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.client_requests_id_seq OWNER TO postgres;

--
-- TOC entry 4789 (class 0 OID 0)
-- Dependencies: 312
-- Name: client_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.client_requests_id_seq OWNED BY public.client_requests.id;


--
-- TOC entry 315 (class 1259 OID 17686)
-- Name: client_sessions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.client_sessions (
    id integer NOT NULL,
    client_id integer NOT NULL,
    session_token character varying(255) NOT NULL,
    ip_address inet,
    user_agent text,
    expires_at timestamp without time zone NOT NULL,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.client_sessions OWNER TO postgres;

--
-- TOC entry 314 (class 1259 OID 17685)
-- Name: client_sessions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.client_sessions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.client_sessions_id_seq OWNER TO postgres;

--
-- TOC entry 4790 (class 0 OID 0)
-- Dependencies: 314
-- Name: client_sessions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.client_sessions_id_seq OWNED BY public.client_sessions.id;


--
-- TOC entry 227 (class 1259 OID 16491)
-- Name: clients; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.clients (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    phone character varying(20),
    email character varying(255),
    address text,
    id_number character varying(20),
    status character varying(20) DEFAULT 'active'::character varying,
    cases_count integer DEFAULT 0,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    client_type character varying(100),
    username character varying(50),
    password_hash character varying(255),
    last_login timestamp without time zone,
    is_online boolean DEFAULT false,
    login_attempts integer DEFAULT 0,
    locked_until timestamp without time zone,
    account_id integer,
    current_balance numeric(15,2) DEFAULT 0
);


ALTER TABLE public.clients OWNER TO postgres;

--
-- TOC entry 228 (class 1259 OID 16502)
-- Name: clients_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.clients_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.clients_id_seq OWNER TO postgres;

--
-- TOC entry 4791 (class 0 OID 0)
-- Dependencies: 228
-- Name: clients_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.clients_id_seq OWNED BY public.clients.id;


--
-- TOC entry 340 (class 1259 OID 18897)
-- Name: companies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.companies (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    address text,
    phone character varying(50),
    email character varying(255),
    website character varying(255),
    established_date date,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    legal_name character varying(255),
    registration_number character varying(100),
    city character varying(100),
    country character varying(100),
    logo_right_text text,
    logo_left_text text,
    logo_url text,
    legal_form character varying(100),
    capital numeric(15,2) DEFAULT 0,
    description text,
    is_active boolean DEFAULT true,
    tax_number character varying(100),
    logo_image_url text,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    latitude numeric(10,8),
    longitude numeric(11,8),
    map_zoom integer DEFAULT 15,
    working_hours text
);


ALTER TABLE public.companies OWNER TO postgres;

--
-- TOC entry 4792 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN companies.latitude; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.companies.latitude IS 'خط العرض للموقع الجغرافي للشركة';


--
-- TOC entry 4793 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN companies.longitude; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.companies.longitude IS 'خط الطول للموقع الجغرافي للشركة';


--
-- TOC entry 4794 (class 0 OID 0)
-- Dependencies: 340
-- Name: COLUMN companies.working_hours; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.companies.working_hours IS 'ساعات العمل للشركة';


--
-- TOC entry 339 (class 1259 OID 18896)
-- Name: companies_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.companies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.companies_id_seq OWNER TO postgres;

--
-- TOC entry 4795 (class 0 OID 0)
-- Dependencies: 339
-- Name: companies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.companies_id_seq OWNED BY public.companies.id;


--
-- TOC entry 229 (class 1259 OID 16512)
-- Name: company; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.company (
    id integer NOT NULL,
    entity_id integer,
    legal_name text NOT NULL,
    tax_id character varying(20),
    sales_tax_id character varying(20),
    license_number character varying(50),
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    control_code character varying(20),
    country_id integer,
    sic_code character varying(20)
);


ALTER TABLE public.company OWNER TO postgres;

--
-- TOC entry 230 (class 1259 OID 16518)
-- Name: company_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.company_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_id_seq OWNER TO postgres;

--
-- TOC entry 4796 (class 0 OID 0)
-- Dependencies: 230
-- Name: company_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.company_id_seq OWNED BY public.company.id;


--
-- TOC entry 231 (class 1259 OID 16519)
-- Name: conversations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.conversations (
    id integer NOT NULL,
    client_id integer,
    user_id integer,
    title character varying(255),
    status character varying(20) DEFAULT 'active'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    last_message_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.conversations OWNER TO postgres;

--
-- TOC entry 232 (class 1259 OID 16526)
-- Name: conversations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.conversations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.conversations_id_seq OWNER TO postgres;

--
-- TOC entry 4797 (class 0 OID 0)
-- Dependencies: 232
-- Name: conversations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.conversations_id_seq OWNED BY public.conversations.id;


--
-- TOC entry 233 (class 1259 OID 16527)
-- Name: cost_centers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cost_centers (
    id integer NOT NULL,
    center_code character varying(20) NOT NULL,
    center_name character varying(255) NOT NULL,
    parent_id integer,
    center_level integer DEFAULT 1,
    is_active boolean DEFAULT true,
    description text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.cost_centers OWNER TO postgres;

--
-- TOC entry 234 (class 1259 OID 16536)
-- Name: cost_centers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cost_centers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cost_centers_id_seq OWNER TO postgres;

--
-- TOC entry 4798 (class 0 OID 0)
-- Dependencies: 234
-- Name: cost_centers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cost_centers_id_seq OWNED BY public.cost_centers.id;


--
-- TOC entry 235 (class 1259 OID 16537)
-- Name: courts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.courts (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    type character varying(100),
    governorate_id integer,
    address text,
    phone character varying(50),
    employee_id integer,
    issue_id integer,
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.courts OWNER TO postgres;

--
-- TOC entry 236 (class 1259 OID 16544)
-- Name: courts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.courts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.courts_id_seq OWNER TO postgres;

--
-- TOC entry 4799 (class 0 OID 0)
-- Dependencies: 236
-- Name: courts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.courts_id_seq OWNED BY public.courts.id;


--
-- TOC entry 237 (class 1259 OID 16545)
-- Name: currencies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.currencies (
    id integer NOT NULL,
    currency_code character varying(10) NOT NULL,
    currency_name character varying(100) NOT NULL,
    symbol character varying(10),
    is_active boolean DEFAULT true,
    exchange_rate numeric(10,4) DEFAULT 1.0000,
    is_base_currency boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.currencies OWNER TO postgres;

--
-- TOC entry 238 (class 1259 OID 16553)
-- Name: currencies_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.currencies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.currencies_id_seq OWNER TO postgres;

--
-- TOC entry 4800 (class 0 OID 0)
-- Dependencies: 238
-- Name: currencies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.currencies_id_seq OWNED BY public.currencies.id;


--
-- TOC entry 301 (class 1259 OID 17483)
-- Name: document_shares; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.document_shares (
    id integer NOT NULL,
    document_id integer,
    shared_with_user integer,
    shared_with_client integer,
    permission_level character varying(50) DEFAULT 'read'::character varying,
    shared_by integer,
    expires_at timestamp without time zone,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.document_shares OWNER TO postgres;

--
-- TOC entry 300 (class 1259 OID 17482)
-- Name: document_shares_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.document_shares_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.document_shares_id_seq OWNER TO postgres;

--
-- TOC entry 4801 (class 0 OID 0)
-- Dependencies: 300
-- Name: document_shares_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.document_shares_id_seq OWNED BY public.document_shares.id;


--
-- TOC entry 299 (class 1259 OID 17463)
-- Name: document_versions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.document_versions (
    id integer NOT NULL,
    document_id integer,
    version_number integer NOT NULL,
    file_name character varying(255) NOT NULL,
    file_path character varying(500) NOT NULL,
    file_size bigint,
    changes_description text,
    uploaded_by integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.document_versions OWNER TO postgres;

--
-- TOC entry 298 (class 1259 OID 17462)
-- Name: document_versions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.document_versions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.document_versions_id_seq OWNER TO postgres;

--
-- TOC entry 4802 (class 0 OID 0)
-- Dependencies: 298
-- Name: document_versions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.document_versions_id_seq OWNED BY public.document_versions.id;


--
-- TOC entry 297 (class 1259 OID 17428)
-- Name: documents; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.documents (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    file_name character varying(255) NOT NULL,
    file_path character varying(500) NOT NULL,
    file_size bigint,
    file_type character varying(100),
    mime_type character varying(100),
    case_id integer,
    client_id integer,
    employee_id integer,
    category character varying(100),
    subcategory character varying(100),
    tags text[],
    content_text text,
    keywords text[],
    access_level character varying(50) DEFAULT 'private'::character varying,
    is_confidential boolean DEFAULT false,
    uploaded_by integer,
    version_number integer DEFAULT 1,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.documents OWNER TO postgres;

--
-- TOC entry 296 (class 1259 OID 17427)
-- Name: documents_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.documents_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.documents_id_seq OWNER TO postgres;

--
-- TOC entry 4803 (class 0 OID 0)
-- Dependencies: 296
-- Name: documents_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.documents_id_seq OWNED BY public.documents.id;


--
-- TOC entry 239 (class 1259 OID 16554)
-- Name: employees; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.employees (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    "position" character varying(255),
    department character varying(255),
    phone character varying(20),
    email character varying(255),
    address text,
    id_number character varying(20),
    salary numeric(10,2),
    hire_date date,
    status character varying(20) DEFAULT 'active'::character varying,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    department_id integer,
    branch_id integer DEFAULT 1,
    governorate_id integer DEFAULT 1,
    employee_number character varying(50),
    account_id integer,
    current_balance numeric(15,2) DEFAULT 0
);


ALTER TABLE public.employees OWNER TO postgres;

--
-- TOC entry 240 (class 1259 OID 16564)
-- Name: employees_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.employees_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.employees_id_seq OWNER TO postgres;

--
-- TOC entry 4804 (class 0 OID 0)
-- Dependencies: 240
-- Name: employees_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.employees_id_seq OWNED BY public.employees.id;


--
-- TOC entry 241 (class 1259 OID 16565)
-- Name: entity; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.entity (
    id integer NOT NULL,
    name text NOT NULL,
    entity_class integer NOT NULL,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    control_code character varying(20),
    country_id integer
);


ALTER TABLE public.entity OWNER TO postgres;

--
-- TOC entry 242 (class 1259 OID 16571)
-- Name: entity_class; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.entity_class (
    id integer NOT NULL,
    class character varying(20) NOT NULL,
    in_use boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.entity_class OWNER TO postgres;

--
-- TOC entry 243 (class 1259 OID 16576)
-- Name: entity_class_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.entity_class_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.entity_class_id_seq OWNER TO postgres;

--
-- TOC entry 4805 (class 0 OID 0)
-- Dependencies: 243
-- Name: entity_class_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.entity_class_id_seq OWNED BY public.entity_class.id;


--
-- TOC entry 244 (class 1259 OID 16577)
-- Name: entity_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.entity_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.entity_id_seq OWNER TO postgres;

--
-- TOC entry 4806 (class 0 OID 0)
-- Dependencies: 244
-- Name: entity_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.entity_id_seq OWNED BY public.entity.id;


--
-- TOC entry 245 (class 1259 OID 16578)
-- Name: follows; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.follows (
    id integer NOT NULL,
    case_id integer,
    case_number character varying(50),
    case_title character varying(255),
    client_name character varying(255),
    service_type character varying(50),
    description text,
    date_field date,
    status character varying(50) DEFAULT 'pending'::character varying,
    priority character varying(20) DEFAULT 'medium'::character varying,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    service_id integer,
    user_id integer,
    next_hearing_date date,
    earned_amount numeric(15,2) DEFAULT 0,
    is_approved boolean DEFAULT false,
    approved_by integer,
    approved_date date,
    next_hearing_id integer,
    report text,
    next_hearing_time time without time zone,
    court_name character varying(255),
    hearing_type character varying(100)
);


ALTER TABLE public.follows OWNER TO postgres;

--
-- TOC entry 246 (class 1259 OID 16587)
-- Name: follows_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.follows_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.follows_id_seq OWNER TO postgres;

--
-- TOC entry 4807 (class 0 OID 0)
-- Dependencies: 246
-- Name: follows_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.follows_id_seq OWNED BY public.follows.id;


--
-- TOC entry 348 (class 1259 OID 18995)
-- Name: footer_links; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.footer_links (
    id integer NOT NULL,
    category character varying(100) NOT NULL,
    name character varying(200) NOT NULL,
    href character varying(500) NOT NULL,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.footer_links OWNER TO postgres;

--
-- TOC entry 347 (class 1259 OID 18994)
-- Name: footer_links_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.footer_links_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.footer_links_id_seq OWNER TO postgres;

--
-- TOC entry 4808 (class 0 OID 0)
-- Dependencies: 347
-- Name: footer_links_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.footer_links_id_seq OWNED BY public.footer_links.id;


--
-- TOC entry 247 (class 1259 OID 16588)
-- Name: gl; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.gl (
    id integer NOT NULL,
    reference text,
    description text,
    transdate date NOT NULL,
    person_id integer,
    notes text,
    approved boolean DEFAULT false,
    approved_by integer,
    approved_at timestamp without time zone,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    workflow_id integer
);


ALTER TABLE public.gl OWNER TO postgres;

--
-- TOC entry 248 (class 1259 OID 16595)
-- Name: gl_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.gl_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.gl_id_seq OWNER TO postgres;

--
-- TOC entry 4809 (class 0 OID 0)
-- Dependencies: 248
-- Name: gl_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.gl_id_seq OWNED BY public.gl.id;


--
-- TOC entry 249 (class 1259 OID 16596)
-- Name: governorates; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.governorates (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    code character varying(10) NOT NULL,
    region character varying(100),
    population integer,
    is_capital boolean DEFAULT false,
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.governorates OWNER TO postgres;

--
-- TOC entry 250 (class 1259 OID 16602)
-- Name: governorates_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.governorates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.governorates_id_seq OWNER TO postgres;

--
-- TOC entry 4810 (class 0 OID 0)
-- Dependencies: 250
-- Name: governorates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.governorates_id_seq OWNED BY public.governorates.id;


--
-- TOC entry 293 (class 1259 OID 17356)
-- Name: hearings; Type: TABLE; Schema: public; Owner: yemen
--

CREATE TABLE public.hearings (
    id integer NOT NULL,
    issue_id integer,
    hearing_date date NOT NULL,
    hearing_time time without time zone,
    court_name character varying(255),
    hearing_type character varying(100),
    notes text,
    status character varying(50) DEFAULT 'scheduled'::character varying,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.hearings OWNER TO yemen;

--
-- TOC entry 292 (class 1259 OID 17355)
-- Name: hearings_id_seq; Type: SEQUENCE; Schema: public; Owner: yemen
--

CREATE SEQUENCE public.hearings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.hearings_id_seq OWNER TO yemen;

--
-- TOC entry 4811 (class 0 OID 0)
-- Dependencies: 292
-- Name: hearings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: yemen
--

ALTER SEQUENCE public.hearings_id_seq OWNED BY public.hearings.id;


--
-- TOC entry 251 (class 1259 OID 16603)
-- Name: invoice; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.invoice (
    id integer NOT NULL,
    trans_id integer,
    parts_id integer,
    description text,
    qty numeric(20,8),
    allocated numeric(20,8) DEFAULT 0,
    sellprice numeric(20,8),
    fxsellprice numeric(20,8),
    discount numeric(4,4) DEFAULT 0,
    assemblyitem boolean DEFAULT false,
    unit character varying(20),
    deliverydate date,
    serialnumber text,
    notes text
);


ALTER TABLE public.invoice OWNER TO postgres;

--
-- TOC entry 252 (class 1259 OID 16611)
-- Name: invoice_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.invoice_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.invoice_id_seq OWNER TO postgres;

--
-- TOC entry 4812 (class 0 OID 0)
-- Dependencies: 252
-- Name: invoice_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.invoice_id_seq OWNED BY public.invoice.id;


--
-- TOC entry 307 (class 1259 OID 17574)
-- Name: invoice_items; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.invoice_items (
    id integer NOT NULL,
    invoice_id integer,
    description text NOT NULL,
    quantity numeric(10,2) DEFAULT 1,
    unit_price numeric(10,2) NOT NULL,
    total_price numeric(12,2) NOT NULL,
    time_entry_id integer,
    case_id integer,
    item_type character varying(50) DEFAULT 'service'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.invoice_items OWNER TO postgres;

--
-- TOC entry 306 (class 1259 OID 17573)
-- Name: invoice_items_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.invoice_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.invoice_items_id_seq OWNER TO postgres;

--
-- TOC entry 4813 (class 0 OID 0)
-- Dependencies: 306
-- Name: invoice_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.invoice_items_id_seq OWNED BY public.invoice_items.id;


--
-- TOC entry 305 (class 1259 OID 17542)
-- Name: invoices; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.invoices (
    id integer NOT NULL,
    invoice_number character varying(50) NOT NULL,
    client_id integer NOT NULL,
    client_name character varying(255),
    client_address text,
    invoice_date date DEFAULT CURRENT_DATE,
    due_date date,
    subtotal numeric(12,2) DEFAULT 0,
    tax_rate numeric(5,2) DEFAULT 0,
    tax_amount numeric(12,2) DEFAULT 0,
    discount_amount numeric(12,2) DEFAULT 0,
    total_amount numeric(12,2) DEFAULT 0,
    status character varying(50) DEFAULT 'draft'::character varying,
    payment_status character varying(50) DEFAULT 'unpaid'::character varying,
    paid_amount numeric(12,2) DEFAULT 0,
    payment_date date,
    notes text,
    terms_conditions text,
    created_by integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.invoices OWNER TO postgres;

--
-- TOC entry 304 (class 1259 OID 17541)
-- Name: invoices_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.invoices_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.invoices_id_seq OWNER TO postgres;

--
-- TOC entry 4814 (class 0 OID 0)
-- Dependencies: 304
-- Name: invoices_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.invoices_id_seq OWNED BY public.invoices.id;


--
-- TOC entry 253 (class 1259 OID 16612)
-- Name: issue_types; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.issue_types (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    description text,
    color character varying(50),
    cases_count integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.issue_types OWNER TO postgres;

--
-- TOC entry 254 (class 1259 OID 16621)
-- Name: issue_types_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.issue_types_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.issue_types_id_seq OWNER TO postgres;

--
-- TOC entry 4815 (class 0 OID 0)
-- Dependencies: 254
-- Name: issue_types_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.issue_types_id_seq OWNED BY public.issue_types.id;


--
-- TOC entry 255 (class 1259 OID 16622)
-- Name: issues; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.issues (
    id integer NOT NULL,
    case_number character varying(50) NOT NULL,
    title character varying(255) NOT NULL,
    description text,
    client_id integer,
    issue_type_id integer,
    status character varying(50) DEFAULT 'pending'::character varying,
    amount numeric(12,2),
    next_hearing date,
    notes text,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    court_id integer,
    case_amount numeric(15,2),
    start_date date,
    end_date date,
    contract_method character varying(20) DEFAULT 'بالجلسة'::character varying,
    contract_date date DEFAULT CURRENT_DATE,
    client_name character varying(255) DEFAULT ''::character varying,
    court_name character varying(255) DEFAULT NULL::character varying,
    issue_type character varying(100) DEFAULT NULL::character varying,
    next_hearing_date date,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT check_contract_method CHECK (((contract_method)::text = ANY (ARRAY[('بالجلسة'::character varying)::text, ('بالعقد'::character varying)::text])))
);


ALTER TABLE public.issues OWNER TO postgres;

--
-- TOC entry 256 (class 1259 OID 16637)
-- Name: issues_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.issues_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.issues_id_seq OWNER TO postgres;

--
-- TOC entry 4816 (class 0 OID 0)
-- Dependencies: 256
-- Name: issues_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.issues_id_seq OWNED BY public.issues.id;


--
-- TOC entry 324 (class 1259 OID 18666)
-- Name: journal_entries; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.journal_entries (
    id integer NOT NULL,
    entry_number character varying(50) NOT NULL,
    entry_date date NOT NULL,
    description text NOT NULL,
    total_debit numeric(15,2) DEFAULT 0 NOT NULL,
    total_credit numeric(15,2) DEFAULT 0 NOT NULL,
    status character varying(20) DEFAULT 'draft'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    entry_type character varying(20) DEFAULT 'journal'::character varying,
    party_name character varying(255),
    party_type character varying(50),
    reference_number character varying(100),
    CONSTRAINT journal_entries_entry_type_check CHECK (((entry_type)::text = ANY ((ARRAY['receipt'::character varying, 'payment'::character varying, 'journal'::character varying])::text[])))
);


ALTER TABLE public.journal_entries OWNER TO postgres;

--
-- TOC entry 323 (class 1259 OID 18665)
-- Name: journal_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.journal_entries_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.journal_entries_id_seq OWNER TO postgres;

--
-- TOC entry 4817 (class 0 OID 0)
-- Dependencies: 323
-- Name: journal_entries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.journal_entries_id_seq OWNED BY public.journal_entries.id;


--
-- TOC entry 326 (class 1259 OID 18683)
-- Name: journal_entry_details; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.journal_entry_details (
    id integer NOT NULL,
    journal_entry_id integer,
    account_id integer,
    account_name character varying(255),
    debit_amount numeric(15,2) DEFAULT 0,
    credit_amount numeric(15,2) DEFAULT 0,
    description text,
    line_order integer DEFAULT 1,
    account_code character varying(50)
);


ALTER TABLE public.journal_entry_details OWNER TO postgres;

--
-- TOC entry 325 (class 1259 OID 18682)
-- Name: journal_entry_details_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.journal_entry_details_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.journal_entry_details_id_seq OWNER TO postgres;

--
-- TOC entry 4818 (class 0 OID 0)
-- Dependencies: 325
-- Name: journal_entry_details_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.journal_entry_details_id_seq OWNED BY public.journal_entry_details.id;


--
-- TOC entry 295 (class 1259 OID 17393)
-- Name: lawyer_earnings; Type: TABLE; Schema: public; Owner: yemen
--

CREATE TABLE public.lawyer_earnings (
    id integer NOT NULL,
    lawyer_id integer NOT NULL,
    case_id integer,
    service_id integer,
    follow_id integer,
    allocated_amount numeric(15,2) DEFAULT 0,
    earned_amount numeric(15,2) DEFAULT 0,
    earning_date date DEFAULT CURRENT_DATE,
    notes text,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.lawyer_earnings OWNER TO yemen;

--
-- TOC entry 294 (class 1259 OID 17392)
-- Name: lawyer_earnings_id_seq; Type: SEQUENCE; Schema: public; Owner: yemen
--

CREATE SEQUENCE public.lawyer_earnings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.lawyer_earnings_id_seq OWNER TO yemen;

--
-- TOC entry 4819 (class 0 OID 0)
-- Dependencies: 294
-- Name: lawyer_earnings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: yemen
--

ALTER SEQUENCE public.lawyer_earnings_id_seq OWNED BY public.lawyer_earnings.id;


--
-- TOC entry 257 (class 1259 OID 16665)
-- Name: lineages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.lineages (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    created_date date DEFAULT CURRENT_DATE,
    admin_percentage numeric(5,2) DEFAULT 0,
    commission_percentage numeric(5,2) DEFAULT 0
);


ALTER TABLE public.lineages OWNER TO postgres;

--
-- TOC entry 258 (class 1259 OID 16671)
-- Name: lineages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.lineages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.lineages_id_seq OWNER TO postgres;

--
-- TOC entry 4820 (class 0 OID 0)
-- Dependencies: 258
-- Name: lineages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.lineages_id_seq OWNED BY public.lineages.id;


--
-- TOC entry 319 (class 1259 OID 17789)
-- Name: main_accounts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.main_accounts (
    id integer NOT NULL,
    account_name character varying(255) NOT NULL,
    account_code character varying(20),
    chart_account_id integer,
    is_required boolean DEFAULT true,
    description text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    table_name character varying(50),
    record_id integer,
    balance numeric(15,2) DEFAULT 0,
    is_active boolean DEFAULT true
);


ALTER TABLE public.main_accounts OWNER TO postgres;

--
-- TOC entry 318 (class 1259 OID 17788)
-- Name: main_accounts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.main_accounts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.main_accounts_id_seq OWNER TO postgres;

--
-- TOC entry 4821 (class 0 OID 0)
-- Dependencies: 318
-- Name: main_accounts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.main_accounts_id_seq OWNED BY public.main_accounts.id;


--
-- TOC entry 259 (class 1259 OID 16672)
-- Name: message_read_status; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.message_read_status (
    id integer NOT NULL,
    message_id integer,
    reader_type character varying(10) NOT NULL,
    reader_id integer NOT NULL,
    read_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT message_read_status_reader_type_check CHECK (((reader_type)::text = ANY ((ARRAY['user'::character varying, 'client'::character varying, 'ai'::character varying])::text[])))
);


ALTER TABLE public.message_read_status OWNER TO postgres;

--
-- TOC entry 260 (class 1259 OID 16677)
-- Name: message_read_status_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.message_read_status_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.message_read_status_id_seq OWNER TO postgres;

--
-- TOC entry 4822 (class 0 OID 0)
-- Dependencies: 260
-- Name: message_read_status_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.message_read_status_id_seq OWNED BY public.message_read_status.id;


--
-- TOC entry 261 (class 1259 OID 16678)
-- Name: messages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.messages (
    id integer NOT NULL,
    conversation_id integer,
    sender_type character varying(10) NOT NULL,
    sender_id integer NOT NULL,
    message_text text,
    message_type character varying(20) DEFAULT 'text'::character varying,
    file_url character varying(500),
    file_name character varying(255),
    file_size integer,
    reply_to_message_id integer,
    is_read boolean DEFAULT false,
    is_edited boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT messages_message_type_check CHECK (((message_type)::text = ANY (ARRAY[('text'::character varying)::text, ('image'::character varying)::text, ('file'::character varying)::text, ('reply'::character varying)::text]))),
    CONSTRAINT messages_sender_type_check CHECK (((sender_type)::text = ANY ((ARRAY['user'::character varying, 'client'::character varying, 'ai'::character varying])::text[])))
);


ALTER TABLE public.messages OWNER TO postgres;

--
-- TOC entry 262 (class 1259 OID 16690)
-- Name: messages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.messages_id_seq OWNER TO postgres;

--
-- TOC entry 4823 (class 0 OID 0)
-- Dependencies: 262
-- Name: messages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.messages_id_seq OWNED BY public.messages.id;


--
-- TOC entry 263 (class 1259 OID 16691)
-- Name: money_transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.money_transactions (
    id integer NOT NULL,
    account_id integer NOT NULL,
    amount_type integer NOT NULL,
    amount numeric(15,2) NOT NULL,
    currency character varying(10) DEFAULT 'rial'::character varying,
    transaction_date date NOT NULL,
    user_id integer NOT NULL,
    related_id integer,
    issue_id integer,
    description text,
    reference_number character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT money_transactions_amount_type_check CHECK ((amount_type = ANY (ARRAY[1, '-1'::integer]))),
    CONSTRAINT money_transactions_currency_check CHECK (((currency)::text = ANY (ARRAY[('rial'::character varying)::text, ('dollar'::character varying)::text, ('saudi'::character varying)::text])))
);


ALTER TABLE public.money_transactions OWNER TO postgres;

--
-- TOC entry 264 (class 1259 OID 16701)
-- Name: money_transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.money_transactions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.money_transactions_id_seq OWNER TO postgres;

--
-- TOC entry 4824 (class 0 OID 0)
-- Dependencies: 264
-- Name: money_transactions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.money_transactions_id_seq OWNED BY public.money_transactions.id;


--
-- TOC entry 265 (class 1259 OID 16702)
-- Name: movements; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.movements (
    id integer NOT NULL,
    case_id integer,
    case_number character varying(50),
    movement_type character varying(50),
    amount numeric(12,2),
    description text,
    movement_date date DEFAULT CURRENT_DATE,
    status character varying(50) DEFAULT 'completed'::character varying,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.movements OWNER TO postgres;

--
-- TOC entry 266 (class 1259 OID 16711)
-- Name: movements_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.movements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.movements_id_seq OWNER TO postgres;

--
-- TOC entry 4825 (class 0 OID 0)
-- Dependencies: 266
-- Name: movements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.movements_id_seq OWNED BY public.movements.id;


--
-- TOC entry 267 (class 1259 OID 16712)
-- Name: navigation_pages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.navigation_pages (
    id integer NOT NULL,
    page_title character varying(255) NOT NULL,
    page_url character varying(500) NOT NULL,
    page_description text,
    category character varying(100),
    keywords text,
    is_active boolean DEFAULT true,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.navigation_pages OWNER TO postgres;

--
-- TOC entry 268 (class 1259 OID 16720)
-- Name: navigation_pages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.navigation_pages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.navigation_pages_id_seq OWNER TO postgres;

--
-- TOC entry 4826 (class 0 OID 0)
-- Dependencies: 268
-- Name: navigation_pages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.navigation_pages_id_seq OWNED BY public.navigation_pages.id;


--
-- TOC entry 269 (class 1259 OID 16721)
-- Name: notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    recipient_type character varying(10) NOT NULL,
    recipient_id integer NOT NULL,
    sender_type character varying(10) NOT NULL,
    sender_id integer NOT NULL,
    notification_type character varying(20) DEFAULT 'message'::character varying,
    title character varying(255),
    content text,
    related_id integer,
    is_read boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT notifications_notification_type_check CHECK (((notification_type)::text = ANY (ARRAY[('message'::character varying)::text, ('mention'::character varying)::text, ('reply'::character varying)::text]))),
    CONSTRAINT notifications_recipient_type_check CHECK (((recipient_type)::text = ANY ((ARRAY['user'::character varying, 'client'::character varying, 'ai'::character varying])::text[]))),
    CONSTRAINT notifications_sender_type_check CHECK (((sender_type)::text = ANY ((ARRAY['user'::character varying, 'client'::character varying, 'ai'::character varying])::text[])))
);


ALTER TABLE public.notifications OWNER TO postgres;

--
-- TOC entry 270 (class 1259 OID 16732)
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.notifications_id_seq OWNER TO postgres;

--
-- TOC entry 4827 (class 0 OID 0)
-- Dependencies: 270
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- TOC entry 291 (class 1259 OID 17340)
-- Name: opening_balances; Type: TABLE; Schema: public; Owner: yemen
--

CREATE TABLE public.opening_balances (
    id integer NOT NULL,
    account_id character varying(50) NOT NULL,
    debit_balance numeric(15,2) DEFAULT 0,
    credit_balance numeric(15,2) DEFAULT 0,
    balance_date date NOT NULL,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.opening_balances OWNER TO yemen;

--
-- TOC entry 271 (class 1259 OID 16739)
-- Name: opening_balances_history; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.opening_balances_history (
    id integer NOT NULL,
    opening_balance_id integer,
    account_name character varying(255),
    account_code character varying(50),
    old_debit_amount numeric(15,2) DEFAULT 0,
    old_credit_amount numeric(15,2) DEFAULT 0,
    new_debit_amount numeric(15,2) DEFAULT 0,
    new_credit_amount numeric(15,2) DEFAULT 0,
    old_balance_type character varying(20),
    new_balance_type character varying(20),
    change_type character varying(50),
    changed_by character varying(255),
    change_reason text,
    change_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.opening_balances_history OWNER TO postgres;

--
-- TOC entry 272 (class 1259 OID 16749)
-- Name: opening_balances_history_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.opening_balances_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.opening_balances_history_id_seq OWNER TO postgres;

--
-- TOC entry 4828 (class 0 OID 0)
-- Dependencies: 272
-- Name: opening_balances_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.opening_balances_history_id_seq OWNED BY public.opening_balances_history.id;


--
-- TOC entry 290 (class 1259 OID 17339)
-- Name: opening_balances_id_seq; Type: SEQUENCE; Schema: public; Owner: yemen
--

CREATE SEQUENCE public.opening_balances_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.opening_balances_id_seq OWNER TO yemen;

--
-- TOC entry 4829 (class 0 OID 0)
-- Dependencies: 290
-- Name: opening_balances_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: yemen
--

ALTER SEQUENCE public.opening_balances_id_seq OWNED BY public.opening_balances.id;


--
-- TOC entry 273 (class 1259 OID 16751)
-- Name: payment; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payment (
    id integer NOT NULL,
    reference text,
    payment_class integer NOT NULL,
    payment_date date NOT NULL,
    closed boolean DEFAULT false,
    entity_id integer,
    employee_id integer,
    currency character varying(3) DEFAULT 'SAR'::character varying,
    notes text,
    department_id integer,
    gl_id integer,
    approved boolean DEFAULT false,
    workflow_id integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.payment OWNER TO postgres;

--
-- TOC entry 274 (class 1259 OID 16760)
-- Name: payment_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.payment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.payment_id_seq OWNER TO postgres;

--
-- TOC entry 4830 (class 0 OID 0)
-- Dependencies: 274
-- Name: payment_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.payment_id_seq OWNED BY public.payment.id;


--
-- TOC entry 275 (class 1259 OID 16761)
-- Name: payment_links; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payment_links (
    payment_id integer NOT NULL,
    entry_id integer NOT NULL,
    type integer NOT NULL
);


ALTER TABLE public.payment_links OWNER TO postgres;

--
-- TOC entry 336 (class 1259 OID 18801)
-- Name: payment_methods; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payment_methods (
    id integer NOT NULL,
    method_name character varying(100) NOT NULL,
    method_code character varying(20) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.payment_methods OWNER TO postgres;

--
-- TOC entry 335 (class 1259 OID 18800)
-- Name: payment_methods_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.payment_methods_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.payment_methods_id_seq OWNER TO postgres;

--
-- TOC entry 4831 (class 0 OID 0)
-- Dependencies: 335
-- Name: payment_methods_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.payment_methods_id_seq OWNED BY public.payment_methods.id;


--
-- TOC entry 334 (class 1259 OID 18771)
-- Name: payment_vouchers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.payment_vouchers (
    id integer NOT NULL,
    entry_number character varying(50) NOT NULL,
    voucher_number character varying(50),
    entry_date date NOT NULL,
    voucher_date date,
    payee_name character varying(255) NOT NULL,
    payee_type character varying(50) DEFAULT 'external'::character varying,
    debit_account_id integer,
    credit_account_id integer,
    amount numeric(15,2) NOT NULL,
    currency_id integer DEFAULT 1,
    payment_method_id integer,
    cost_center_id integer,
    description text,
    reference_number character varying(100),
    case_id integer,
    service_id integer,
    status character varying(20) DEFAULT 'draft'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.payment_vouchers OWNER TO postgres;

--
-- TOC entry 333 (class 1259 OID 18770)
-- Name: payment_vouchers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.payment_vouchers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.payment_vouchers_id_seq OWNER TO postgres;

--
-- TOC entry 4832 (class 0 OID 0)
-- Dependencies: 333
-- Name: payment_vouchers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.payment_vouchers_id_seq OWNED BY public.payment_vouchers.id;


--
-- TOC entry 350 (class 1259 OID 19009)
-- Name: permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.permissions (
    id integer NOT NULL,
    permission_key character varying(100) NOT NULL,
    permission_name character varying(200) NOT NULL,
    category character varying(100) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.permissions OWNER TO postgres;

--
-- TOC entry 349 (class 1259 OID 19008)
-- Name: permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.permissions_id_seq OWNER TO postgres;

--
-- TOC entry 4833 (class 0 OID 0)
-- Dependencies: 349
-- Name: permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.permissions_id_seq OWNED BY public.permissions.id;


--
-- TOC entry 276 (class 1259 OID 16772)
-- Name: project; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.project (
    id integer NOT NULL,
    projectnumber text,
    description text,
    startdate date,
    enddate date,
    parts_id integer,
    production numeric(20,8) DEFAULT 0,
    completed numeric(20,8) DEFAULT 0,
    customer_id integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.project OWNER TO postgres;

--
-- TOC entry 277 (class 1259 OID 16780)
-- Name: project_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.project_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.project_id_seq OWNER TO postgres;

--
-- TOC entry 4834 (class 0 OID 0)
-- Dependencies: 277
-- Name: project_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.project_id_seq OWNED BY public.project.id;


--
-- TOC entry 338 (class 1259 OID 18871)
-- Name: public_announcements; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.public_announcements (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    content text NOT NULL,
    type character varying(50) NOT NULL,
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT now(),
    updated_date timestamp without time zone DEFAULT now(),
    CONSTRAINT public_announcements_type_check CHECK (((type)::text = ANY ((ARRAY['public_1'::character varying, 'public_2'::character varying])::text[])))
);


ALTER TABLE public.public_announcements OWNER TO postgres;

--
-- TOC entry 337 (class 1259 OID 18870)
-- Name: public_announcements_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.public_announcements_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.public_announcements_id_seq OWNER TO postgres;

--
-- TOC entry 4835 (class 0 OID 0)
-- Dependencies: 337
-- Name: public_announcements_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.public_announcements_id_seq OWNED BY public.public_announcements.id;


--
-- TOC entry 332 (class 1259 OID 18755)
-- Name: receipt_vouchers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.receipt_vouchers (
    id integer NOT NULL,
    entry_number character varying(50) NOT NULL,
    entry_date date NOT NULL,
    payer_name character varying(255) NOT NULL,
    payer_type character varying(50) DEFAULT 'external'::character varying,
    debit_account_id integer,
    credit_account_id integer,
    amount numeric(15,2) NOT NULL,
    description text,
    reference_number character varying(100),
    status character varying(20) DEFAULT 'draft'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.receipt_vouchers OWNER TO postgres;

--
-- TOC entry 331 (class 1259 OID 18754)
-- Name: receipt_vouchers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.receipt_vouchers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.receipt_vouchers_id_seq OWNER TO postgres;

--
-- TOC entry 4836 (class 0 OID 0)
-- Dependencies: 331
-- Name: receipt_vouchers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.receipt_vouchers_id_seq OWNED BY public.receipt_vouchers.id;


--
-- TOC entry 278 (class 1259 OID 16781)
-- Name: security_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.security_logs (
    id integer NOT NULL,
    user_id integer,
    action character varying(255) NOT NULL,
    ip_address character varying(45),
    user_agent text,
    device_id character varying(255),
    success boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.security_logs OWNER TO postgres;

--
-- TOC entry 279 (class 1259 OID 16788)
-- Name: security_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.security_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.security_logs_id_seq OWNER TO postgres;

--
-- TOC entry 4837 (class 0 OID 0)
-- Dependencies: 279
-- Name: security_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.security_logs_id_seq OWNED BY public.security_logs.id;


--
-- TOC entry 280 (class 1259 OID 16789)
-- Name: service_distributions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.service_distributions (
    id integer NOT NULL,
    case_distribution_id integer,
    service_id integer,
    percentage numeric(5,2) DEFAULT 0,
    amount numeric(15,2) DEFAULT 0,
    lawyer_id integer,
    created_date date DEFAULT CURRENT_DATE
);


ALTER TABLE public.service_distributions OWNER TO postgres;

--
-- TOC entry 281 (class 1259 OID 16795)
-- Name: service_distributions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.service_distributions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.service_distributions_id_seq OWNER TO postgres;

--
-- TOC entry 4838 (class 0 OID 0)
-- Dependencies: 281
-- Name: service_distributions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.service_distributions_id_seq OWNED BY public.service_distributions.id;


--
-- TOC entry 344 (class 1259 OID 18959)
-- Name: services; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.services (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    lineage_id integer,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.services OWNER TO postgres;

--
-- TOC entry 343 (class 1259 OID 18958)
-- Name: services_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.services_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.services_id_seq OWNER TO postgres;

--
-- TOC entry 4839 (class 0 OID 0)
-- Dependencies: 343
-- Name: services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.services_id_seq OWNED BY public.services.id;


--
-- TOC entry 346 (class 1259 OID 18972)
-- Name: serviceslow; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.serviceslow (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    slug character varying(255) NOT NULL,
    description text,
    content text,
    icon_name character varying(100) DEFAULT 'Scale'::character varying,
    icon_color character varying(50) DEFAULT '#2563eb'::character varying,
    image_url character varying(500),
    is_active boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    meta_title character varying(255),
    meta_description text,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.serviceslow OWNER TO postgres;

--
-- TOC entry 4840 (class 0 OID 0)
-- Dependencies: 346
-- Name: TABLE serviceslow; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON TABLE public.serviceslow IS 'جدول خدمات الموقع الرئيسي - منفصل عن جدول services المستخدم في نظام إدارة القضايا';


--
-- TOC entry 345 (class 1259 OID 18971)
-- Name: serviceslow_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.serviceslow_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.serviceslow_id_seq OWNER TO postgres;

--
-- TOC entry 4841 (class 0 OID 0)
-- Dependencies: 345
-- Name: serviceslow_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.serviceslow_id_seq OWNED BY public.serviceslow.id;


--
-- TOC entry 322 (class 1259 OID 18515)
-- Name: suppliers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.suppliers (
    id integer NOT NULL,
    name character varying(255) NOT NULL,
    company_name character varying(255),
    phone character varying(50),
    email character varying(255),
    address text,
    tax_number character varying(100),
    commercial_register character varying(100),
    contact_person character varying(255),
    payment_terms character varying(100),
    credit_limit numeric(15,2) DEFAULT 0,
    current_balance numeric(15,2) DEFAULT 0,
    status character varying(20) DEFAULT 'active'::character varying,
    account_id integer,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.suppliers OWNER TO postgres;

--
-- TOC entry 321 (class 1259 OID 18514)
-- Name: suppliers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.suppliers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.suppliers_id_seq OWNER TO postgres;

--
-- TOC entry 4842 (class 0 OID 0)
-- Dependencies: 321
-- Name: suppliers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.suppliers_id_seq OWNED BY public.suppliers.id;


--
-- TOC entry 282 (class 1259 OID 16801)
-- Name: test_table; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.test_table (
    id integer NOT NULL,
    name character varying(100),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.test_table OWNER TO postgres;

--
-- TOC entry 283 (class 1259 OID 16805)
-- Name: test_table_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.test_table_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.test_table_id_seq OWNER TO postgres;

--
-- TOC entry 4843 (class 0 OID 0)
-- Dependencies: 283
-- Name: test_table_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.test_table_id_seq OWNED BY public.test_table.id;


--
-- TOC entry 303 (class 1259 OID 17513)
-- Name: time_entries; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.time_entries (
    id integer NOT NULL,
    case_id integer,
    client_id integer,
    employee_id integer NOT NULL,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone,
    duration_minutes integer,
    task_description text NOT NULL,
    task_category character varying(100),
    hourly_rate numeric(10,2),
    billable_amount numeric(12,2),
    is_billable boolean DEFAULT true,
    is_billed boolean DEFAULT false,
    status character varying(50) DEFAULT 'active'::character varying,
    notes text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.time_entries OWNER TO postgres;

--
-- TOC entry 302 (class 1259 OID 17512)
-- Name: time_entries_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.time_entries_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.time_entries_id_seq OWNER TO postgres;

--
-- TOC entry 4844 (class 0 OID 0)
-- Dependencies: 302
-- Name: time_entries_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.time_entries_id_seq OWNED BY public.time_entries.id;


--
-- TOC entry 284 (class 1259 OID 16806)
-- Name: timecard; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.timecard (
    id integer NOT NULL,
    employee_id integer,
    project_id integer,
    business_unit_id integer,
    transdate date NOT NULL,
    description text,
    qty numeric(20,8) NOT NULL,
    sellprice numeric(20,8),
    fxsellprice numeric(20,8),
    curr character varying(3) DEFAULT 'SAR'::character varying,
    allocated numeric(20,8) DEFAULT 0,
    notes text,
    jctype integer,
    total numeric(20,8),
    non_billable boolean DEFAULT false,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.timecard OWNER TO postgres;

--
-- TOC entry 285 (class 1259 OID 16815)
-- Name: timecard_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.timecard_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.timecard_id_seq OWNER TO postgres;

--
-- TOC entry 4845 (class 0 OID 0)
-- Dependencies: 285
-- Name: timecard_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.timecard_id_seq OWNED BY public.timecard.id;


--
-- TOC entry 352 (class 1259 OID 19022)
-- Name: user_permissions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_permissions (
    id integer NOT NULL,
    user_id integer,
    permission_key character varying(100) NOT NULL,
    granted_by integer,
    granted_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_active boolean DEFAULT true
);


ALTER TABLE public.user_permissions OWNER TO postgres;

--
-- TOC entry 351 (class 1259 OID 19021)
-- Name: user_permissions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_permissions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_permissions_id_seq OWNER TO postgres;

--
-- TOC entry 4846 (class 0 OID 0)
-- Dependencies: 351
-- Name: user_permissions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_permissions_id_seq OWNED BY public.user_permissions.id;


--
-- TOC entry 354 (class 1259 OID 19043)
-- Name: user_role_assignments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_role_assignments (
    id integer NOT NULL,
    user_id integer,
    role_name character varying(50),
    assigned_by integer,
    assigned_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_active boolean DEFAULT true
);


ALTER TABLE public.user_role_assignments OWNER TO postgres;

--
-- TOC entry 353 (class 1259 OID 19042)
-- Name: user_role_assignments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_role_assignments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_role_assignments_id_seq OWNER TO postgres;

--
-- TOC entry 4847 (class 0 OID 0)
-- Dependencies: 353
-- Name: user_role_assignments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_role_assignments_id_seq OWNED BY public.user_role_assignments.id;


--
-- TOC entry 317 (class 1259 OID 17724)
-- Name: user_roles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_roles (
    id integer NOT NULL,
    role_name character varying(50) NOT NULL,
    display_name character varying(100) NOT NULL,
    description text,
    permissions text[] DEFAULT '{}'::text[],
    is_active boolean DEFAULT true,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.user_roles OWNER TO postgres;

--
-- TOC entry 316 (class 1259 OID 17723)
-- Name: user_roles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_roles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_roles_id_seq OWNER TO postgres;

--
-- TOC entry 4848 (class 0 OID 0)
-- Dependencies: 316
-- Name: user_roles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_roles_id_seq OWNED BY public.user_roles.id;


--
-- TOC entry 286 (class 1259 OID 16816)
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username character varying(255) NOT NULL,
    email character varying(255),
    password_hash character varying(255),
    status character varying(20) DEFAULT 'active'::character varying,
    last_login timestamp without time zone,
    created_date date DEFAULT CURRENT_DATE,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    device_id character varying(255),
    employee_id integer,
    is_active boolean DEFAULT true,
    last_logout timestamp without time zone,
    last_failed_login timestamp without time zone,
    is_online boolean DEFAULT false,
    login_attempts integer DEFAULT 0,
    locked_until timestamp without time zone,
    role character varying(50) DEFAULT 'user'::character varying,
    permissions text[] DEFAULT '{}'::text[],
    user_type character varying(20) DEFAULT 'user'::character varying
);


ALTER TABLE public.users OWNER TO postgres;

--
-- TOC entry 287 (class 1259 OID 16827)
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- TOC entry 4849 (class 0 OID 0)
-- Dependencies: 287
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- TOC entry 288 (class 1259 OID 16828)
-- Name: vouchers; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.vouchers (
    id integer NOT NULL,
    voucher_number character varying(50) NOT NULL,
    voucher_type character varying(20) NOT NULL,
    voucher_date date NOT NULL,
    description text,
    total_amount numeric(15,2) DEFAULT 0 NOT NULL,
    reference_number character varying(100),
    status character varying(20) DEFAULT 'مسودة'::character varying,
    created_by character varying(100) DEFAULT 'النظام'::character varying,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT vouchers_voucher_type_check CHECK (((voucher_type)::text = ANY (ARRAY[('قبض'::character varying)::text, ('صرف'::character varying)::text, ('قيد'::character varying)::text])))
);


ALTER TABLE public.vouchers OWNER TO postgres;

--
-- TOC entry 289 (class 1259 OID 16838)
-- Name: vouchers_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.vouchers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.vouchers_id_seq OWNER TO postgres;

--
-- TOC entry 4850 (class 0 OID 0)
-- Dependencies: 289
-- Name: vouchers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.vouchers_id_seq OWNED BY public.vouchers.id;


--
-- TOC entry 342 (class 1259 OID 18928)
-- Name: website_services; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.website_services (
    id integer NOT NULL,
    title text NOT NULL,
    slug text NOT NULL,
    description text,
    content text,
    icon_name text DEFAULT 'Scale'::text,
    icon_color text DEFAULT '#2563eb'::text,
    image_url text,
    is_active boolean DEFAULT true,
    sort_order integer DEFAULT 0,
    meta_title text,
    meta_description text,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.website_services OWNER TO postgres;

--
-- TOC entry 341 (class 1259 OID 18927)
-- Name: website_services_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.website_services_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.website_services_id_seq OWNER TO postgres;

--
-- TOC entry 4851 (class 0 OID 0)
-- Dependencies: 341
-- Name: website_services_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.website_services_id_seq OWNED BY public.website_services.id;


--
-- TOC entry 3691 (class 2604 OID 16839)
-- Name: acc_trans entry_id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.acc_trans ALTER COLUMN entry_id SET DEFAULT nextval('public.acc_trans_entry_id_seq'::regclass);


--
-- TOC entry 4004 (class 2604 OID 18727)
-- Name: accounting_transaction_details id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transaction_details ALTER COLUMN id SET DEFAULT nextval('public.accounting_transaction_details_id_seq'::regclass);


--
-- TOC entry 3998 (class 2604 OID 18709)
-- Name: accounting_transactions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transactions ALTER COLUMN id SET DEFAULT nextval('public.accounting_transactions_id_seq'::regclass);


--
-- TOC entry 3692 (class 2604 OID 16841)
-- Name: accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts ALTER COLUMN id SET DEFAULT nextval('public.accounts_id_seq'::regclass);


--
-- TOC entry 3700 (class 2604 OID 16842)
-- Name: announcements id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.announcements ALTER COLUMN id SET DEFAULT nextval('public.announcements_id_seq'::regclass);


--
-- TOC entry 3704 (class 2604 OID 16843)
-- Name: ar id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ar ALTER COLUMN id SET DEFAULT nextval('public.ar_id_seq'::regclass);


--
-- TOC entry 3714 (class 2604 OID 16844)
-- Name: branches id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.branches ALTER COLUMN id SET DEFAULT nextval('public.branches_id_seq'::regclass);


--
-- TOC entry 3717 (class 2604 OID 16845)
-- Name: budget id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget ALTER COLUMN id SET DEFAULT nextval('public.budget_id_seq'::regclass);


--
-- TOC entry 3723 (class 2604 OID 16846)
-- Name: case_distribution id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_distribution ALTER COLUMN id SET DEFAULT nextval('public.case_distribution_id_seq'::regclass);


--
-- TOC entry 3727 (class 2604 OID 16847)
-- Name: chart_of_accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chart_of_accounts ALTER COLUMN id SET DEFAULT nextval('public.chart_of_accounts_id_seq'::regclass);


--
-- TOC entry 3942 (class 2604 OID 17632)
-- Name: client_notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications ALTER COLUMN id SET DEFAULT nextval('public.client_notifications_id_seq'::regclass);


--
-- TOC entry 3933 (class 2604 OID 17604)
-- Name: client_portal_accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts ALTER COLUMN id SET DEFAULT nextval('public.client_portal_accounts_id_seq'::regclass);


--
-- TOC entry 3948 (class 2604 OID 17661)
-- Name: client_requests id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests ALTER COLUMN id SET DEFAULT nextval('public.client_requests_id_seq'::regclass);


--
-- TOC entry 3953 (class 2604 OID 17689)
-- Name: client_sessions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_sessions ALTER COLUMN id SET DEFAULT nextval('public.client_sessions_id_seq'::regclass);


--
-- TOC entry 3737 (class 2604 OID 16848)
-- Name: clients id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients ALTER COLUMN id SET DEFAULT nextval('public.clients_id_seq'::regclass);


--
-- TOC entry 4028 (class 2604 OID 18900)
-- Name: companies id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.companies ALTER COLUMN id SET DEFAULT nextval('public.companies_id_seq'::regclass);


--
-- TOC entry 3745 (class 2604 OID 16850)
-- Name: company id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company ALTER COLUMN id SET DEFAULT nextval('public.company_id_seq'::regclass);


--
-- TOC entry 3747 (class 2604 OID 16851)
-- Name: conversations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversations ALTER COLUMN id SET DEFAULT nextval('public.conversations_id_seq'::regclass);


--
-- TOC entry 3752 (class 2604 OID 16852)
-- Name: cost_centers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cost_centers ALTER COLUMN id SET DEFAULT nextval('public.cost_centers_id_seq'::regclass);


--
-- TOC entry 3757 (class 2604 OID 16853)
-- Name: courts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.courts ALTER COLUMN id SET DEFAULT nextval('public.courts_id_seq'::regclass);


--
-- TOC entry 3760 (class 2604 OID 16854)
-- Name: currencies id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.currencies ALTER COLUMN id SET DEFAULT nextval('public.currencies_id_seq'::regclass);


--
-- TOC entry 3907 (class 2604 OID 17486)
-- Name: document_shares id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares ALTER COLUMN id SET DEFAULT nextval('public.document_shares_id_seq'::regclass);


--
-- TOC entry 3905 (class 2604 OID 17466)
-- Name: document_versions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_versions ALTER COLUMN id SET DEFAULT nextval('public.document_versions_id_seq'::regclass);


--
-- TOC entry 3898 (class 2604 OID 17431)
-- Name: documents id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents ALTER COLUMN id SET DEFAULT nextval('public.documents_id_seq'::regclass);


--
-- TOC entry 3766 (class 2604 OID 16855)
-- Name: employees id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees ALTER COLUMN id SET DEFAULT nextval('public.employees_id_seq'::regclass);


--
-- TOC entry 3773 (class 2604 OID 16856)
-- Name: entity id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity ALTER COLUMN id SET DEFAULT nextval('public.entity_id_seq'::regclass);


--
-- TOC entry 3775 (class 2604 OID 16857)
-- Name: entity_class id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_class ALTER COLUMN id SET DEFAULT nextval('public.entity_class_id_seq'::regclass);


--
-- TOC entry 3778 (class 2604 OID 16858)
-- Name: follows id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows ALTER COLUMN id SET DEFAULT nextval('public.follows_id_seq'::regclass);


--
-- TOC entry 4050 (class 2604 OID 18998)
-- Name: footer_links id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.footer_links ALTER COLUMN id SET DEFAULT nextval('public.footer_links_id_seq'::regclass);


--
-- TOC entry 3785 (class 2604 OID 16859)
-- Name: gl id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.gl ALTER COLUMN id SET DEFAULT nextval('public.gl_id_seq'::regclass);


--
-- TOC entry 3788 (class 2604 OID 16860)
-- Name: governorates id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.governorates ALTER COLUMN id SET DEFAULT nextval('public.governorates_id_seq'::regclass);


--
-- TOC entry 3889 (class 2604 OID 17359)
-- Name: hearings id; Type: DEFAULT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.hearings ALTER COLUMN id SET DEFAULT nextval('public.hearings_id_seq'::regclass);


--
-- TOC entry 3792 (class 2604 OID 16861)
-- Name: invoice id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice ALTER COLUMN id SET DEFAULT nextval('public.invoice_id_seq'::regclass);


--
-- TOC entry 3929 (class 2604 OID 17577)
-- Name: invoice_items id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items ALTER COLUMN id SET DEFAULT nextval('public.invoice_items_id_seq'::regclass);


--
-- TOC entry 3917 (class 2604 OID 17545)
-- Name: invoices id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices ALTER COLUMN id SET DEFAULT nextval('public.invoices_id_seq'::regclass);


--
-- TOC entry 3796 (class 2604 OID 16862)
-- Name: issue_types id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issue_types ALTER COLUMN id SET DEFAULT nextval('public.issue_types_id_seq'::regclass);


--
-- TOC entry 3801 (class 2604 OID 16863)
-- Name: issues id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues ALTER COLUMN id SET DEFAULT nextval('public.issues_id_seq'::regclass);


--
-- TOC entry 3986 (class 2604 OID 18669)
-- Name: journal_entries id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entries ALTER COLUMN id SET DEFAULT nextval('public.journal_entries_id_seq'::regclass);


--
-- TOC entry 3994 (class 2604 OID 18686)
-- Name: journal_entry_details id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entry_details ALTER COLUMN id SET DEFAULT nextval('public.journal_entry_details_id_seq'::regclass);


--
-- TOC entry 3893 (class 2604 OID 17396)
-- Name: lawyer_earnings id; Type: DEFAULT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.lawyer_earnings ALTER COLUMN id SET DEFAULT nextval('public.lawyer_earnings_id_seq'::regclass);


--
-- TOC entry 3811 (class 2604 OID 16866)
-- Name: lineages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.lineages ALTER COLUMN id SET DEFAULT nextval('public.lineages_id_seq'::regclass);


--
-- TOC entry 3960 (class 2604 OID 17792)
-- Name: main_accounts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.main_accounts ALTER COLUMN id SET DEFAULT nextval('public.main_accounts_id_seq'::regclass);


--
-- TOC entry 3815 (class 2604 OID 16867)
-- Name: message_read_status id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message_read_status ALTER COLUMN id SET DEFAULT nextval('public.message_read_status_id_seq'::regclass);


--
-- TOC entry 3817 (class 2604 OID 16868)
-- Name: messages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages ALTER COLUMN id SET DEFAULT nextval('public.messages_id_seq'::regclass);


--
-- TOC entry 3823 (class 2604 OID 16869)
-- Name: money_transactions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.money_transactions ALTER COLUMN id SET DEFAULT nextval('public.money_transactions_id_seq'::regclass);


--
-- TOC entry 3827 (class 2604 OID 16870)
-- Name: movements id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movements ALTER COLUMN id SET DEFAULT nextval('public.movements_id_seq'::regclass);


--
-- TOC entry 3832 (class 2604 OID 16871)
-- Name: navigation_pages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.navigation_pages ALTER COLUMN id SET DEFAULT nextval('public.navigation_pages_id_seq'::regclass);


--
-- TOC entry 3836 (class 2604 OID 16872)
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- TOC entry 3884 (class 2604 OID 17343)
-- Name: opening_balances id; Type: DEFAULT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.opening_balances ALTER COLUMN id SET DEFAULT nextval('public.opening_balances_id_seq'::regclass);


--
-- TOC entry 3840 (class 2604 OID 16874)
-- Name: opening_balances_history id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.opening_balances_history ALTER COLUMN id SET DEFAULT nextval('public.opening_balances_history_id_seq'::regclass);


--
-- TOC entry 3846 (class 2604 OID 16875)
-- Name: payment id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment ALTER COLUMN id SET DEFAULT nextval('public.payment_id_seq'::regclass);


--
-- TOC entry 4021 (class 2604 OID 18804)
-- Name: payment_methods id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_methods ALTER COLUMN id SET DEFAULT nextval('public.payment_methods_id_seq'::regclass);


--
-- TOC entry 4014 (class 2604 OID 18774)
-- Name: payment_vouchers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers ALTER COLUMN id SET DEFAULT nextval('public.payment_vouchers_id_seq'::regclass);


--
-- TOC entry 4055 (class 2604 OID 19012)
-- Name: permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions ALTER COLUMN id SET DEFAULT nextval('public.permissions_id_seq'::regclass);


--
-- TOC entry 3851 (class 2604 OID 16877)
-- Name: project id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project ALTER COLUMN id SET DEFAULT nextval('public.project_id_seq'::regclass);


--
-- TOC entry 4024 (class 2604 OID 18874)
-- Name: public_announcements id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.public_announcements ALTER COLUMN id SET DEFAULT nextval('public.public_announcements_id_seq'::regclass);


--
-- TOC entry 4008 (class 2604 OID 18758)
-- Name: receipt_vouchers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.receipt_vouchers ALTER COLUMN id SET DEFAULT nextval('public.receipt_vouchers_id_seq'::regclass);


--
-- TOC entry 3855 (class 2604 OID 16878)
-- Name: security_logs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.security_logs ALTER COLUMN id SET DEFAULT nextval('public.security_logs_id_seq'::regclass);


--
-- TOC entry 3858 (class 2604 OID 16879)
-- Name: service_distributions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_distributions ALTER COLUMN id SET DEFAULT nextval('public.service_distributions_id_seq'::regclass);


--
-- TOC entry 4041 (class 2604 OID 18962)
-- Name: services id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services ALTER COLUMN id SET DEFAULT nextval('public.services_id_seq'::regclass);


--
-- TOC entry 4043 (class 2604 OID 18975)
-- Name: serviceslow id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.serviceslow ALTER COLUMN id SET DEFAULT nextval('public.serviceslow_id_seq'::regclass);


--
-- TOC entry 3980 (class 2604 OID 18518)
-- Name: suppliers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.suppliers ALTER COLUMN id SET DEFAULT nextval('public.suppliers_id_seq'::regclass);


--
-- TOC entry 3862 (class 2604 OID 16881)
-- Name: test_table id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_table ALTER COLUMN id SET DEFAULT nextval('public.test_table_id_seq'::regclass);


--
-- TOC entry 3911 (class 2604 OID 17516)
-- Name: time_entries id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries ALTER COLUMN id SET DEFAULT nextval('public.time_entries_id_seq'::regclass);


--
-- TOC entry 3864 (class 2604 OID 16882)
-- Name: timecard id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.timecard ALTER COLUMN id SET DEFAULT nextval('public.timecard_id_seq'::regclass);


--
-- TOC entry 4058 (class 2604 OID 19025)
-- Name: user_permissions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions ALTER COLUMN id SET DEFAULT nextval('public.user_permissions_id_seq'::regclass);


--
-- TOC entry 4061 (class 2604 OID 19046)
-- Name: user_role_assignments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments ALTER COLUMN id SET DEFAULT nextval('public.user_role_assignments_id_seq'::regclass);


--
-- TOC entry 3956 (class 2604 OID 17727)
-- Name: user_roles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles ALTER COLUMN id SET DEFAULT nextval('public.user_roles_id_seq'::regclass);


--
-- TOC entry 3869 (class 2604 OID 16883)
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- TOC entry 3879 (class 2604 OID 16884)
-- Name: vouchers id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vouchers ALTER COLUMN id SET DEFAULT nextval('public.vouchers_id_seq'::regclass);


--
-- TOC entry 4034 (class 2604 OID 18931)
-- Name: website_services id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.website_services ALTER COLUMN id SET DEFAULT nextval('public.website_services_id_seq'::regclass);


--
-- TOC entry 4617 (class 0 OID 16393)
-- Dependencies: 211
-- Data for Name: acc_trans; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.acc_trans (trans_id, chart_id, amount, transdate, source, cleared, fx_transaction, memo, invoice_id, approved, entry_id, voucher_id) FROM stdin;
\.


--
-- TOC entry 4736 (class 0 OID 18724)
-- Dependencies: 330
-- Data for Name: accounting_transaction_details; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.accounting_transaction_details (id, transaction_id, account_id, account_name, account_code, debit_amount, credit_amount, description, line_order) FROM stdin;
1	1	6	النقدية والبنوك	1110	5000.00	0.00	استلام من فاطمة أحمد حسن	1
2	1	52	الإيرادات	4000	0.00	5000.00	إيراد من فاطمة أحمد حسن	2
3	2	6	النقدية والبنوك	1110	15000.00	0.00	استلام من شركة النور للتجارة	1
4	2	52	الإيرادات	4000	0.00	15000.00	إيراد من شركة النور للتجارة	2
5	3	6	النقدية والبنوك	1110	8000.00	0.00	استلام من محمد عبدالله صالح	1
6	3	52	الإيرادات	4000	0.00	8000.00	إيراد من محمد عبدالله صالح	2
7	4	6	النقدية والبنوك	1110	1000.00	0.00	استلام من عميل تجريبي	1
8	4	52	الإيرادات	4000	0.00	1000.00	إيراد من عميل تجريبي	2
9	5	6	النقدية والبنوك	1110	2000.00	0.00	استلام من عميل تجريبي من النموذج	1
10	5	52	الإيرادات	4000	0.00	2000.00	إيراد من عميل تجريبي من النموذج	2
11	6	6	النقدية والبنوك	1110	3000.00	0.00	استلام من عميل تجريبي نهائي	1
12	6	52	الإيرادات	4000	0.00	3000.00	إيراد من عميل تجريبي نهائي	2
13	7	6	النقدية والبنوك	1110	5000.00	0.00	استلام من عميل تجريبي مع قيد	1
14	7	52	الإيرادات	4000	0.00	5000.00	إيراد من عميل تجريبي مع قيد	2
15	8	6	النقدية والبنوك	1110	7500.00	0.00	استلام من عميل تجريبي مع تسجيل	1
16	8	52	الإيرادات	4000	0.00	7500.00	إيراد من عميل تجريبي مع تسجيل	2
17	9	6	النقدية والبنوك	1110	10000.00	0.00	استلام من عميل تجريبي نهائي	1
18	9	52	الإيرادات	4000	0.00	10000.00	إيراد من عميل تجريبي نهائي	2
19	10	19	المصروفات المدفوعة مقدماً	1140	15000.00	0.00	دفع إلى خالد أحمد المحامي	1
20	10	6	النقدية والبنوك	1110	0.00	15000.00	مصروف لـ خالد أحمد المحامي	2
21	11	19	المصروفات المدفوعة مقدماً	1140	2500.00	0.00	دفع إلى شركة الكهرباء	1
22	11	6	النقدية والبنوك	1110	0.00	2500.00	مصروف لـ شركة الكهرباء	2
23	12	19	المصروفات المدفوعة مقدماً	1140	1200.00	0.00	دفع إلى مكتبة القانون	1
24	12	6	النقدية والبنوك	1110	0.00	1200.00	مصروف لـ مكتبة القانون	2
25	13	19	المصروفات المدفوعة مقدماً	1140	2000.00	0.00	دفع إلى اختبار جديد	1
26	13	6	النقدية والبنوك	1110	0.00	2000.00	مصروف لـ اختبار جديد	2
27	14	19	المصروفات المدفوعة مقدماً	1140	1500.00	0.00	دفع إلى مورد تجريبي	1
28	14	6	النقدية والبنوك	1110	0.00	1500.00	مصروف لـ مورد تجريبي	2
29	15	19	المصروفات المدفوعة مقدماً	1140	3000.00	0.00	دفع إلى مورد تجريبي	1
30	15	6	النقدية والبنوك	1110	0.00	3000.00	مصروف لـ مورد تجريبي	2
31	16	1	الصندوق الرئيسي		50000.00	0.00	\N	1
32	16	2	رأس المال		0.00	50000.00	\N	2
33	17	1	الصندوق		1000.00	0.00	استلام نقدية	1
34	17	2	الإيرادات		0.00	1000.00	إيراد خدمات	2
35	18	1	الصندوق الرئيسي		5000.00	0.00	استلام نقدية من عميل	1
36	18	2	الإيرادات		0.00	5000.00	إيراد أتعاب قانونية	2
\.


--
-- TOC entry 4734 (class 0 OID 18706)
-- Dependencies: 328
-- Data for Name: accounting_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.accounting_transactions (id, transaction_number, transaction_type, transaction_date, description, party_name, party_type, reference_number, total_amount, status, created_by, created_date, updated_at) FROM stdin;
1	RV000001	receipt	2024-01-15	أتعاب استشارة قانونية	فاطمة أحمد حسن	client	\N	5000.00	draft	النظام	2025-08-23 22:42:18.009	2025-08-24 01:51:31.487932
2	RV000002	receipt	2024-01-16	أتعاب صياغة عقد	شركة النور للتجارة	client	\N	15000.00	draft	النظام	2025-08-23 22:42:18.009	2025-08-24 01:51:31.500454
3	RV000003	receipt	2024-01-17	أتعاب قضية تجارية	محمد عبدالله صالح	client	\N	8000.00	draft	النظام	2025-08-23 22:42:18.009	2025-08-24 01:51:31.509159
4	RV000004	receipt	2024-01-20	سند قبض تجريبي	عميل تجريبي	client	REF001	1000.00	draft	النظام	2025-08-23 23:11:49.139	2025-08-24 01:51:31.516555
5	RV000005	receipt	2024-01-21	سند قبض من النموذج المحدث	عميل تجريبي من النموذج	external	REF002	2000.00	draft	النظام	2025-08-23 23:17:23.117	2025-08-24 01:51:31.524708
6	RV000006	receipt	2024-01-22	سند قبض نهائي	عميل تجريبي نهائي	client	REF003	3000.00	draft	النظام	2025-08-23 23:17:48.742	2025-08-24 01:51:31.532544
7	RV000007	receipt	2024-01-25	سند قبض مع قيد يومية تلقائي	عميل تجريبي مع قيد	client	TEST001	5000.00	draft	النظام	2025-08-24 00:50:15.871	2025-08-24 01:51:31.54027
8	RV000008	receipt	2024-01-26	سند قبض مع تسجيل محسن	عميل تجريبي مع تسجيل	client	TEST002	7500.00	draft	النظام	2025-08-24 00:52:31.217	2025-08-24 01:51:31.552272
9	RV000009	receipt	2024-01-27	سند قبض مع قيد يومية نهائي	عميل تجريبي نهائي	client	FINAL001	10000.00	draft	النظام	2025-08-24 00:55:06.442	2025-08-24 01:51:31.559155
10	PV000001	payment	2024-01-15	راتب شهر يناير	خالد أحمد المحامي	employee	\N	15000.00	draft	النظام	2025-08-23 22:42:18.011	2025-08-24 01:51:31.567883
11	PV000002	payment	2024-01-16	فاتورة كهرباء المكتب	شركة الكهرباء	external	\N	2500.00	draft	النظام	2025-08-23 22:42:18.011	2025-08-24 01:51:31.574726
12	PV000003	payment	2024-01-17	شراء كتب قانونية	مكتبة القانون	external	\N	1200.00	draft	النظام	2025-08-23 22:42:18.011	2025-08-24 01:51:31.582955
13	PV000004	payment	2024-01-18	سند صرف تجريبي جديد	اختبار جديد	external	\N	2000.00	draft	النظام	2025-08-23 22:42:36.904	2025-08-24 01:51:31.591624
14	PV000005	payment	2024-01-23	سند صرف تجريبي	مورد تجريبي	supplier	PAY001	1500.00	draft	النظام	2025-08-23 23:19:26.886	2025-08-24 01:51:31.596869
15	PV000006	payment	2024-01-27	سند صرف مع قيد يومية	مورد تجريبي	supplier	PAY002	3000.00	draft	النظام	2025-08-24 00:55:44.309	2025-08-24 01:51:31.600593
16	JE000001	journal	2024-01-15	قيد افتتاحي للصندوق	\N	\N	\N	50000.00	draft	النظام	2025-08-23 22:42:18.012	2025-08-24 01:51:31.607564
17	JE000002	journal	2024-01-18	قيد تجريبي	\N	\N	\N	1000.00	draft	النظام	2025-08-23 22:44:33.993	2025-08-24 01:51:31.612198
18	JE000003	journal	2024-01-18	قيد تجريبي للاختبار	\N	\N	\N	5000.00	draft	النظام	2025-08-23 22:49:19.959	2025-08-24 01:51:31.615363
19	JE000004	journal	2025-08-23	بيسش شيبشب	\N	\N	\N	5000.00	draft	النظام	2025-08-23 23:26:58.864	2025-08-24 01:51:31.618186
\.


--
-- TOC entry 4619 (class 0 OID 16410)
-- Dependencies: 213
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.accounts (id, account_code, account_name, account_type, parent_id, balance_type, opening_balance, current_balance, is_active, created_date, account_category, account_level, allow_posting, description) FROM stdin;
1	1000	الأصول	أصول	\N	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	رئيسي	1	f	\N
7	2000	الخصوم	خصوم	\N	دائن	0.00	0.00	t	2025-07-18 23:06:00.718705	رئيسي	1	f	\N
10	3000	حقوق الملكية	حقوق ملكية	\N	دائن	0.00	0.00	t	2025-07-18 23:06:00.718705	رئيسي	1	f	\N
12	4000	الإيرادات	إيرادات	\N	دائن	0.00	0.00	t	2025-07-18 23:06:00.718705	رئيسي	1	f	\N
15	5000	المصروفات	مصروفات	\N	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	رئيسي	1	f	\N
3	1110	النقدية	أصول	2	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	1	t	\N
4	1111	الصندوق	أصول	3	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	1	t	\N
5	1112	البنك	أصول	3	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	1	t	\N
6	1120	المدينون	أصول	2	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	1	t	\N
9	2110	الدائنون	خصوم	8	دائن	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	1	t	\N
14	4110	أتعاب قانونية	إيرادات	13	دائن	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	1	t	\N
17	5110	مصروفات إدارية	مصروفات	16	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	1	t	\N
18	5120	مصروفات عمومية	مصروفات	16	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	1	t	\N
2	1100	الأصول المتداولة	أصول	1	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	2	t	\N
8	2100	الخصوم المتداولة	خصوم	7	دائن	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	2	t	\N
11	3100	رأس المال	حقوق ملكية	10	دائن	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	2	t	\N
13	4100	إيرادات التشغيل	إيرادات	12	دائن	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	2	t	\N
16	5100	مصروفات التشغيل	مصروفات	15	مدين	0.00	0.00	t	2025-07-18 23:06:00.718705	فرعي	2	t	\N
19	1113	حساب جاري - البنك الأهلي	أصول	5	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
20	1114	حساب جاري - بنك الراجحي	أصول	5	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
21	1121	عملاء - أتعاب قانونية	أصول	6	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
22	2111	موردون - مكتبية	خصوم	9	دائن	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
23	2112	مستحقات موظفين	خصوم	9	دائن	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
24	4111	أتعاب استشارات قانونية	إيرادات	14	دائن	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
25	4112	أتعاب ترافع	إيرادات	14	دائن	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
26	4113	أتعاب صياغة عقود	إيرادات	14	دائن	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
27	5111	رواتب وأجور	مصروفات	17	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
28	5112	إيجار المكتب	مصروفات	17	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
29	5113	كهرباء وماء	مصروفات	17	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
30	5121	مصروفات اتصالات	مصروفات	18	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
31	5122	مصروفات مكتبية	مصروفات	18	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
32	5123	مصروفات سفر وانتقال	مصروفات	18	مدين	0.00	0.00	t	2025-07-19 00:10:42.61763	فرعي	3	t	\N
\.


--
-- TOC entry 4726 (class 0 OID 18393)
-- Dependencies: 320
-- Data for Name: ai_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.ai_settings (id, enabled, model, delay_seconds, working_hours_only, working_hours_start, working_hours_end, working_days, max_responses_per_conversation, keywords_trigger, excluded_keywords, auto_responses, created_at, updated_at) FROM stdin;
1	t	groq-llama	3	f	00:00:00	23:59:00	{الأحد,الاثنين,الثلاثاء,الأربعاء,الخميس,الجمعة,السبت}	2000	{مساعدة,استفسار,سؤال,معلومات,خدمة,مرحبا,السلام,أهلا}	{}	{"help": "يمكنني مساعدتك في:\\\\n• الاستفسارات القانونية العامة\\\\n• معلومات عن خدمات المكتب\\\\n• توجيهك للمحامي المناسب", "greeting": "مرحباً! أنا المساعد الذكي للمكتب.", "signature": "🤖 المساعد الذكي للمكتب", "disclaimer": "للحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا."}	2025-08-18 01:55:27.75368	2025-08-18 03:47:56.421072
\.


--
-- TOC entry 4621 (class 0 OID 16425)
-- Dependencies: 215
-- Data for Name: announcements; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.announcements (id, announcement_1, announcement_2, announcement_3, announcement_4, is_active, created_date, updated_at) FROM stdin;
1	مرحباً بكم في نظام إدارة شركات ومكاتب المحاماة 	قريبا يمكنكم إدارة حساباتكم وادارة القضايا وخدمة العملاء بسهولة	تم تحديث النظام بميزات جديدة ومحسنة	للدعم الفني يرجى التواصل مع الإدارة على الرقم *********	t	2025-07-24 02:14:05.869397	2025-08-03 04:04:44.746854
3					t	2025-08-25 01:07:15.736909	2025-08-25 01:07:15.736909
\.


--
-- TOC entry 4623 (class 0 OID 16434)
-- Dependencies: 217
-- Data for Name: ar; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.ar (id, invnumber, transdate, entity_id, taxincluded, amount, netamount, paid, datepaid, duedate, invoice, shippingpoint, terms, notes, curr, ordnumber, employee_id, till, quonumber, intnotes, department_id, shipvia, language_code, ponumber, on_hold, reverse, approved, created_date, workflow_id, crdate) FROM stdin;
\.


--
-- TOC entry 4625 (class 0 OID 16449)
-- Dependencies: 219
-- Data for Name: branches; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.branches (id, name, governorate_id, address, phone, manager_name, is_active, created_date) FROM stdin;
1	فرع صنعاء الرئيسي	1	شارع الزبيري - صنعاء	01-123456	أحمد محمد	t	2025-07-14
2	فرع عدن	2	كريتر - عدن	02-234567	محمد علي	t	2025-07-14
3	فرع تعز	3	شارع جمال - تعز	04-345678	علي أحمد	t	2025-07-14
4	فرع الحديدة	4	شارع الكورنيش - الحديدة	03-456789	سالم محمد	t	2025-07-14
\.


--
-- TOC entry 4627 (class 0 OID 16457)
-- Dependencies: 221
-- Data for Name: budget; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.budget (id, budget_year, account_id, budgeted_amount, actual_amount, variance_amount, variance_percentage, notes, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 4629 (class 0 OID 16468)
-- Dependencies: 223
-- Data for Name: case_distribution; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.case_distribution (id, issue_id, lineage_id, admin_amount, remaining_amount, created_date) FROM stdin;
\.


--
-- TOC entry 4631 (class 0 OID 16475)
-- Dependencies: 225
-- Data for Name: chart_of_accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.chart_of_accounts (id, account_code, account_name, account_name_en, level_1_code, level_2_code, level_3_code, level_4_code, account_level, parent_id, account_type, account_nature, is_active, allow_transactions, linked_table, auto_create_sub_accounts, opening_balance, current_balance, description, notes, created_date, updated_date, is_linked_record, original_table, linked_record_id, linked_entity_type, linked_entity_id) FROM stdin;
4	1000	الأصول	\N	\N	\N	\N	\N	1	\N	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
5	1100	الأصول المتداولة	\N	\N	\N	\N	\N	2	4	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
135	1121016	عميل اختبار جديد	\N	\N	\N	\N	\N	5	121	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:43:51.647283	2025-08-23 19:43:51.647283	f	\N	\N	\N	\N
7	1111	الصندوق الرئيسي	\N	\N	\N	\N	\N	4	6	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
6	1110	النقدية والبنوك	\N	\N	\N	\N	\N	3	5	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
8	1112	صندوق فرعي	\N	\N	\N	\N	\N	4	6	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
10	1114	بنك سبأ	\N	\N	\N	\N	\N	4	6	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
136	12010016	حساب العميل: عميل اختبار جديد	\N	\N	\N	\N	\N	4	\N	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 20:07:59.724742	2025-08-23 20:07:59.724742	t	clients	16	\N	\N
15	1123	مدينون متنوعون	\N	\N	\N	\N	\N	4	12	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-26 00:35:48.458665	f	\N	\N	\N	\N
9	1113	البنك الأهلي اليمني	\N	\N	\N	\N	\N	4	6	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
11	1115	البنك التجاري اليمني	\N	\N	\N	\N	\N	4	6	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
12	1120	العملاء والمدينون	\N	\N	\N	\N	\N	3	5	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
16	1130	المخزون	\N	\N	\N	\N	\N	3	5	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
17	1131	مخزون القرطاسية	\N	\N	\N	\N	\N	4	16	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
18	1132	مخزون المواد المكتبية	\N	\N	\N	\N	\N	4	16	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
19	1140	المصروفات المدفوعة مقدماً	\N	\N	\N	\N	\N	3	5	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
20	1141	إيجار مدفوع مقدماً	\N	\N	\N	\N	\N	4	19	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
21	1142	تأمين مدفوع مقدماً	\N	\N	\N	\N	\N	4	19	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
22	1200	الأصول الثابتة	\N	\N	\N	\N	\N	2	4	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
23	1210	الأثاث والمعدات	\N	\N	\N	\N	\N	3	22	أصول	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
24	1211	أثاث المكتب	\N	\N	\N	\N	\N	4	23	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
25	1212	أجهزة الكمبيوتر	\N	\N	\N	\N	\N	4	23	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
26	1213	معدات مكتبية	\N	\N	\N	\N	\N	4	23	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
27	1220	مجمع الإهلاك	\N	\N	\N	\N	\N	3	22	أصول	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
28	1221	مجمع إهلاك الأثاث	\N	\N	\N	\N	\N	4	27	أصول	دائن	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
29	1222	مجمع إهلاك أجهزة الكمبيوتر	\N	\N	\N	\N	\N	4	27	أصول	دائن	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
30	2000	الخصوم	\N	\N	\N	\N	\N	1	\N	خصوم	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
31	2100	الخصوم المتداولة	\N	\N	\N	\N	\N	2	30	خصوم	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
32	2110	الموردون والدائنون	\N	\N	\N	\N	\N	3	31	خصوم	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
34	2112	أوراق الدفع	\N	\N	\N	\N	\N	4	32	خصوم	دائن	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
35	2113	دائنون متنوعون	\N	\N	\N	\N	\N	4	32	خصوم	دائن	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
36	2120	المرتبات والأجور المستحقة	\N	\N	\N	\N	\N	3	31	خصوم	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
38	2122	بدلات مستحقة	\N	\N	\N	\N	\N	4	36	خصوم	دائن	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
40	2130	الضرائب والرسوم المستحقة	\N	\N	\N	\N	\N	3	31	خصوم	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
41	2131	ضريبة الدخل المستحقة	\N	\N	\N	\N	\N	4	40	خصوم	دائن	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
42	2132	رسوم حكومية مستحقة	\N	\N	\N	\N	\N	4	40	خصوم	دائن	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
43	2140	العهد والأمانات	\N	\N	\N	\N	\N	3	31	خصوم	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
45	2142	أمانات العملاء	\N	\N	\N	\N	\N	4	43	خصوم	دائن	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
46	3000	حقوق الملكية	\N	\N	\N	\N	\N	1	\N	حقوق ملكية	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
47	3100	رأس المال	\N	\N	\N	\N	\N	2	46	حقوق ملكية	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
48	3110	رأس المال المدفوع	\N	\N	\N	\N	\N	3	47	حقوق ملكية	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
49	3200	الأرباح المحتجزة	\N	\N	\N	\N	\N	2	46	حقوق ملكية	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
50	3210	أرباح العام الحالي	\N	\N	\N	\N	\N	3	49	حقوق ملكية	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
51	3220	أرباح الأعوام السابقة	\N	\N	\N	\N	\N	3	49	حقوق ملكية	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
52	4000	الإيرادات	\N	\N	\N	\N	\N	1	\N	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
53	4100	إيرادات الخدمات القانونية	\N	\N	\N	\N	\N	2	52	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
54	4110	أتعاب الاستشارات القانونية	\N	\N	\N	\N	\N	3	53	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
55	4120	أتعاب التمثيل القانوني	\N	\N	\N	\N	\N	3	53	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
56	4130	أتعاب صياغة العقود	\N	\N	\N	\N	\N	3	53	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
57	4140	أتعاب التوثيق	\N	\N	\N	\N	\N	3	53	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
58	4200	إيرادات أخرى	\N	\N	\N	\N	\N	2	52	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
59	4210	إيرادات فوائد	\N	\N	\N	\N	\N	3	58	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
60	4220	إيرادات استثمارات	\N	\N	\N	\N	\N	3	58	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
61	4230	فوارق عملة موجبة	\N	\N	\N	\N	\N	3	58	إيرادات	دائن	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
137	12020005	حساب الموظف: محمد الحاشدي	\N	\N	\N	\N	\N	4	\N	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 20:08:47.532982	2025-08-23 20:08:47.532982	t	employees	5	\N	\N
62	5000	المصروفات	\N	\N	\N	\N	\N	1	\N	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
63	5100	مصروفات الموظفين	\N	\N	\N	\N	\N	2	62	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
64	5110	المرتبات والأجور	\N	\N	\N	\N	\N	3	63	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
138	12020011	حساب الموظف: سارة أحمد محمد	\N	\N	\N	\N	\N	4	\N	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 20:08:47.542186	2025-08-23 20:08:47.542186	t	employees	11	\N	\N
139	12020012	حساب الموظف: خالد محمد علي	\N	\N	\N	\N	\N	4	\N	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 20:08:47.547668	2025-08-23 20:08:47.547668	t	employees	12	\N	\N
140	12020013	حساب الموظف: نادية حسن صالح	\N	\N	\N	\N	\N	4	\N	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 20:08:47.553778	2025-08-23 20:08:47.553778	t	employees	13	\N	\N
68	5114	مكافآت نهاية الخدمة	\N	\N	\N	\N	\N	4	64	مصروفات	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
69	5120	التأمينات الاجتماعية	\N	\N	\N	\N	\N	3	63	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
70	5121	تأمينات اجتماعية	\N	\N	\N	\N	\N	4	69	مصروفات	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
71	5122	تأمين صحي	\N	\N	\N	\N	\N	4	69	مصروفات	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
72	5200	المصروفات الإدارية	\N	\N	\N	\N	\N	2	62	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
73	5210	إيجار المكتب	\N	\N	\N	\N	\N	3	72	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
74	5220	الكهرباء والماء	\N	\N	\N	\N	\N	3	72	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
75	5230	الهاتف والإنترنت	\N	\N	\N	\N	\N	3	72	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
76	5240	القرطاسية والمطبوعات	\N	\N	\N	\N	\N	3	72	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
77	5250	صيانة وإصلاح	\N	\N	\N	\N	\N	3	72	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
78	5260	مصروفات السفر	\N	\N	\N	\N	\N	3	72	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
79	5270	مصروفات ضيافة	\N	\N	\N	\N	\N	3	72	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
80	5300	مصروفات أخرى	\N	\N	\N	\N	\N	2	62	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
81	5310	الإهلاك	\N	\N	\N	\N	\N	3	80	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
82	5320	مصروفات بنكية	\N	\N	\N	\N	\N	3	80	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
83	5330	فوارق عملة سالبة	\N	\N	\N	\N	\N	3	80	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
84	5340	مصروفات متنوعة	\N	\N	\N	\N	\N	3	80	مصروفات	مدين	t	f	\N	f	0.00	0.00	\N	\N	2025-08-20 00:00:00	2025-08-20 03:37:23.370663	f	\N	\N	\N	\N
141	12020014	حساب الموظف: عمر عبدالله أحمد	\N	\N	\N	\N	\N	4	\N	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 20:08:47.558282	2025-08-23 20:08:47.558282	t	employees	14	\N	\N
121	1121	حسابات العملاء	\N	\N	\N	\N	\N	4	12	أصول	مدين	t	f	clients	t	0.00	0.00	الحساب الرئيسي لجميع العملاء	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
86	1151	حسابات الموظفين	Employees	11	1150	1150	1150	4	19	أصول	مدين	t	f	employees	t	0.00	0.00	الحساب الرئيسي لجميع الموظفين (عهد وسلف)	\N	2025-08-20 03:55:22.682072	2025-08-20 23:16:13.165263	f	\N	\N	\N	\N
33	2111	حسابات الموردين	\N	\N	\N	\N	\N	4	32	خصوم	دائن	t	f	suppliers	t	0.00	0.00	الحساب الرئيسي لجميع الموردين	\N	2025-08-20 00:00:00	2025-08-20 23:16:49.540174	f	\N	\N	\N	\N
122	1121010	عائشة علي محمد	\N	\N	\N	\N	\N	5	121	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
123	1121011	يوسف حسن أحمد	\N	\N	\N	\N	\N	5	121	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
124	1121012	تجريبي	\N	\N	\N	\N	\N	5	121	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
125	1121001	عميل تجريبي	\N	\N	\N	\N	\N	5	121	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
126	1121007	أحمد محمد علي	\N	\N	\N	\N	\N	5	121	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
127	1121008	فاطمة أحمد حسن	\N	\N	\N	\N	\N	5	121	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
128	1121009	محمد عبدالله صالح	\N	\N	\N	\N	\N	5	121	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
129	1151005	محمد الحاشدي	\N	\N	\N	\N	\N	5	86	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
130	1151011	سارة أحمد محمد	\N	\N	\N	\N	\N	5	86	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
131	1151012	خالد محمد علي	\N	\N	\N	\N	\N	5	86	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
132	1151013	نادية حسن صالح	\N	\N	\N	\N	\N	5	86	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
133	1151014	عمر عبدالله أحمد	\N	\N	\N	\N	\N	5	86	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
134	1151015	ليلى محمد حسن	\N	\N	\N	\N	\N	5	86	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 19:38:38.156482	2025-08-23 19:38:38.156482	f	\N	\N	\N	\N
142	12020015	حساب الموظف: ليلى محمد حسن	\N	\N	\N	\N	\N	4	\N	أصول	مدين	t	t	\N	f	0.00	0.00	\N	\N	2025-08-23 20:08:47.563468	2025-08-23 20:08:47.563468	t	employees	15	\N	\N
\.


--
-- TOC entry 4717 (class 0 OID 17629)
-- Dependencies: 311
-- Data for Name: client_notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.client_notifications (id, client_id, title, message, type, case_id, document_id, is_read, read_at, sent_via, sent_at, created_date) FROM stdin;
1	1	مرحباً بك في بوابة العملاء	تم إنشاء حسابك بنجاح في بوابة العملاء. يمكنك الآن متابعة قضاياك والوصول للوثائق والخدمات المختلفة.	success	\N	\N	f	\N	portal	2025-08-04 03:22:20.719858	2025-08-04 03:22:20.719858
2	1	وثيقة جديدة متاحة	تم إضافة وثيقة جديدة لحسابك: دليل الإجراءات القانونية	info	\N	\N	f	\N	portal	2025-08-04 03:22:20.719858	2025-08-04 03:22:20.719858
3	1	تحديث في النظام	تم تحديث النظام وإضافة ميزات جديدة لتحسين تجربتك	info	\N	\N	t	\N	portal	2025-08-04 03:22:20.719858	2025-08-04 03:22:20.719858
4	1	تذكير مهم	لا تنس مراجعة الوثائق المطلوبة قبل الموعد القادم	warning	\N	\N	f	\N	portal	2025-08-04 03:22:20.719858	2025-08-04 03:22:20.719858
5	1	رسالة من المحامي	يرجى التواصل معنا لمناقشة تفاصيل إضافية حول قضيتك	info	\N	\N	f	\N	portal	2025-08-04 03:22:20.719858	2025-08-04 03:22:20.719858
6	3	مرحباً بك في بوابة العملاء	تم إنشاء حسابك بنجاح. يرجى التحقق من بريدك الإلكتروني لتفعيل الحساب.	success	\N	\N	f	\N	portal	2025-08-04 03:43:57.677047	2025-08-04 03:43:57.677047
\.


--
-- TOC entry 4715 (class 0 OID 17601)
-- Dependencies: 309
-- Data for Name: client_portal_accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.client_portal_accounts (id, client_id, username, email, password_hash, is_active, is_verified, verification_token, reset_token, reset_token_expires, language, timezone, notification_preferences, last_login, login_attempts, locked_until, created_date, updated_at) FROM stdin;
2	3	admin	<EMAIL>	$2b$12$x0yF8nFP5zWGXMqfEzFcQOtlIHq6DoqCYyHpnHtxDm85ZKlpL6R3i	t	f	feb262b805ff5533440bbe824bd66e833d9e6e6ee419ffcc97284bdd7be0443f	\N	\N	ar	Asia/Kuwait	{"case_updates": true, "document_uploads": true, "email_notifications": true, "appointment_reminders": true, "invoice_notifications": true}	\N	0	\N	2025-08-04 03:43:57.661441	2025-08-04 03:43:57.661441
1	1	demo_client	<EMAIL>	$2b$12$KJ2dw0yqIHsiylnn.SWcQuTCEiqq1afBfidzgkQGIvQTX1gCFDQvO	t	t	\N	\N	\N	ar	Asia/Riyadh	{"case_updates": true, "document_uploads": true, "email_notifications": true, "appointment_reminders": true, "invoice_notifications": true}	\N	0	\N	2025-08-04 03:22:20.716471	2025-08-04 03:22:20.716471
\.


--
-- TOC entry 4719 (class 0 OID 17658)
-- Dependencies: 313
-- Data for Name: client_requests; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.client_requests (id, client_id, case_id, request_type, title, description, priority, status, assigned_to, response, due_date, completed_date, created_date, updated_at) FROM stdin;
1	1	\N	document_request	طلب نسخة من الوثيقة	أحتاج نسخة من دليل الإجراءات القانونية	medium	pending	\N	\N	\N	\N	2025-08-04 03:22:20.72308	2025-08-04 03:22:20.72308
2	1	\N	meeting_request	طلب موعد استشارة	أرغب في حجز موعد لاستشارة قانونية	high	in_progress	\N	\N	\N	\N	2025-08-04 03:22:20.72308	2025-08-04 03:22:20.72308
3	1	\N	status_inquiry	استفسار عن الخدمات	ما هي الخدمات المتاحة في البوابة؟	low	completed	\N	\N	\N	\N	2025-08-04 03:22:20.72308	2025-08-04 03:22:20.72308
4	1	\N	document_request	طلب وثيقة مخصصة	أحتاج إعداد وثيقة قانونية مخصصة	high	pending	\N	\N	\N	\N	2025-08-04 03:22:20.72308	2025-08-04 03:22:20.72308
5	1	\N	status_inquiry	استفسار عن الرسوم	ما هي رسوم الخدمات المختلفة؟	medium	in_progress	\N	\N	\N	\N	2025-08-04 03:22:20.72308	2025-08-04 03:22:20.72308
\.


--
-- TOC entry 4721 (class 0 OID 17686)
-- Dependencies: 315
-- Data for Name: client_sessions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.client_sessions (id, client_id, session_token, ip_address, user_agent, expires_at, is_active, created_date) FROM stdin;
1	1	demo_session_token_*********abcdef	*************	Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36	2025-08-05 03:22:20.725964	t	2025-08-04 03:22:20.725964
\.


--
-- TOC entry 4633 (class 0 OID 16491)
-- Dependencies: 227
-- Data for Name: clients; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.clients (id, name, phone, email, address, id_number, status, cases_count, created_date, updated_at, client_type, username, password_hash, last_login, is_online, login_attempts, locked_until, account_id, current_balance) FROM stdin;
10	عائشة علي محمد	*********	<EMAIL>	شارع الثورة، صنعاء	***********	active	0	2025-08-20	2025-08-20 02:53:34.072543	\N	\N	\N	\N	f	0	\N	121	0.00
11	يوسف حسن أحمد	*********	<EMAIL>	شارع الجمهورية، صنعاء	***********	active	0	2025-08-20	2025-08-20 02:53:34.072543	\N	\N	\N	\N	f	0	\N	121	0.00
12	تجريبي	*********	<EMAIL>	شبمن ب بمكشنستب شك	***********	active	0	2025-08-20	2025-08-20 03:58:50.598207	individual	شبشضثق	$2b$10$akGNF/oBY6B2h319iC/mA.4C4a9g0o97OrjNx6GW5FaL5xCS68fg6	\N	f	0	\N	121	0.00
1	عميل تجريبي	*********	<EMAIL>	صنعاء شارع تعز	************	active	0	2025-08-20	2025-08-20 03:10:56.459625	individual	client	$2b$10$QevemCZS9eV.QLVFrrcKi.r3A05FSYEPOOgEAQBOfGDAxwzIVCW.m	\N	f	0	\N	121	0.00
7	أحمد محمد علي	*********	<EMAIL>	شارع الزبيري، صنعاء	***********	active	0	2025-08-20	2025-08-20 03:11:11.627468	\N	admin	$2b$10$QTYV6LE/FTnct1h9YOu5ruBfLUchfEd0Gt4OZDVfRA0tVTdpCZHX2	\N	f	0	\N	121	0.00
8	فاطمة أحمد حسن	777234567	<EMAIL>	شارع الستين، صنعاء	0*********1	active	0	2025-08-20	2025-08-20 02:53:34.072543	\N	\N	\N	\N	f	0	\N	121	0.00
9	محمد عبدالله صالح	777345678	<EMAIL>	شارع الحصبة، صنعاء	0*********2	active	0	2025-08-20	2025-08-20 02:53:34.072543	\N	\N	\N	\N	f	0	\N	121	0.00
16	عميل اختبار جديد	*********	<EMAIL>	\N	\N	active	0	2025-08-23	2025-08-23 19:43:51.647283	\N	\N	\N	\N	f	0	\N	121	0.00
\.


--
-- TOC entry 4746 (class 0 OID 18897)
-- Dependencies: 340
-- Data for Name: companies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.companies (id, name, address, phone, email, website, established_date, created_date, legal_name, registration_number, city, country, logo_right_text, logo_left_text, logo_url, legal_form, capital, description, is_active, tax_number, logo_image_url, updated_at, latitude, longitude, map_zoom, working_hours) FROM stdin;
1	مؤسسة الجرافي للمحاماة  والاستشارات القانونية	صنعاء - شارع مجاهد - عمارة الحاشدي - الدور الثالث .	+967-1-123456	<EMAIL>	www.legalfirm.ye	2020-01-06	2025-07-22 00:00:00	مؤسسة الجرافي للمحاماة  والاستشارات القانونية	CR-2024-001	صنعاء	اليمن	مؤسسة الجرافي  للمحاماة والاستشارات القانونية	صنعاء \nشارع مجاهد - رقم *********	/images/logo.png	مؤسسة قانونية محلية ودولية 	1000000.00	مكتب متخصص في تقديم الخدمات القانونية والاستشارات القانونية في جميع المجالات	t	TAX-*********	/uploads/logos/logo_1756144248408.png	2025-07-22 02:08:04.110617	15.33834930	44.20112000	15	الأحد - الخميس: 8:00 ص - 6:00 م
\.


--
-- TOC entry 4635 (class 0 OID 16512)
-- Dependencies: 229
-- Data for Name: company; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.company (id, entity_id, legal_name, tax_id, sales_tax_id, license_number, created_date, control_code, country_id, sic_code) FROM stdin;
\.


--
-- TOC entry 4637 (class 0 OID 16519)
-- Dependencies: 231
-- Data for Name: conversations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.conversations (id, client_id, user_id, title, status, created_at, updated_at, last_message_at) FROM stdin;
1	1	1	استشارة قانونية - أحمد محمد سالم	active	2025-07-14 12:16:50.36047	2025-08-19 14:42:50.996357	2025-08-19 14:42:50.996357
\.


--
-- TOC entry 4639 (class 0 OID 16527)
-- Dependencies: 233
-- Data for Name: cost_centers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cost_centers (id, center_code, center_name, parent_id, center_level, is_active, description, created_date, updated_date) FROM stdin;
1	CC001	الإدارة العامة	\N	1	t	مركز تكلفة الإدارة العامة والخدمات الإدارية	2025-07-24 02:00:14.616325	2025-07-24 02:00:14.616325
2	CC002	القسم القانوني	\N	1	t	مركز تكلفة الخدمات القانونية والقضايا	2025-07-24 02:00:14.620277	2025-07-24 02:00:14.620277
3	CC003	المحاسبة والمالية	\N	1	t	مركز تكلفة الخدمات المحاسبية والمالية	2025-07-24 02:00:14.621506	2025-07-24 02:00:14.621506
4	CC004	الموارد البشرية	\N	1	t	مركز تكلفة إدارة الموارد البشرية	2025-07-24 02:00:14.623858	2025-07-24 02:00:14.623858
5	CC005	التسويق والعلاقات العامة	\N	1	t	مركز تكلفة أنشطة التسويق والعلاقات العامة	2025-07-24 02:00:14.625372	2025-07-24 02:00:14.625372
6	CC006	تقنية المعلومات	\N	1	t	مركز تكلفة خدمات تقنية المعلومات والأنظمة	2025-07-24 02:00:14.626296	2025-07-24 02:00:14.626296
7	CC002-01	القضايا المدنية	2	2	t	مركز تكلفة القضايا المدنية	2025-07-24 02:00:14.628424	2025-07-24 02:00:14.628424
8	CC002-02	القضايا التجارية	2	2	t	مركز تكلفة القضايا التجارية	2025-07-24 02:00:14.629373	2025-07-24 02:00:14.629373
9	CC002-03	القضايا الجنائية	2	2	t	مركز تكلفة القضايا الجنائية	2025-07-24 02:00:14.630284	2025-07-24 02:00:14.630284
10	CC002-04	قضايا الأحوال الشخصية	2	2	t	مركز تكلفة قضايا الأحوال الشخصية	2025-07-24 02:00:14.631219	2025-07-24 02:00:14.631219
11	CC002-05	القضايا العمالية	2	2	t	مركز تكلفة القضايا العمالية	2025-07-24 02:00:14.632125	2025-07-24 02:00:14.632125
\.


--
-- TOC entry 4641 (class 0 OID 16537)
-- Dependencies: 235
-- Data for Name: courts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.courts (id, name, type, governorate_id, address, phone, employee_id, issue_id, is_active, created_date) FROM stdin;
16	المحكمة العليا	محكمة عليا	5	شارع الزبيري، صنعاء	01-274856	\N	\N	t	2025-08-20
17	محكمة استئناف صنعاء	محكمة استئناف	5	شارع الستين، صنعاء	01-274857	\N	\N	t	2025-08-20
18	المحكمة التجارية بصنعاء	محكمة تجارية	5	شارع الحصبة، صنعاء	01-274858	\N	\N	t	2025-08-20
19	محكمة الأحوال الشخصية	محكمة أحوال شخصية	5	شارع الثورة، صنعاء	01-274859	\N	\N	t	2025-08-20
20	المحكمة الجنائية	محكمة جنائية	5	شارع الجمهورية، صنعاء	01-274860	\N	\N	t	2025-08-20
\.


--
-- TOC entry 4643 (class 0 OID 16545)
-- Dependencies: 237
-- Data for Name: currencies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.currencies (id, currency_code, currency_name, symbol, is_active, exchange_rate, is_base_currency, created_date, updated_date) FROM stdin;
2	USD	الدولار الأمريكي	$	t	3.7500	f	2025-07-18 22:01:08.330144	2025-07-18 22:01:08.330144
1	YR	ريال يمني	ر.س	t	1.0000	t	2025-07-18 22:01:08.330144	2025-07-18 22:01:08.330144
3	SAR	ريال سعودي 	€	t	4.0800	f	2025-07-18 22:01:08.330144	2025-07-18 22:01:08.330144
11	YER	ريال يمني	ر.ي	t	1.0000	t	2025-07-20 16:27:08.448935	2025-07-20 16:27:08.448935
12	EUR	يورو	€	t	1600.0000	f	2025-07-20 16:27:08.448935	2025-07-20 16:27:08.448935
\.


--
-- TOC entry 4707 (class 0 OID 17483)
-- Dependencies: 301
-- Data for Name: document_shares; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.document_shares (id, document_id, shared_with_user, shared_with_client, permission_level, shared_by, expires_at, is_active, created_date) FROM stdin;
\.


--
-- TOC entry 4705 (class 0 OID 17463)
-- Dependencies: 299
-- Data for Name: document_versions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.document_versions (id, document_id, version_number, file_name, file_path, file_size, changes_description, uploaded_by, created_date) FROM stdin;
\.


--
-- TOC entry 4703 (class 0 OID 17428)
-- Dependencies: 297
-- Data for Name: documents; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.documents (id, title, description, file_name, file_path, file_size, file_type, mime_type, case_id, client_id, employee_id, category, subcategory, tags, content_text, keywords, access_level, is_confidential, uploaded_by, version_number, is_active, created_date, updated_at) FROM stdin;
1	عقد توكيل نموذجي	نموذج عقد توكيل قانوني	power_of_attorney_template.pdf	/documents/templates/power_of_attorney_template.pdf	\N	\N	\N	\N	\N	\N	template	\N	{عقد,توكيل,نموذج}	نموذج عقد توكيل قانوني للاستخدام في القضايا المختلفة	\N	private	f	1	1	t	2025-08-04 03:05:17.640561	2025-08-04 03:05:17.640561
2	دليل الإجراءات القانونية	دليل شامل للإجراءات القانونية	legal_procedures_guide.pdf	/documents/guides/legal_procedures_guide.pdf	\N	\N	\N	\N	\N	\N	guide	\N	{دليل,إجراءات,قانون}	دليل شامل يوضح الإجراءات القانونية المختلفة	\N	private	f	1	1	t	2025-08-04 03:05:17.640561	2025-08-04 03:05:17.640561
3	عقد توكيل نموذجي	نموذج عقد توكيل قانوني	power_of_attorney_template.pdf	/documents/templates/power_of_attorney_template.pdf	\N	\N	\N	\N	\N	\N	template	\N	{عقد,توكيل,نموذج}	نموذج عقد توكيل قانوني للاستخدام في القضايا المختلفة	\N	private	f	1	1	t	2025-08-04 03:06:01.954119	2025-08-04 03:06:01.954119
4	دليل الإجراءات القانونية	دليل شامل للإجراءات القانونية	legal_procedures_guide.pdf	/documents/guides/legal_procedures_guide.pdf	\N	\N	\N	\N	\N	\N	guide	\N	{دليل,إجراءات,قانون}	دليل شامل يوضح الإجراءات القانونية المختلفة	\N	private	f	1	1	t	2025-08-04 03:06:01.954119	2025-08-04 03:06:01.954119
9	دليل الإجراءات القانونية	دليل شامل للإجراءات القانونية في المملكة	legal_guide.pdf	/uploads/documents/legal_guide.pdf	1024000	pdf	application/pdf	\N	\N	\N	guide	legal	{دليل,إجراءات,قانون}	\N	\N	public	f	\N	1	t	2025-08-04 03:21:08.062694	2025-08-04 03:21:08.062694
10	نموذج عقد استشارة	نموذج عقد استشارة قانونية	contract_template.pdf	/uploads/documents/contract_template.pdf	512000	pdf	application/pdf	\N	\N	\N	template	contract	{نموذج,عقد,استشارة}	\N	\N	public	f	\N	1	t	2025-08-04 03:21:08.062694	2025-08-04 03:21:08.062694
11	وثيقة سرية	وثيقة سرية للاستخدام الداخلي	confidential_doc.pdf	/uploads/documents/confidential_doc.pdf	256000	pdf	application/pdf	\N	\N	\N	internal	confidential	{سري,داخلي}	\N	\N	restricted	t	\N	1	t	2025-08-04 03:21:08.062694	2025-08-04 03:21:08.062694
12	تقرير مالي	تقرير مالي ربع سنوي	financial_report.pdf	/uploads/documents/financial_report.pdf	2048000	pdf	application/pdf	\N	\N	\N	financial	report	{مالي,تقرير,"ربع سنوي"}	\N	\N	private	f	\N	1	t	2025-08-04 03:21:08.062694	2025-08-04 03:21:08.062694
13	دليل الإجراءات القانونية	دليل شامل للإجراءات القانونية في المملكة	legal_guide.pdf	/uploads/documents/legal_guide.pdf	1024000	pdf	application/pdf	\N	\N	\N	guide	legal	{دليل,إجراءات,قانون}	\N	\N	public	f	\N	1	t	2025-08-04 03:21:26.533474	2025-08-04 03:21:26.533474
14	نموذج عقد استشارة	نموذج عقد استشارة قانونية	contract_template.pdf	/uploads/documents/contract_template.pdf	512000	pdf	application/pdf	\N	\N	\N	template	contract	{نموذج,عقد,استشارة}	\N	\N	public	f	\N	1	t	2025-08-04 03:21:26.533474	2025-08-04 03:21:26.533474
15	وثيقة سرية	وثيقة سرية للاستخدام الداخلي	confidential_doc.pdf	/uploads/documents/confidential_doc.pdf	256000	pdf	application/pdf	\N	\N	\N	internal	confidential	{سري,داخلي}	\N	\N	restricted	t	\N	1	t	2025-08-04 03:21:26.533474	2025-08-04 03:21:26.533474
16	تقرير مالي	تقرير مالي ربع سنوي	financial_report.pdf	/uploads/documents/financial_report.pdf	2048000	pdf	application/pdf	\N	\N	\N	financial	report	{مالي,تقرير,"ربع سنوي"}	\N	\N	private	f	\N	1	t	2025-08-04 03:21:26.533474	2025-08-04 03:21:26.533474
17	دليل الإجراءات القانونية	دليل شامل للإجراءات القانونية في المملكة	legal_guide.pdf	/uploads/documents/legal_guide.pdf	1024000	pdf	application/pdf	\N	\N	\N	guide	legal	{دليل,إجراءات,قانون}	\N	\N	public	f	\N	1	t	2025-08-04 03:22:20.708792	2025-08-04 03:22:20.708792
18	نموذج عقد استشارة	نموذج عقد استشارة قانونية	contract_template.pdf	/uploads/documents/contract_template.pdf	512000	pdf	application/pdf	\N	\N	\N	template	contract	{نموذج,عقد,استشارة}	\N	\N	public	f	\N	1	t	2025-08-04 03:22:20.708792	2025-08-04 03:22:20.708792
19	وثيقة سرية	وثيقة سرية للاستخدام الداخلي	confidential_doc.pdf	/uploads/documents/confidential_doc.pdf	256000	pdf	application/pdf	\N	\N	\N	internal	confidential	{سري,داخلي}	\N	\N	restricted	t	\N	1	t	2025-08-04 03:22:20.708792	2025-08-04 03:22:20.708792
20	تقرير مالي	تقرير مالي ربع سنوي	financial_report.pdf	/uploads/documents/financial_report.pdf	2048000	pdf	application/pdf	\N	\N	\N	financial	report	{مالي,تقرير,"ربع سنوي"}	\N	\N	private	f	\N	1	t	2025-08-04 03:22:20.708792	2025-08-04 03:22:20.708792
21	مذكرة قانونية	مذكرة قانونية حول قضية معينة	legal_memo.docx	/uploads/documents/legal_memo.docx	128000	docx	application/vnd.openxmlformats-officedocument.wordprocessingml.document	\N	\N	\N	memo	legal	{مذكرة,قانونية}	\N	\N	private	f	\N	1	t	2025-08-04 03:22:20.708792	2025-08-04 03:22:20.708792
22	وثيقة اختبار	وثيقة اختبار للنظام	1754269506086_test-document.txt	/uploads/documents/1754269506086_test-document.txt	290	txt	text/plain	\N	\N	\N	test		{}	\N	\N	public	f	1	1	t	2025-08-04 04:05:06.119892	2025-08-04 04:05:06.119892
\.


--
-- TOC entry 4645 (class 0 OID 16554)
-- Dependencies: 239
-- Data for Name: employees; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.employees (id, name, "position", department, phone, email, address, id_number, salary, hire_date, status, created_date, updated_at, department_id, branch_id, governorate_id, employee_number, account_id, current_balance) FROM stdin;
5	محمد الحاشدي	مدير	1	*********	<EMAIL>	\N	***********	100000.00	2025-07-31	active	2025-08-20	2025-08-20 03:27:25.063506	\N	1	5	EMP001	86	0.00
11	سارة أحمد محمد	محامية	القسم القانوني	*********	<EMAIL>	صنعاء، اليمن	********	35000.00	2025-08-20	active	2025-08-20	2025-08-20 02:53:34.072543	\N	1	5	EMP002	86	0.00
12	خالد محمد علي	محاسب	المحاسبة	*********	<EMAIL>	صنعاء، اليمن	********	30000.00	2025-08-20	active	2025-08-20	2025-08-20 02:53:34.072543	\N	1	5	EMP003	86	0.00
13	نادية حسن صالح	سكرتيرة	الإدارة	*********	<EMAIL>	صنعاء، اليمن	********	25000.00	2025-08-20	active	2025-08-20	2025-08-20 02:53:34.072543	\N	1	5	EMP004	86	0.00
14	عمر عبدالله أحمد	مستشار قانوني	الاستشارات	*********	<EMAIL>	صنعاء، اليمن	********	40000.00	2025-08-20	active	2025-08-20	2025-08-20 02:53:34.072543	\N	1	5	EMP005	86	0.00
15	ليلى محمد حسن	محامية متدربة	القسم القانوني	777555666	<EMAIL>	صنعاء، اليمن	55555555	20000.00	2025-08-20	active	2025-08-20	2025-08-20 02:53:34.072543	\N	1	5	EMP006	86	0.00
\.


--
-- TOC entry 4647 (class 0 OID 16565)
-- Dependencies: 241
-- Data for Name: entity; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.entity (id, name, entity_class, created_date, control_code, country_id) FROM stdin;
\.


--
-- TOC entry 4648 (class 0 OID 16571)
-- Dependencies: 242
-- Data for Name: entity_class; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.entity_class (id, class, in_use, created_date) FROM stdin;
1	Vendor	t	2025-07-19 01:07:09.772385
2	Customer	t	2025-07-19 01:07:09.772385
3	Employee	t	2025-07-19 01:07:09.772385
4	Contact	t	2025-07-19 01:07:09.772385
5	Lead	t	2025-07-19 01:07:09.772385
\.


--
-- TOC entry 4651 (class 0 OID 16578)
-- Dependencies: 245
-- Data for Name: follows; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.follows (id, case_id, case_number, case_title, client_name, service_type, description, date_field, status, priority, created_date, updated_at, service_id, user_id, next_hearing_date, earned_amount, is_approved, approved_by, approved_date, next_hearing_id, report, next_hearing_time, court_name, hearing_type) FROM stdin;
\.


--
-- TOC entry 4754 (class 0 OID 18995)
-- Dependencies: 348
-- Data for Name: footer_links; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.footer_links (id, category, name, href, sort_order, is_active, created_date, updated_at) FROM stdin;
1	روابط سريعة	الرئيسية	/home	1	t	2025-08-27 00:47:32.217637	2025-08-27 00:47:32.217637
2	روابط سريعة	خدماتنا	/serviceslow	2	t	2025-08-27 00:47:32.219411	2025-08-27 00:47:32.219411
3	روابط سريعة	المكتبة القانونية	/home#library	3	t	2025-08-27 00:47:32.220276	2025-08-27 00:47:32.220276
4	روابط سريعة	من نحن	/home#about	4	t	2025-08-27 00:47:32.221331	2025-08-27 00:47:32.221331
5	روابط سريعة	فريق العمل	/home#team	5	t	2025-08-27 00:47:32.22242	2025-08-27 00:47:32.22242
6	روابط سريعة	اتصل بنا	/home#contact	6	t	2025-08-27 00:47:32.223416	2025-08-27 00:47:32.223416
7	خدماتنا	القانون الجنائي والدفاع	/serviceslow/criminal-law-defense	1	t	2025-08-27 00:47:32.224348	2025-08-27 00:47:32.224348
8	خدماتنا	صياغة العقود والاتفاقيات	/serviceslow/contracts-agreements	2	t	2025-08-27 00:47:32.2252	2025-08-27 00:47:32.2252
9	خدماتنا	قانون الشركات والاستثمار	/serviceslow/qnwn-lshrkt-wlstthmr-tjrb	3	t	2025-08-27 00:47:32.22691	2025-08-27 00:47:32.22691
10	خدماتنا	قانون العمل والعلاقات الصناعية	/serviceslow/labor-industrial-relations	4	t	2025-08-27 00:47:32.227808	2025-08-27 00:47:32.227808
11	خدماتنا	القانون المدني والأحوال الشخصية	/serviceslow/civil-personal-status-law	5	t	2025-08-27 00:47:32.22871	2025-08-27 00:47:32.22871
12	خدماتنا	جميع الخدمات	/serviceslow	6	t	2025-08-27 00:47:32.229731	2025-08-27 00:47:32.229731
13	المستندات القانونية	نماذج عقود	/home#library	1	t	2025-08-27 00:47:32.230762	2025-08-27 00:47:32.230762
14	المستندات القانونية	لوائح وأنظمة	/home#library	2	t	2025-08-27 00:47:32.231841	2025-08-27 00:47:32.231841
15	المستندات القانونية	أحكام قضائية	/home#library	3	t	2025-08-27 00:47:32.232985	2025-08-27 00:47:32.232985
16	المستندات القانونية	أبحاث قانونية	/home#library	4	t	2025-08-27 00:47:32.23416	2025-08-27 00:47:32.23416
17	المستندات القانونية	أسئلة متكررة	/home#faq	5	t	2025-08-27 00:47:32.235474	2025-08-27 00:47:32.235474
18	المستندات القانونية	المزيد	/home#library	6	t	2025-08-27 00:47:32.236668	2025-08-27 00:47:32.236668
\.


--
-- TOC entry 4653 (class 0 OID 16588)
-- Dependencies: 247
-- Data for Name: gl; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.gl (id, reference, description, transdate, person_id, notes, approved, approved_by, approved_at, created_date, workflow_id) FROM stdin;
\.


--
-- TOC entry 4655 (class 0 OID 16596)
-- Dependencies: 249
-- Data for Name: governorates; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.governorates (id, name, code, region, population, is_capital, is_active, created_date) FROM stdin;
5	صنعاء	SAN	\N	\N	f	t	2025-08-20
37	عدن	ADE	\N	\N	f	t	2025-08-20
38	تعز	TAI	\N	\N	f	t	2025-08-20
39	الحديدة	HOD	\N	\N	f	t	2025-08-20
40	إب	IBB	\N	\N	f	t	2025-08-20
41	ذمار	DHA	\N	\N	f	t	2025-08-20
42	حضرموت	HAD	\N	\N	f	t	2025-08-20
43	لحج	LAH	\N	\N	f	t	2025-08-20
44	أبين	ABI	\N	\N	f	t	2025-08-20
45	شبوة	SHA	\N	\N	f	t	2025-08-20
57	حجة	HAJ	\N	\N	f	t	2025-08-20
58	صعدة	SAD	\N	\N	f	t	2025-08-20
59	عمران	AMR	\N	\N	f	t	2025-08-20
60	الجوف	JOW	\N	\N	f	t	2025-08-20
61	مأرب	MAR	\N	\N	f	t	2025-08-20
62	البيضاء	BAY	\N	\N	f	t	2025-08-20
63	الضالع	DAL	\N	\N	f	t	2025-08-20
64	ريمة	RAY	\N	\N	f	t	2025-08-20
56	المحويت	MAH	\N	\N	f	t	2025-08-20
66	سقطرى	SOC	\N	\N	f	t	2025-08-20
67	أمانة العاصمة	CAP	\N	\N	f	t	2025-08-20
\.


--
-- TOC entry 4699 (class 0 OID 17356)
-- Dependencies: 293
-- Data for Name: hearings; Type: TABLE DATA; Schema: public; Owner: yemen
--

COPY public.hearings (id, issue_id, hearing_date, hearing_time, court_name, hearing_type, notes, status, created_date, updated_at) FROM stdin;
6	11	2024-09-01	10:00:00	المحكمة العليا	جلسة أولى	الجلسة الأولى لمناقشة القضية	مجدولة	2025-08-20	2025-08-20 02:57:53.098996
7	12	2024-09-15	11:00:00	محكمة استئناف صنعاء	جلسة مرافعة	جلسة مرافعة الدفاع	مجدولة	2025-08-20	2025-08-20 02:57:53.098996
8	13	2024-08-20	09:00:00	المحكمة التجارية بصنعاء	جلسة حكم	تم إصدار الحكم	مكتملة	2025-08-20	2025-08-20 02:57:53.098996
9	14	2024-09-30	14:00:00	محكمة الأحوال الشخصية	جلسة استئناف	جلسة استئناف الحكم	مجدولة	2025-08-20	2025-08-20 02:57:53.098996
10	15	2024-10-10	10:30:00	المحكمة الجنائية	جلسة تنفيذ	جلسة تنفيذ الحكم	مجدولة	2025-08-20	2025-08-20 02:57:53.098996
\.


--
-- TOC entry 4657 (class 0 OID 16603)
-- Dependencies: 251
-- Data for Name: invoice; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.invoice (id, trans_id, parts_id, description, qty, allocated, sellprice, fxsellprice, discount, assemblyitem, unit, deliverydate, serialnumber, notes) FROM stdin;
\.


--
-- TOC entry 4713 (class 0 OID 17574)
-- Dependencies: 307
-- Data for Name: invoice_items; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.invoice_items (id, invoice_id, description, quantity, unit_price, total_price, time_entry_id, case_id, item_type, created_date) FROM stdin;
\.


--
-- TOC entry 4711 (class 0 OID 17542)
-- Dependencies: 305
-- Data for Name: invoices; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.invoices (id, invoice_number, client_id, client_name, client_address, invoice_date, due_date, subtotal, tax_rate, tax_amount, discount_amount, total_amount, status, payment_status, paid_amount, payment_date, notes, terms_conditions, created_by, created_date, updated_at) FROM stdin;
2	INV000001	1	عميل تجريبي	الرياض، المملكة العربية السعودية	2025-07-30	2025-08-29	1050.00	15.00	157.50	0.00	1207.50	sent	unpaid	0.00	\N	\N	\N	1	2025-08-04 03:21:26.540686	2025-08-04 03:21:26.540686
\.


--
-- TOC entry 4659 (class 0 OID 16612)
-- Dependencies: 253
-- Data for Name: issue_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.issue_types (id, name, description, color, cases_count, is_active, created_date, updated_at) FROM stdin;
1	قضايا مدنية	القضايا المدنية والتجارية	\N	0	t	2025-07-14	2025-07-14 01:18:11.225322
2	قضايا جنائية	القضايا الجنائية والجزائية	\N	0	t	2025-07-14	2025-07-14 01:18:11.227462
3	قضايا أحوال شخصية	قضايا الزواج والطلاق والميراث	\N	0	t	2025-07-14	2025-07-14 01:18:11.228027
4	قضايا عمالية	قضايا العمل والعمال	\N	0	t	2025-07-14	2025-07-14 01:18:11.228824
5	قضايا إدارية	القضايا الإدارية والحكومية	\N	0	t	2025-07-14	2025-07-14 01:18:11.229475
\.


--
-- TOC entry 4661 (class 0 OID 16622)
-- Dependencies: 255
-- Data for Name: issues; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.issues (id, case_number, title, description, client_id, issue_type_id, status, amount, next_hearing, notes, created_date, updated_at, court_id, case_amount, start_date, end_date, contract_method, contract_date, client_name, court_name, issue_type, next_hearing_date, updated_date) FROM stdin;
12	CASE-2024-002	قضية عمالية	نزاع عمالي حول مستحقات الموظف	7	\N	جديد	\N	\N	\N	2025-08-20	2025-08-20 02:57:53.098996	17	\N	\N	\N	بالجلسة	2025-08-20		\N	عمالي	\N	2025-08-20 02:57:53.098996
13	CASE-2024-003	قضية أحوال شخصية	قضية طلاق وحضانة أطفال	8	\N	مكتمل	\N	\N	\N	2025-08-20	2025-08-20 02:57:53.098996	18	\N	\N	\N	بالجلسة	2025-08-20		\N	أحوال شخصية	\N	2025-08-20 02:57:53.098996
14	CASE-2024-004	قضية عقارية	نزاع حول ملكية عقار	9	\N	جاري	\N	\N	\N	2025-08-20	2025-08-20 02:57:53.098996	19	\N	\N	\N	بالجلسة	2025-08-20		\N	عقاري	\N	2025-08-20 02:57:53.098996
15	CASE-2024-005	قضية جنائية	قضية احتيال مالي	10	\N	معلق	100000.00	\N		2025-08-20	2025-08-27 02:36:19.666956	20	\N	\N	\N	بالعقد	2025-08-19	عائشة علي محمد	المحكمة الجنائية	جنائي	\N	2025-08-20 02:57:53.098996
11	CASE-2024-001	قضية نزاع تجاري	نزاع تجاري بين شركتين حول عقد توريد	1	\N	جاري	500000.00	\N		2025-08-20	2025-08-27 02:36:42.868181	16	\N	\N	\N	بالجلسة	2025-08-19	عميل تجريبي	المحكمة العليا	تجاري	\N	2025-08-20 02:57:53.098996
\.


--
-- TOC entry 4730 (class 0 OID 18666)
-- Dependencies: 324
-- Data for Name: journal_entries; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.journal_entries (id, entry_number, entry_date, description, total_debit, total_credit, status, created_by, created_date, updated_at, entry_type, party_name, party_type, reference_number) FROM stdin;
8	RV000001	2024-01-15	أتعاب استشارة قانونية	5000.00	5000.00	draft	النظام	2025-08-23 22:42:18.009	2025-08-24 01:52:47.312559	receipt	فاطمة أحمد حسن	client	\N
9	RV000002	2024-01-16	أتعاب صياغة عقد	15000.00	15000.00	draft	النظام	2025-08-23 22:42:18.009	2025-08-24 01:52:47.326425	receipt	شركة النور للتجارة	client	\N
10	RV000003	2024-01-17	أتعاب قضية تجارية	8000.00	8000.00	draft	النظام	2025-08-23 22:42:18.009	2025-08-24 01:52:47.335328	receipt	محمد عبدالله صالح	client	\N
11	RV000004	2024-01-20	سند قبض تجريبي	1000.00	1000.00	draft	النظام	2025-08-23 23:11:49.139	2025-08-24 01:52:47.344367	receipt	عميل تجريبي	client	REF001
12	RV000005	2024-01-21	سند قبض من النموذج المحدث	2000.00	2000.00	draft	النظام	2025-08-23 23:17:23.117	2025-08-24 01:52:47.353905	receipt	عميل تجريبي من النموذج	external	REF002
13	RV000006	2024-01-22	سند قبض نهائي	3000.00	3000.00	draft	النظام	2025-08-23 23:17:48.742	2025-08-24 01:52:47.361121	receipt	عميل تجريبي نهائي	client	REF003
14	RV000007	2024-01-25	سند قبض مع قيد يومية تلقائي	5000.00	5000.00	draft	النظام	2025-08-24 00:50:15.871	2025-08-24 01:52:47.368128	receipt	عميل تجريبي مع قيد	client	TEST001
15	RV000008	2024-01-26	سند قبض مع تسجيل محسن	7500.00	7500.00	draft	النظام	2025-08-24 00:52:31.217	2025-08-24 01:52:47.37534	receipt	عميل تجريبي مع تسجيل	client	TEST002
16	RV000009	2024-01-27	سند قبض مع قيد يومية نهائي	10000.00	10000.00	draft	النظام	2025-08-24 00:55:06.442	2025-08-24 01:52:47.382499	receipt	عميل تجريبي نهائي	client	FINAL001
17	PV000001	2024-01-15	راتب شهر يناير	15000.00	15000.00	draft	النظام	2025-08-23 22:42:18.011	2025-08-24 01:52:47.390936	payment	خالد أحمد المحامي	employee	\N
18	PV000002	2024-01-16	فاتورة كهرباء المكتب	2500.00	2500.00	draft	النظام	2025-08-23 22:42:18.011	2025-08-24 01:52:47.397133	payment	شركة الكهرباء	external	\N
19	PV000003	2024-01-17	شراء كتب قانونية	1200.00	1200.00	draft	النظام	2025-08-23 22:42:18.011	2025-08-24 01:52:47.402133	payment	مكتبة القانون	external	\N
20	PV000004	2024-01-18	سند صرف تجريبي جديد	2000.00	2000.00	draft	النظام	2025-08-23 22:42:36.904	2025-08-24 01:52:47.406785	payment	اختبار جديد	external	\N
21	PV000005	2024-01-23	سند صرف تجريبي	1500.00	1500.00	draft	النظام	2025-08-23 23:19:26.886	2025-08-24 01:52:47.413344	payment	مورد تجريبي	supplier	PAY001
22	PV000006	2024-01-27	سند صرف مع قيد يومية	3000.00	3000.00	draft	النظام	2025-08-24 00:55:44.309	2025-08-24 01:52:47.418346	payment	مورد تجريبي	supplier	PAY002
23	RV000010	2024-01-28	سند قبض من API الموحد	5000.00	5000.00	draft	النظام	2025-08-24 01:54:14.839433	2025-08-24 01:54:14.839433	receipt	عميل جديد	client	UNIFIED001
24	PV000007	2024-01-28	سند صرف من API الموحد	2000.00	2000.00	draft	النظام	2025-08-24 01:54:30.816953	2025-08-24 01:54:30.816953	payment	مورد جديد	supplier	UNIFIED002
29	RV000012	2025-08-23	fdkaflkjf;a	50000.00	50000.00	draft	النظام	2025-08-24 02:48:57.565737	2025-08-24 02:48:57.565737	receipt	الصندوق الرئيسي	external	
27	RV000011	2024-01-29	سند قبض من الواجهة المحدثة	8000.00	8000.00	draft	النظام	2025-08-24 02:04:58.908559	2025-08-24 02:04:58.908559	receipt	عميل من الواجهة المحدثة	client	UI001
28	PV000008	2024-01-29	سند صرف من الواجهة المحدثة	3500.00	3500.00	draft	النظام	2025-08-24 02:05:14.323667	2025-08-24 02:05:14.323667	payment	مورد من الواجهة المحدثة	supplier	UI002
30	RV000013	2024-01-30	سند قبض مع استخراج تلقائي للدافع	5000.00	5000.00	draft	النظام	2025-08-24 02:50:42.09996	2025-08-24 02:50:42.09996	receipt	عميل تلقائي	client	AUTO001
31	PV000009	2025-08-23	تجربة سند صرف جديد	80080.00	80080.00	draft	النظام	2025-08-24 02:52:05.295966	2025-08-24 02:52:05.295966	payment	خالد محمد علي	external	
32	RV000014	2025-08-23	تجربة سند قبض جديد	7070.00	7070.00	draft	النظام	2025-08-24 02:55:28.504271	2025-08-24 02:55:28.504271	receipt	بنك سبأ	external	
33	PV000010	2025-08-24	تجربة صند صرف	9090.00	9090.00	draft	النظام	2025-08-24 03:00:39.881765	2025-08-24 03:00:39.881765	payment	سارة أحمد محمد	external	
34	RV000015	2025-08-24	سند قبض 	5050.00	5050.00	draft	النظام	2025-08-24 03:04:17.311245	2025-08-24 03:04:17.311245	receipt	ليلى محمد حسن	external	
35	RV000016	2024-01-30	سند قبض مع استخراج تلقائي محدث	12000.00	12000.00	draft	النظام	2025-08-24 03:12:49.432363	2025-08-24 03:12:49.432363	receipt	عميل تلقائي جديد	client	AUTO002
44	JE000017	2025-08-24	5555	5555.00	5555.00	draft	النظام	2025-08-25 01:11:51.935404	2025-08-25 01:11:51.935404	journal	\N	\N	\N
45	JE000018	2024-02-01	قيد تجريبي مع رموز الحسابات	4000.00	4000.00	draft	النظام	2025-08-25 01:21:03.874821	2025-08-25 01:21:03.874821	journal	\N	\N	\N
46	JE000019	2024-02-02	قيد اختبار نهائي للعرض	1500.00	1500.00	draft	النظام	2025-08-25 01:23:25.272287	2025-08-25 01:23:25.272287	journal	\N	\N	\N
47	RV000017	2025-08-25	تجربة	5565.00	5565.00	draft	النظام	2025-08-26 00:35:27.757729	2025-08-26 00:35:27.757729	receipt	صندوق فرعي	external	
\.


--
-- TOC entry 4732 (class 0 OID 18683)
-- Dependencies: 326
-- Data for Name: journal_entry_details; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.journal_entry_details (id, journal_entry_id, account_id, account_name, debit_amount, credit_amount, description, line_order, account_code) FROM stdin;
15	8	6	النقدية والبنوك	5000.00	0.00	استلام من فاطمة أحمد حسن	1	1110
16	8	52	الإيرادات	0.00	5000.00	إيراد من فاطمة أحمد حسن	2	4000
17	9	6	النقدية والبنوك	15000.00	0.00	استلام من شركة النور للتجارة	1	1110
18	9	52	الإيرادات	0.00	15000.00	إيراد من شركة النور للتجارة	2	4000
19	10	6	النقدية والبنوك	8000.00	0.00	استلام من محمد عبدالله صالح	1	1110
20	10	52	الإيرادات	0.00	8000.00	إيراد من محمد عبدالله صالح	2	4000
21	11	6	النقدية والبنوك	1000.00	0.00	استلام من عميل تجريبي	1	1110
22	11	52	الإيرادات	0.00	1000.00	إيراد من عميل تجريبي	2	4000
23	12	6	النقدية والبنوك	2000.00	0.00	استلام من عميل تجريبي من النموذج	1	1110
24	12	52	الإيرادات	0.00	2000.00	إيراد من عميل تجريبي من النموذج	2	4000
25	13	6	النقدية والبنوك	3000.00	0.00	استلام من عميل تجريبي نهائي	1	1110
26	13	52	الإيرادات	0.00	3000.00	إيراد من عميل تجريبي نهائي	2	4000
27	14	6	النقدية والبنوك	5000.00	0.00	استلام من عميل تجريبي مع قيد	1	1110
28	14	52	الإيرادات	0.00	5000.00	إيراد من عميل تجريبي مع قيد	2	4000
29	15	6	النقدية والبنوك	7500.00	0.00	استلام من عميل تجريبي مع تسجيل	1	1110
30	15	52	الإيرادات	0.00	7500.00	إيراد من عميل تجريبي مع تسجيل	2	4000
31	16	6	النقدية والبنوك	10000.00	0.00	استلام من عميل تجريبي نهائي	1	1110
32	16	52	الإيرادات	0.00	10000.00	إيراد من عميل تجريبي نهائي	2	4000
33	17	19	المصروفات المدفوعة مقدماً	15000.00	0.00	دفع إلى خالد أحمد المحامي	1	1140
34	17	6	النقدية والبنوك	0.00	15000.00	مصروف لـ خالد أحمد المحامي	2	1110
35	18	19	المصروفات المدفوعة مقدماً	2500.00	0.00	دفع إلى شركة الكهرباء	1	1140
36	18	6	النقدية والبنوك	0.00	2500.00	مصروف لـ شركة الكهرباء	2	1110
37	19	19	المصروفات المدفوعة مقدماً	1200.00	0.00	دفع إلى مكتبة القانون	1	1140
38	19	6	النقدية والبنوك	0.00	1200.00	مصروف لـ مكتبة القانون	2	1110
39	20	19	المصروفات المدفوعة مقدماً	2000.00	0.00	دفع إلى اختبار جديد	1	1140
40	20	6	النقدية والبنوك	0.00	2000.00	مصروف لـ اختبار جديد	2	1110
41	21	19	المصروفات المدفوعة مقدماً	1500.00	0.00	دفع إلى مورد تجريبي	1	1140
42	21	6	النقدية والبنوك	0.00	1500.00	مصروف لـ مورد تجريبي	2	1110
43	22	19	المصروفات المدفوعة مقدماً	3000.00	0.00	دفع إلى مورد تجريبي	1	1140
44	22	6	النقدية والبنوك	0.00	3000.00	مصروف لـ مورد تجريبي	2	1110
45	23	6	النقدية والبنوك	5000.00	0.00	استلام نقدية	1	1110
46	23	52	الإيرادات	0.00	5000.00	إيراد أتعاب	2	4000
47	24	19	المصروفات المدفوعة مقدماً	2000.00	0.00	مصروف مكتبية	1	1140
48	24	6	النقدية والبنوك	0.00	2000.00	دفع نقدي	2	1110
53	27	6	النقدية والبنوك	8000.00	0.00	استلام من عميل من الواجهة المحدثة	1	1110
54	27	52	الإيرادات	0.00	8000.00	إيراد من عميل من الواجهة المحدثة	2	4000
55	28	19	المصروفات المدفوعة مقدماً	3500.00	0.00	دفع إلى مورد من الواجهة المحدثة	1	1140
56	28	6	النقدية والبنوك	0.00	3500.00	مصروف لـ مورد من الواجهة المحدثة	2	1110
57	29	123	يوسف حسن أحمد	50000.00	0.00	استلام من الصندوق الرئيسي	1	1121011
58	29	7	الصندوق الرئيسي	0.00	50000.00	إيراد من الصندوق الرئيسي	2	1111
59	30	6	النقدية والبنوك	5000.00	0.00	استلام من عميل تلقائي	1	1110
60	30	52	الإيرادات	0.00	5000.00	إيراد من عميل تلقائي	2	4000
61	31	131	خالد محمد علي	80080.00	0.00	دفع إلى خالد محمد علي	1	1151012
62	31	11	البنك التجاري اليمني	0.00	80080.00	مصروف لـ خالد محمد علي	2	1115
63	32	129	محمد الحاشدي	7070.00	0.00	استلام من بنك سبأ	1	1151005
64	32	10	بنك سبأ	0.00	7070.00	إيراد من بنك سبأ	2	1114
65	33	130	سارة أحمد محمد	9090.00	0.00	دفع إلى سارة أحمد محمد	1	1151011
66	33	7	الصندوق الرئيسي	0.00	9090.00	مصروف لـ سارة أحمد محمد	2	1111
67	34	9	البنك الأهلي اليمني	5050.00	0.00	استلام من ليلى محمد حسن	1	1113
68	34	134	ليلى محمد حسن	0.00	5050.00	إيراد من ليلى محمد حسن	2	1151015
69	35	7	الصندوق الرئيسي	12000.00	0.00	استلام من عميل تلقائي جديد	1	1111
70	35	123	يوسف حسن أحمد	0.00	12000.00	إيراد من عميل تلقائي جديد	2	1121011
87	44	131	خالد محمد علي	5555.00	0.00	تجربة	1	\N
88	44	11	البنك التجاري اليمني	0.00	5555.00	تجربة	2	\N
89	45	6	النقدية والبنوك	4000.00	0.00	مدين - النقدية والبنوك	1	1110
90	45	52	الإيرادات	0.00	4000.00	دائن - الإيرادات	2	4000
91	46	7	الصندوق الرئيسي	1500.00	0.00	مدين - البنوك	1	1111
92	46	54	أتعاب الاستشارات القانونية	0.00	1500.00	دائن - أتعاب الاستشارات القانونية	2	4110
93	47	132	نادية حسن صالح	5565.00	0.00	استلام من صندوق فرعي	1	1151013
94	47	8	صندوق فرعي	0.00	5565.00	إيراد من صندوق فرعي	2	1112
\.


--
-- TOC entry 4701 (class 0 OID 17393)
-- Dependencies: 295
-- Data for Name: lawyer_earnings; Type: TABLE DATA; Schema: public; Owner: yemen
--

COPY public.lawyer_earnings (id, lawyer_id, case_id, service_id, follow_id, allocated_amount, earned_amount, earning_date, notes, created_date) FROM stdin;
\.


--
-- TOC entry 4663 (class 0 OID 16665)
-- Dependencies: 257
-- Data for Name: lineages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.lineages (id, name, created_date, admin_percentage, commission_percentage) FROM stdin;
1	نسب القضايا المدنية	2024-01-01	15.00	0.00
2	نسب القضايا التجارية	2024-01-02	20.00	0.00
3	نسب القضايا الجنائية	2024-01-03	10.00	0.00
4	نسب القضايا العمالية	2024-01-04	25.00	0.00
5	نسب القضايا العقارية	2024-01-05	18.00	0.00
6	نسب القضايا الإدارية	2024-01-06	12.00	0.00
7	نسب القضايا الأسرية	2024-01-07	8.00	0.00
8	نسب القضايا الضريبية	2024-01-08	22.00	0.00
9	نسب القضايا المدنية	2024-01-01	15.00	10.00
10	نسب القضايا التجارية	2024-01-02	20.00	15.00
11	نسب القضايا الجنائية	2024-01-03	10.00	8.00
12	نسب القضايا العمالية	2024-01-04	25.00	20.00
13	نسب القضايا العقارية	2024-01-05	18.00	12.00
\.


--
-- TOC entry 4725 (class 0 OID 17789)
-- Dependencies: 319
-- Data for Name: main_accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.main_accounts (id, account_name, account_code, chart_account_id, is_required, description, created_date, updated_date, table_name, record_id, balance, is_active) FROM stdin;
\.


--
-- TOC entry 4665 (class 0 OID 16672)
-- Dependencies: 259
-- Data for Name: message_read_status; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.message_read_status (id, message_id, reader_type, reader_id, read_at) FROM stdin;
1	1	user	1	2025-07-14 14:07:21.88539
2	3	user	1	2025-07-14 14:07:21.890762
3	2	client	1	2025-08-18 00:44:38.676724
4	4	client	1	2025-08-18 00:44:38.680651
5	6	client	1	2025-08-18 00:44:38.681494
6	5	client	1	2025-08-18 00:44:38.682165
7	19	client	1	2025-08-18 01:24:35.02806
8	20	client	1	2025-08-18 01:25:35.091071
9	21	client	1	2025-08-18 01:26:35.743769
10	22	client	1	2025-08-18 01:26:35.746247
11	23	client	1	2025-08-18 01:26:35.747823
12	24	client	1	2025-08-18 01:27:35.057436
13	25	client	1	2025-08-18 01:27:35.059499
14	27	client	1	2025-08-18 01:28:35.331858
15	29	client	1	2025-08-18 01:29:40.187168
16	31	client	1	2025-08-18 01:29:40.18921
17	33	client	1	2025-08-18 01:29:40.190296
18	35	client	1	2025-08-18 01:29:40.311318
19	37	client	1	2025-08-18 01:30:48.602743
20	39	client	1	2025-08-18 01:32:53.646751
21	41	client	1	2025-08-18 01:33:59.084374
22	43	client	1	2025-08-18 01:36:26.415836
23	45	client	1	2025-08-18 01:41:01.357607
24	47	client	1	2025-08-18 01:41:06.486354
25	49	client	1	2025-08-18 01:41:06.493112
26	51	client	1	2025-08-18 01:41:11.436367
27	53	client	1	2025-08-18 01:41:16.339879
28	38	user	1	2025-08-18 01:44:26.783825
29	40	user	1	2025-08-18 01:44:26.79034
30	7	user	1	2025-08-18 01:44:26.79338
31	9	user	1	2025-08-18 01:44:26.796242
32	44	user	1	2025-08-18 01:44:26.798476
33	46	user	1	2025-08-18 01:44:26.800535
34	11	user	1	2025-08-18 01:44:26.803826
35	13	user	1	2025-08-18 01:44:26.806495
36	15	user	1	2025-08-18 01:44:26.809233
37	8	user	1	2025-08-18 01:44:26.812305
38	26	user	1	2025-08-18 01:44:26.814617
39	42	user	1	2025-08-18 01:44:26.816819
40	28	user	1	2025-08-18 01:44:26.819451
41	30	user	1	2025-08-18 01:44:26.822264
42	32	user	1	2025-08-18 01:44:26.824757
43	34	user	1	2025-08-18 01:44:26.828066
44	48	user	1	2025-08-18 01:44:26.830305
45	50	user	1	2025-08-18 01:44:26.832387
46	36	user	1	2025-08-18 01:44:26.834534
47	52	user	1	2025-08-18 01:44:26.837417
48	61	user	1	2025-08-18 01:45:53.651108
49	62	user	1	2025-08-18 01:45:53.656004
50	54	user	1	2025-08-18 01:45:53.658325
51	55	user	1	2025-08-18 01:45:53.661057
52	56	user	1	2025-08-18 01:45:53.663669
53	57	user	1	2025-08-18 01:45:53.665955
54	58	user	1	2025-08-18 01:45:53.668105
55	59	user	1	2025-08-18 01:45:53.672642
56	60	user	1	2025-08-18 01:45:53.675544
57	63	user	1	2025-08-18 01:45:58.689856
58	64	user	1	2025-08-18 01:45:58.692636
59	65	user	1	2025-08-18 01:45:58.693854
60	66	user	1	2025-08-18 01:45:58.695037
61	67	user	1	2025-08-18 01:46:03.72004
62	68	user	1	2025-08-18 01:46:03.722683
63	69	user	1	2025-08-18 01:46:03.724464
64	70	user	1	2025-08-18 01:46:03.726248
65	71	user	1	2025-08-18 01:46:03.728195
66	72	user	1	2025-08-18 01:46:03.730863
67	73	user	1	2025-08-18 01:46:08.637033
68	74	user	1	2025-08-18 01:51:41.696782
69	75	user	1	2025-08-18 01:51:41.702349
70	76	user	1	2025-08-18 01:51:41.704758
71	77	user	1	2025-08-18 01:51:41.707463
72	78	user	1	2025-08-18 01:51:41.710167
73	79	user	1	2025-08-18 01:51:41.712363
74	80	user	1	2025-08-18 01:51:41.714631
75	81	user	1	2025-08-18 01:51:41.716818
76	82	user	1	2025-08-18 01:51:41.719172
77	83	user	1	2025-08-18 01:51:41.721209
78	85	client	1	2025-08-18 01:58:21.036436
79	90	client	1	2025-08-18 02:03:01.297319
80	92	client	1	2025-08-18 02:03:01.300785
81	94	client	1	2025-08-18 02:03:01.30173
82	96	client	1	2025-08-18 02:03:01.302496
83	98	client	1	2025-08-18 02:03:06.342756
84	99	client	1	2025-08-18 02:03:06.347967
85	100	client	1	2025-08-18 02:03:06.350957
86	101	client	1	2025-08-18 02:03:06.354101
87	103	client	1	2025-08-18 02:08:58.588817
88	105	client	1	2025-08-18 02:09:23.444373
89	107	client	1	2025-08-18 02:09:46.133971
90	109	client	1	2025-08-18 02:12:28.972297
91	111	client	1	2025-08-18 02:12:39.060844
92	113	client	1	2025-08-18 02:13:17.849538
93	115	client	1	2025-08-18 02:18:04.446338
94	108	user	1	2025-08-18 02:23:59.347797
95	110	user	1	2025-08-18 02:23:59.350521
96	102	user	1	2025-08-18 02:23:59.352128
97	86	user	1	2025-08-18 02:23:59.353473
98	87	user	1	2025-08-18 02:23:59.354807
99	104	user	1	2025-08-18 02:23:59.355949
100	88	user	1	2025-08-18 02:23:59.35766
101	89	user	1	2025-08-18 02:23:59.359076
102	91	user	1	2025-08-18 02:23:59.360538
103	84	user	1	2025-08-18 02:23:59.361856
104	93	user	1	2025-08-18 02:23:59.36301
105	95	user	1	2025-08-18 02:23:59.364015
106	97	user	1	2025-08-18 02:23:59.365841
107	114	user	1	2025-08-18 02:23:59.366981
108	112	user	1	2025-08-18 02:23:59.368387
109	106	user	1	2025-08-18 02:23:59.369742
110	119	user	1	2025-08-18 02:34:51.635815
111	120	user	1	2025-08-18 02:35:47.49849
112	123	user	1	2025-08-18 02:38:44.578415
113	124	user	1	2025-08-18 02:39:06.928335
114	125	client	1	2025-08-18 02:44:58.88998
115	117	client	1	2025-08-18 02:44:58.894664
116	118	client	1	2025-08-18 02:44:58.896066
117	126	client	1	2025-08-18 02:44:58.897317
118	121	client	1	2025-08-18 02:44:58.899355
119	122	client	1	2025-08-18 02:44:58.900929
120	116	client	1	2025-08-18 02:44:58.902528
121	128	client	1	2025-08-18 02:45:52.56332
122	130	client	1	2025-08-18 02:46:47.80181
123	133	client	1	2025-08-18 02:50:44.552775
124	134	client	1	2025-08-18 02:50:44.554852
125	136	client	1	2025-08-18 02:51:35.24823
126	140	client	1	2025-08-18 02:55:48.788696
127	142	client	1	2025-08-18 02:56:56.612012
128	144	client	1	2025-08-18 02:57:51.905112
129	146	client	1	2025-08-18 02:59:09.874449
130	148	client	1	2025-08-18 03:00:12.992901
131	150	client	1	2025-08-18 03:02:46.091071
132	152	client	1	2025-08-18 03:03:55.259624
133	154	client	1	2025-08-18 03:04:45.424511
134	156	client	1	2025-08-18 03:05:39.935147
135	129	user	1	2025-08-18 03:09:01.636268
136	138	user	1	2025-08-18 03:09:01.639366
137	135	user	1	2025-08-18 03:09:01.641133
138	143	user	1	2025-08-18 03:09:01.643738
139	147	user	1	2025-08-18 03:09:01.644977
140	131	user	1	2025-08-18 03:09:01.64629
141	155	user	1	2025-08-18 03:09:01.647704
142	145	user	1	2025-08-18 03:09:01.648917
143	127	user	1	2025-08-18 03:09:01.650311
144	137	user	1	2025-08-18 03:09:01.651762
145	132	user	1	2025-08-18 03:09:01.652802
146	157	user	1	2025-08-18 03:09:01.653793
147	151	user	1	2025-08-18 03:09:01.65445
148	141	user	1	2025-08-18 03:09:01.655351
149	139	user	1	2025-08-18 03:09:01.657008
150	149	user	1	2025-08-18 03:09:01.658497
151	158	user	1	2025-08-18 03:09:01.65989
152	153	user	1	2025-08-18 03:09:01.661457
153	163	user	1	2025-08-18 03:22:40.548197
154	164	user	1	2025-08-18 03:22:40.550394
155	166	client	1	2025-08-18 03:26:10.7461
156	165	client	1	2025-08-18 03:26:10.74898
157	161	client	1	2025-08-18 03:26:10.750484
158	159	client	1	2025-08-18 03:26:10.75217
159	162	client	1	2025-08-18 03:26:10.75335
160	160	client	1	2025-08-18 03:26:10.754773
161	168	client	1	2025-08-18 03:26:27.209276
162	170	client	1	2025-08-18 03:26:47.123706
163	172	client	1	2025-08-18 03:27:09.557161
164	174	client	1	2025-08-18 03:27:23.359189
165	176	client	1	2025-08-18 03:28:12.422392
166	178	client	1	2025-08-18 03:29:16.333604
167	180	client	1	2025-08-18 03:30:17.740955
168	184	client	1	2025-08-18 03:50:39.007534
169	186	client	1	2025-08-18 03:52:37.656717
170	188	client	1	2025-08-18 03:54:29.037083
171	190	client	1	2025-08-18 04:14:07.971639
172	192	client	1	2025-08-18 04:15:26.223525
173	195	client	1	2025-08-18 04:27:29.033508
174	197	client	1	2025-08-18 04:29:17.47131
175	173	user	1	2025-08-18 04:32:52.712232
176	182	user	1	2025-08-18 04:32:52.717488
177	185	user	1	2025-08-18 04:32:52.719159
178	187	user	1	2025-08-18 04:32:52.720862
179	189	user	1	2025-08-18 04:32:52.722568
180	193	user	1	2025-08-18 04:32:52.724178
181	196	user	1	2025-08-18 04:32:52.725853
182	177	user	1	2025-08-18 04:32:52.727839
183	175	user	1	2025-08-18 04:32:52.729459
184	181	user	1	2025-08-18 04:32:52.73087
185	167	user	1	2025-08-18 04:32:52.732849
186	179	user	1	2025-08-18 04:32:52.734406
187	183	user	1	2025-08-18 04:32:52.736635
188	191	user	1	2025-08-18 04:32:52.737891
189	169	user	1	2025-08-18 04:32:52.738729
190	171	user	1	2025-08-18 04:32:52.739831
191	194	user	1	2025-08-18 04:32:52.740745
192	198	client	1	2025-08-18 18:51:29.746231
193	202	client	1	2025-08-18 18:52:09.204376
194	201	client	1	2025-08-18 18:52:09.206075
195	204	client	1	2025-08-19 14:43:15.760037
196	199	user	1	2025-08-21 19:54:44.670338
197	203	user	1	2025-08-21 19:54:44.674198
198	200	user	1	2025-08-21 19:54:44.675001
\.


--
-- TOC entry 4667 (class 0 OID 16678)
-- Dependencies: 261
-- Data for Name: messages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.messages (id, conversation_id, sender_type, sender_id, message_text, message_type, file_url, file_name, file_size, reply_to_message_id, is_read, is_edited, created_at, updated_at) FROM stdin;
173	1	client	1	رجل توفي ولدية ثروة عشرة مليون ريال ولدية 2 اولاد وبنت 	text	\N	\N	\N	\N	t	f	2025-08-18 03:27:09.474414	2025-08-18 03:27:09.474414
166	1	user	1	احتاج استشارة قانونية 	text	\N	\N	\N	\N	t	f	2025-08-18 03:24:11.736364	2025-08-18 03:24:11.736364
182	1	client	1	هل سوف نحصل على ردود حقيقية 	text	\N	\N	\N	\N	t	f	2025-08-18 03:32:20.516457	2025-08-18 03:32:20.516457
185	1	client	1	ماهو حكم المظاهرات في اليمن 	text	\N	\N	\N	\N	t	f	2025-08-18 03:52:06.587533	2025-08-18 03:52:06.587533
187	1	client	1	الزكــاة:الحصة المقدرة شرعا في مال المسلم بالشروط المقررة في الشريعة الإسلامية	text	\N	\N	\N	\N	t	f	2025-08-18 03:53:54.689021	2025-08-18 03:53:54.689021
119	1	client	1	أحتاج استشارة قانونية حول حقوق العمال	text	\N	\N	\N	\N	t	f	2025-08-18 02:34:49.436867	2025-08-18 02:34:49.436867
108	1	client	1	اختبار اتجاه التمرير - يجب أن يذهب للأسفل	text	\N	\N	\N	\N	t	f	2025-08-18 02:11:39.450257	2025-08-18 02:11:39.450257
110	1	client	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 02:12:28.742806	2025-08-18 02:12:28.742806
129	1	client	1	احتاج مساعدة في قضية طلاق 	text	\N	\N	\N	\N	t	f	2025-08-18 02:46:12.740756	2025-08-18 02:46:12.740756
125	1	user	1	احتاج استشارة بقضية تجارية 	text	\N	\N	\N	\N	t	f	2025-08-18 02:40:58.856698	2025-08-18 02:40:58.856698
74	1	client	1	رسالة اختبار رقم 1 - للتحقق من التمرير	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:32.829237	2025-08-18 01:51:32.829237
4	1	user	1	سلام 	text	\N	\N	\N	\N	t	f	2025-07-14 14:07:32.801389	2025-07-14 14:07:32.801389
165	1	user	1	هل تعمل الان 	text	\N	\N	\N	\N	t	f	2025-08-18 03:22:40.351635	2025-08-18 03:22:40.351635
5	1	user	1	تستاهل 	text	\N	\N	\N	\N	t	f	2025-07-18 02:22:20.096258	2025-07-18 02:22:20.096258
6	1	user	1	بالله عليك يامحامي 	text	\N	\N	\N	\N	t	f	2025-07-24 03:14:32.419027	2025-07-24 03:14:32.419027
162	1	user	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 03:21:52.472706	2025-08-18 03:21:52.472706
148	1	ai	0	سؤال ممتاز! دعني أوضح لك الأسباب والمنطق وراء ذلك! 💡\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:59:44.044526	2025-08-18 02:59:44.044526
122	1	user	1	حول قضية طلاق 	text	\N	\N	\N	\N	t	f	2025-08-18 02:36:18.701476	2025-08-18 02:36:18.701476
159	1	user	1	هل يمكنك الرد 	text	\N	\N	\N	\N	t	f	2025-08-18 03:09:07.518831	2025-08-18 03:09:07.518831
99	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 6 - لإنشاء محتوى قابل للتمرير في المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:03:01.910019	2025-08-18 02:03:01.910019
47	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "ما هي الإجراءات المطلوبة؟"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:41:02.162768	2025-08-18 01:41:02.162768
105	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "مرحبا"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:09:13.23917	2025-08-18 02:09:13.23917
120	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nأرى أنك تحتاج استشارة قانونية. يمكنني مساعدتك في:\n• فهم الإجراءات القانونية الأساسية\n• توضيح حقوقك وواجباتك\n• توجيهك للمحامي المختص\n\nللحصول على استشارة مفصلة، يرجى التواصل مع أحد محامينا المختصين.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:35:23.240309	2025-08-18 02:35:23.240309
172	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nأستطيع مساعدتك في:\n• الاستشارات القانونية\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدماتنا\n\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:26:50.065938	2025-08-18 03:26:50.065938
41	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "عندي قضية طلاق"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:33:54.738647	2025-08-18 01:33:54.738647
51	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "ما هي التكلفة المتوقعة؟"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:41:08.473053	2025-08-18 01:41:08.473053
75	1	client	1	رسالة اختبار رقم 2 - للتحقق من التمرير	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:33.937839	2025-08-18 01:51:33.937839
84	1	client	1	مرحبا، اختبار الإعدادات الجديدة	text	\N	\N	\N	\N	t	f	2025-08-18 01:58:18.010823	2025-08-18 01:58:18.010823
67	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 7 - تأكيد الحجز"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:59.149632	2025-08-18 01:45:59.149632
11	1	client	1	مرحبا، أحتاج مساعدة قانونية	text	\N	\N	\N	\N	t	f	2025-08-18 01:19:52.220233	2025-08-18 01:19:52.220233
98	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 5 - لإنشاء محتوى قابل للتمرير في المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:03:01.362267	2025-08-18 02:03:01.362267
82	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 4 - للتحقق من التمرير"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:38.147445	2025-08-18 01:51:38.147445
115	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "عندي قضية طلاق"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب\n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:18:01.51572	2025-08-18 02:18:01.51572
54	1	client	1	رسالة رقم 1 - بداية المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:44.477296	2025-08-18 01:45:44.477296
117	1	user	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 02:33:15.290677	2025-08-18 02:33:15.290677
158	1	client	1	وليس حسب السؤال المقدم لك 	text	\N	\N	\N	\N	t	f	2025-08-18 03:06:10.398997	2025-08-18 03:06:10.398997
143	1	client	1	هل تفهم في المواريث 	text	\N	\N	\N	\N	t	f	2025-08-18 02:57:42.149306	2025-08-18 02:57:42.149306
186	1	ai	0	شكراً لثقتكم بي كمحامي يمني متخصص في القانون اليمني والشريعة الإسلامية.\n\nبخصوص سؤالكم حول حكم المظاهرات في اليمن، أود أن أوضح أن المظاهرات هي أحد أشكال التعبير عن الرأي والاحتجاج السلمي الذي يكفلها الدستور اليمني والقانون الدولي.\n\nوفقاً للدستور اليمني، فإن المادة 41 منه تنص على أن "لكل مواطن اليمني الحق في التعبير عن رأيه والاحتجاج السلمي ضمن khuôn الإطار القانوني". كما أن المادة 42 منه تنص على أن "لكل مواطن اليمني الحق في الاجتماع السلمي والتنظيم النقابي".\n\nكما أن القانون اليمني رقم 24 لسنة 1991م بشأن تنظیم الاجتماعات العامة والمتظاهرين ينظم شروط واجراءات المظاهرات في اليمن. وي规定 أن المظاهرات يجب أن تكون سلمية وغير مسلحة، ولا يجب أن تتعارض مع القانون العام أو الآداب العامة أو حقوق الآخرين.\n\nوعلى الرغم من أن المظاهرات مكفولة دستورياً وقانونياً، إلا أن هناك بعض الشروط والقواعد التي يجب اتباعها لضمان سير المظاهرات في إطار قانوني سليم. فعلى سبيل المثال، يجب أن يتم إخطار السلطات المختصة قبل المظاهرة، ويجب أن يتم تنظيم المظاهرة من قبل لجنة تنظيمية مسؤولة، ويجب أن يتم الالتزام بقواعد السلامة العامة والآداب العامة.\n\nفي الختام، أود أن أوضح أن المظاهرات هي حق دستوري مكفول في اليمن، شريطة اتباع الشروط والقواعد القانونية المعمول بها. وينصح العملاء باتخاذ الإجراءات القانونية اللازمة لضمان سير المظاهرات في إطار قانوني سليم.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:52:12.378676	2025-08-18 03:52:12.378676
168	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nأستطيع مساعدتك في:\n• الاستشارات القانونية\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدماتنا\n\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:26:18.47288	2025-08-18 03:26:18.47288
27	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "مرحبا، أحتاج مساعدة قانونية"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:27:41.851589	2025-08-18 01:27:41.851589
73	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 10 - الرسالة الأخيرة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:46:05.443448	2025-08-18 01:46:05.443448
100	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 7 - لإنشاء محتوى قابل للتمرير في المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:03:02.459023	2025-08-18 02:03:02.459023
195	1	ai	0	أهلاً بك سيدي العميل،\n\nشكراً لثقتك بي في طلب استشارة قانونية around this important issue. According to the Yemeni Constitution and the Law of Education, the state is responsible for providing free education to all citizens, as stated in Article 53 of the Constitution: "Education is a right for all citizens, and the state shall provide free education in accordance with the law."\n\nHowever, in practice, the state's ability to provide free education to all students is limited, and many students may not have access to free education. In this case, the student or their guardian may need to bear the costs of education themselves.\n\nIt's worth noting that the Yemeni government has established a number of programs and initiatives to support students from low-income families, such as scholarships and financial aid. These programs aim to ensure that all students have access to education, regardless of their financial situation.\n\nIn summary, while the state has a constitutional obligation to provide free education, the reality is that many students may need to bear some or all of the costs of education themselves. However, there are programs and initiatives in place to support students from low-income families.\n\nIf you have any further questions or would like more information on these programs, please don't hesitate to ask.\n\nنصيحة عملية: إذا كنت طالباً من عائلة ذات دخل منخفض، فيمكنك البحث عن البرامج والمنح الدراسية التي تقدمها الحكومة أو المؤسسات الخاصة ل Supporting students from low-income families.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 04:27:03.17908	2025-08-18 04:27:03.17908
175	1	client	1	مانصيب كل واحد منهم 	text	\N	\N	\N	\N	t	f	2025-08-18 03:27:23.178275	2025-08-18 03:27:23.178275
35	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "كيف يمكنني حجز موعد؟"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:29:17.552838	2025-08-18 01:29:17.552838
116	1	user	1	هل الردود حقيقية 	text	\N	\N	\N	\N	t	f	2025-08-18 02:24:05.331091	2025-08-18 02:24:05.331091
21	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب. كيف يمكنني مساعدتك؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:25:35.125768	2025-08-18 01:25:35.125768
76	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 1 - للتحقق من التمرير"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:34.8376	2025-08-18 01:51:34.8376
178	1	ai	0	سؤال ممتاز! دعني أوضح لك الأسباب والمنطق وراء ذلك! 💡\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:28:49.248452	2025-08-18 03:28:49.248452
163	1	client	1	أحتاج استشارة قانونية حول عقد العمل	text	\N	\N	\N	\N	t	f	2025-08-18 03:22:31.886874	2025-08-18 03:22:31.886874
87	1	client	1	رسالة اختبار رقم 2 - لإنشاء محتوى قابل للتمرير في المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 02:02:57.614605	2025-08-18 02:02:57.614605
189	1	client	1	هل على الدولة ان تتحمل نفقات الدراسة لجميع الطلاب او كل طالب يتحمل نفقاتة لنفسة 	text	\N	\N	\N	\N	t	f	2025-08-18 04:12:49.576432	2025-08-18 04:12:49.576432
193	1	client	1	هل على الدولة ان تتحمل نفقات الدراسة لجميع الطلاب او كل طالب يتحمل نفقاتة لنفسة   ٠٤:١٢ ص	text	\N	\N	\N	\N	t	f	2025-08-18 04:20:47.9143	2025-08-18 04:20:47.9143
48	1	client	1	كم تستغرق القضية؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:41:03.269813	2025-08-18 01:41:03.269813
196	1	client	1	ماهو نصاب زكاة الابل 	text	\N	\N	\N	\N	t	f	2025-08-18 04:28:43.97651	2025-08-18 04:28:43.97651
93	1	client	1	رسالة اختبار رقم 6 - لإنشاء محتوى قابل للتمرير في المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 02:02:59.905279	2025-08-18 02:02:59.905279
72	1	client	1	رسالة رقم 10 - الرسالة الأخيرة	text	\N	\N	\N	\N	t	f	2025-08-18 01:46:03.435771	2025-08-18 01:46:03.435771
123	1	client	1	أحتاج استشارة قانونية حول حقوق العمال في اليمن	text	\N	\N	\N	\N	t	f	2025-08-18 02:38:25.963158	2025-08-18 02:38:25.963158
138	1	client	1	أريد معرفة حقوقي في عقد العمل	text	\N	\N	\N	\N	t	f	2025-08-18 02:54:03.519887	2025-08-18 02:54:03.519887
199	1	client	1	السلام عليكم 	text	\N	\N	\N	\N	t	f	2025-08-18 18:51:34.11597	2025-08-18 18:51:34.11597
97	1	client	1	رسالة اختبار رقم 8 - لإنشاء محتوى قابل للتمرير في المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 02:03:01.001845	2025-08-18 02:03:01.001845
68	1	client	1	رسالة رقم 8 - شكر وتقدير	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:59.225495	2025-08-18 01:45:59.225495
144	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nيمكنني مساعدتك في:\n• الاستشارات القانونية العامة\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدمات المكتب\n\nما هو الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:57:45.240747	2025-08-18 02:57:45.240747
130	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nبخصوص القضايا والمحاكم، نقدم:\n• التمثيل أمام جميع المحاكم\n• إعداد المذكرات القانونية\n• متابعة القضايا\n\nفريقنا من المحامين المختصين جاهز لتمثيلك.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:46:46.192916	2025-08-18 02:46:46.192916
80	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 3 - للتحقق من التمرير"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:37.060314	2025-08-18 01:51:37.060314
43	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "لماذا تقوم بتحريك شريط العرض من الاعلى للاسفل كل ثانية يرجى تثبيت  المحاذاة على اخر الرسائل وعدم عرض الرسائل السابقة الا اذا قمت انا بذلك يدويا "\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:36:25.114091	2025-08-18 01:36:25.114091
170	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nأستطيع مساعدتك في:\n• الاستشارات القانونية\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدماتنا\n\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:26:30.177086	2025-08-18 03:26:30.177086
85	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "مرحبا، اختبار الإعدادات الجديدة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:58:20.028119	2025-08-18 01:58:20.028119
118	1	user	1	هل تعمل الان	text	\N	\N	\N	\N	t	f	2025-08-18 02:34:48.078137	2025-08-18 02:34:48.078137
32	1	client	1	ما هي خدماتكم؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:29:12.273704	2025-08-18 01:29:12.273704
147	1	client	1	لماذا لم تجيب على السؤال 	text	\N	\N	\N	\N	t	f	2025-08-18 02:59:40.906433	2025-08-18 02:59:40.906433
102	1	client	1	اختبار الحل البسيط - هل سيتوقف التمرير المستمر؟	text	\N	\N	\N	\N	t	f	2025-08-18 02:07:06.885043	2025-08-18 02:07:06.885043
61	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 4 - سؤال عن الأسعار"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:52.801097	2025-08-18 01:45:52.801097
103	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "اختبار الحل البسيط - هل سيتوقف التمرير المستمر؟"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:07:08.896598	2025-08-18 02:07:08.896598
177	1	client	1	لماذا ترد بنفس الرد في جميع الحالات	text	\N	\N	\N	\N	t	f	2025-08-18 03:28:45.574156	2025-08-18 03:28:45.574156
192	1	ai	0	📚 **من الملفات القانونية المحلية:**\n\n**من قانون-الضرائب-على-الانتاج-والاستهلاك-والخدمات-وتعديلاته:**\nTitle: قانون  الضرائب على الانتاج والاستهلاك والخدمات وتعديلاته\nSource: https://agoyemen.net/lib_details.php?id=104\nScraped at: Mon Aug 18 02:13:33 2025\n\n---\n\nLANGUGE\nالعربية\nقانون  الضرائب على الانتاج والاستهلاك والخدمات وتعديلاته\nقانون رقم (70) لسنة 1991م بشأن\nالضرائب على الانتاج والاستهلاك والخدمات\n\n---\n\nقانون  الضرائب على الانتاج والاستهلاك والخدمات وتعديلاته\nقانون رقم (70) لسنة 1991م بشأن\nالضرائب على الانتاج والاستهلاك والخدمات\nباسم الشعب:-\nرئيس مجلس الرئاسة:-\n\n==================================================\n\n**من قانون-الجمارك:**\nالباب الثالث\nمبادئ تطبيق التعريفة الجمركية\nمادة(8) تخضع البضائع التي تدخل أراضي الجمهورية أو تخرج منها بأية صورة كانت للرسوم الجمركية المقررة في التعريفة والرسوم والضرائب الأخرى المقررة إلا ما استثني بموجب أحكام هذا القانون أو بموجب اتفاقيات أو نصوص قانونية أخرى.\nمادة(9) تطبق رسوم التعريفة العادية على بضائع جميع الدول مع مراعاة ما يرد في المادتين (13،10)من هذا القانون.\nمادة(10) تطبق رسوم التعريفة التفضيلية على بعض الدول وفق الاتفاقيات المعقودة بهذا الصدد ويشترط في هذه الحالة أن تراعى المصلحة الاقتصادية للجمهورية والمعاملة بالمثل.\n\n---\n\nمادة(21) تطبق التعريفة النافذة على البضائع الخاضعة لرسم نسبي (قيمي)وفق الحالة التي تكون عليها أما البضائع الخاضعة لرسم نوعي (مقطوع)فيستوفى عنها ذلك نتيجة الرسم كاملا بصرف النظر عن حالتها ما لم تتحقق الجمارك من أن تلفا أصابها قوة قاهرة أو حادث طارئ فيخفض مقدار الرسم النوعي بنسبة ما لحق البضاعة من تلف.\nوتحدد نسبة التلف بقرار من رئيس المصلحة أو من يفوضه ويجوز لأصحاب العلاقة الاعتراض على هذا القرار إلى لجنة التحكيم المنصوص عليها في المادة (77) من هذا القانون.\nمادة(22) تطبق أحكام المواد (15-16-17-18-19-20-21) من هذا القانون على جميع الرسوم والضرائب الأخرى التي تستوفيها الجمارك\nما لم يكن ثمة نص مخالف.\nالباب الرابع\n\n---\n\n2-يفترض أن تكون البضاعة مسلمة إلى الشاري في مكان إدخالها الحدود.\n3-يفترض ان يكون البائع قد ضمن الثمن جميع ما انفق على بيعها وتسليمها في مكان إدخالها الحدود.\n4-لا يدخل في مفهوم الثمن العادي نفقات النقل داخل البلاد والرسوم والضرائب المتوجبة على البضاعة بعد إدخالها.\n5-يفترض ان البيع جرى في سوق منافسة حرة بين شار وبائع مستقل احدهما عن الآخر حيث:\nأ.يكون دفع الثمن من الشاري التزامه الفعلي الوحيد تجاه البائع.\n\n📖 **المصادر:** قانون-الضرائب-على-الانتاج-والاستهلاك-والخدمات-وتعديلاته, قانون-الجمارك\n\n\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 04:14:59.019138	2025-08-18 04:14:59.019138
104	1	client	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 02:09:11.228195	2025-08-18 02:09:11.228195
81	1	client	1	رسالة اختبار رقم 5 - للتحقق من التمرير	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:37.275598	2025-08-18 01:51:37.275598
15	1	client	1	مرحبا، هل يمكنك مساعدتي؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:21:34.754804	2025-08-18 01:21:34.754804
7	1	client	1	مرحبا 	text	\N	\N	\N	\N	t	f	2025-08-18 00:44:46.652412	2025-08-18 00:44:46.652412
127	1	client	1	لماذا تتجاهل سؤالي	text	\N	\N	\N	\N	t	f	2025-08-18 02:45:13.150865	2025-08-18 02:45:13.150865
9	1	client	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 01:14:37.178573	2025-08-18 01:14:37.178573
149	1	client	1	توفي الاب اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد منهم 	text	\N	\N	\N	\N	t	f	2025-08-18 03:00:58.821298	2025-08-18 03:00:58.821298
133	1	ai	0	سؤال رائع: "أحتاج استشارة قانونية حول قضية طلاق"\n\nيمكنني مساعدتك في:\n• شرح المفاهيم البرمجية\n• كتابة الكود\n• حل المشاكل\n• تطوير المشاريع\n\nما الذي تحتاج مساعدة فيه تحديداً؟ 🚀\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:50:41.980651	2025-08-18 02:50:41.980651
142	1	ai	0	بخصوص قضايا الطلاق، يمكنني توضيح الآتي:\n\n• الطلاق في القانون اليمني يتم وفقاً لأحكام الشريعة الإسلامية\n• هناك أنواع مختلفة من الطلاق (رجعي، بائن، مبارات)\n• يجب مراعاة حقوق الزوجة والأطفال\n• قد تحتاج لتوثيق الطلاق أمام المحكمة\n\nللحصول على استشارة مفصلة حول حالتك الخاصة، يرجى التواصل مع أحد محامينا المختصين في قضايا الأحوال الشخصية.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:56:48.923888	2025-08-18 02:56:48.923888
113	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "هل التمرير ثابت بالاسفل"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:13:09.272596	2025-08-18 02:13:09.272596
101	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 8 - لإنشاء محتوى قابل للتمرير في المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:03:03.007711	2025-08-18 02:03:03.007711
109	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "اختبار اتجاه التمرير - يجب أن يذهب للأسفل"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:11:41.470419	2025-08-18 02:11:41.470419
23	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب. كيف يمكنني مساعدتك؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:26:23.529699	2025-08-18 01:26:23.529699
13	1	client	1	اختبار الرد التلقائي	text	\N	\N	\N	\N	t	f	2025-08-18 01:20:31.308652	2025-08-18 01:20:31.308652
8	1	client	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 01:11:44.533334	2025-08-18 01:11:44.533334
112	1	client	1	هل التمرير ثابت بالاسفل	text	\N	\N	\N	\N	t	f	2025-08-18 02:13:07.262479	2025-08-18 02:13:07.262479
60	1	client	1	رسالة رقم 4 - سؤال عن الأسعار	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:50.793216	2025-08-18 01:45:50.793216
77	1	client	1	رسالة اختبار رقم 3 - للتحقق من التمرير	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:35.050225	2025-08-18 01:51:35.050225
3	1	client	1	تم فصلي من العمل بدون مبرر واضح، وأريد معرفة حقوقي القانونية	text	\N	\N	\N	\N	t	f	2025-07-14 12:16:50.388589	2025-07-14 12:16:50.388589
203	1	client	1	احتاج استشارة قضائية 	text	\N	\N	\N	\N	t	f	2025-08-19 14:42:44.925788	2025-08-19 14:42:44.925788
36	1	client	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 01:30:46.186868	2025-08-18 01:30:46.186868
38	1	client	1	احتاج الى استشارة قانونية 	text	\N	\N	\N	\N	t	f	2025-08-18 01:32:51.432146	2025-08-18 01:32:51.432146
40	1	client	1	عندي قضية طلاق	text	\N	\N	\N	\N	t	f	2025-08-18 01:33:52.728526	2025-08-18 01:33:52.728526
63	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 5 - طلب موعد"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:54.934768	2025-08-18 01:45:54.934768
164	1	ai	0	بخصوص عقود العمل والعقود القانونية:\n\n• حقوق العامل محمية بالقانون اليمني\n• العقد يجب أن يتضمن الراتب وساعات العمل\n• الإجازات والتأمينات حق للعامل\n• يمكن مراجعة وصياغة العقود\n\nنساعدك في حماية حقوقك القانونية.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:22:34.939148	2025-08-18 03:22:34.939148
2	1	user	1	وعليكم السلام ورحمة الله وبركاته، أهلاً وسهلاً بك. يمكنني مساعدتك في القضية العمالية. ما هي تفاصيل القضية؟	text	\N	\N	\N	\N	t	f	2025-07-14 12:16:50.387279	2025-07-14 12:16:50.387279
161	1	user	1	لماذا لايوجد رد 	text	\N	\N	\N	\N	t	f	2025-08-18 03:11:04.897364	2025-08-18 03:11:04.897364
154	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nيمكنني مساعدتك في:\n• الاستشارات القانونية العامة\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدمات المكتب\n\nما هو الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:04:04.742669	2025-08-18 03:04:04.742669
174	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nأستطيع مساعدتك في:\n• الاستشارات القانونية\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدماتنا\n\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:27:12.570701	2025-08-18 03:27:12.570701
86	1	client	1	رسالة اختبار رقم 1 - لإنشاء محتوى قابل للتمرير في المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 02:02:57.018583	2025-08-18 02:02:57.018583
191	1	client	1	سالت عن نفقات الدراسة لماذا تجيب عن الضرائب 	text	\N	\N	\N	\N	t	f	2025-08-18 04:14:55.071362	2025-08-18 04:14:55.071362
202	1	ai	0	نرحب بك في مكتبنا القانوني! نحن هنا لمساعدتك في أي مشكلة قانونية تواجهك.\n\nما هو موضوع الاستشارة القانونية التي ترغب في مناقشتها؟ هل هي متعلقة بقضية عمل، أو مسائل أحوال شخصية، أو جريمة ما، أو نزاع تجاري، أو ميراث، أو شراكة شركة؟\n\nيرجى ذكر التفاصيل الكافية حول مشكلتك القانونية حتى نستطيع تقديم استشارة قانونية دقيقة ومفصلة تتناسب مع حاجتك.\n\nهنا بعض الأسئلة للتوجيه:\n\n* ما هو نوع القضية أو المشكلة القانونية التي تواجهك؟\n* ما هي الأطراف المعنية في هذه القضية (أي الأشخاص أو الكيانات القانونية)؟\n* ما هي الأحداث أو الأحداث السابقة التي أدت إلى هذه القضية؟\n* ما هو هدفك من هذه الاستشارة القانونية؟\n\nبمجرد تلقينا هذه المعلومات، سنكون قادرين على تقديم استشارة قانونية شاملة ومفصلة تضم:\n\n* شرح القانون اليمني ذي الصلة ( مثل قانون العمل اليمني رقم 5 لسنة 1995، قانون الأحوال الشخصية اليمني، قانون الجرائم والعقوبات اليمني، قانون المرافعات المدنية والتجارية، أحكام الميراث في الشريعة الإسلامية، قانون الشركات والاستثمار اليمني)\n* الإجراءات العملية خطوة بخطوة لاتخاذ الإجراءات اللازمة\n* نصائح عملية قابلة للتطبيق لتصحيح الوضع القانوني\n\nنحن جاهزون لتقديم المساعدة القانونية لك. رجاءً لا تتردد في طرح سؤال أو شرح مشكلتك القانونية.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 18:51:43.748947	2025-08-18 18:51:43.748947
65	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 6 - استفسار عن المواعيد المتاحة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:57.029096	2025-08-18 01:45:57.029096
146	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nيمكنني مساعدتك في:\n• الاستشارات القانونية العامة\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدمات المكتب\n\nما هو الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:58:27.587149	2025-08-18 02:58:27.587149
153	1	client	1	توفي الاب اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد منهم 	text	\N	\N	\N	\N	t	f	2025-08-18 03:04:01.679712	2025-08-18 03:04:01.679712
136	1	ai	0	سؤال رائع: "احتاج استشارة قانونية بخصوص قضية طلاق"\n\nيمكنني مساعدتك في:\n• شرح المفاهيم البرمجية\n• كتابة الكود\n• حل المشاكل\n• تطوير المشاريع\n\nما الذي تحتاج مساعدة فيه تحديداً؟ 🚀\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:51:11.174682	2025-08-18 02:51:11.174682
121	1	user	1	اريد الحصول على استشارة قانونية 	text	\N	\N	\N	\N	t	f	2025-08-18 02:36:05.621199	2025-08-18 02:36:05.621199
20	1	ai	0	اختبار رسالة من الذكاء الاصطناعي	text	\N	\N	\N	\N	t	f	2025-08-18 01:25:14.243438	2025-08-18 01:25:14.243438
57	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 2 - استفسار عن الخدمات"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:48.618999	2025-08-18 01:45:48.618999
1	1	client	1	السلام عليكم، أحتاج استشارة قانونية بخصوص قضية عمالية	text	\N	\N	\N	\N	t	f	2025-07-14 12:16:50.380385	2025-07-14 12:16:50.380385
79	1	client	1	رسالة اختبار رقم 4 - للتحقق من التمرير	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:36.139295	2025-08-18 01:51:36.139295
183	1	client	1	مرحبا 	text	\N	\N	\N	\N	t	f	2025-08-18 03:49:57.655348	2025-08-18 03:49:57.655348
197	1	ai	0	استشارة قانونية: نصاب زكاة الابل\n\nالسلام عليكم،\n\nنصاب زكاة الابل هو أحد أهم مجالات الزكاة في الشريعة الإسلامية، والتي تهدف إلى تحقيق العدالة الاجتماعية والرعاية بالفقراء والمساكين. وفي اليمن، ينظم قانون الأحوال الشخصية اليمني رقم 20 لسنة 1992، أحكام الزكاة بما في ذلك نصاب زكاة الابل.\n\nنصاب زكاة الابل:\n\nيجب على مالك الابل أن يزكي عنها إذا بلغت النصاب المحدد في الشريعة الإسلامية، والذي هو خمسة وعشرون ناقة، فما فوق. ويجب أن يزكي عن كل خمس وعشرين ناقة، بنت لبون (أنثى من النوق تبلغ من العمر سنة واحدة).\n\nالمادة 113 من قانون الأحوال الشخصية اليمني رقم 20 لسنة 1992، تنص على ما يلي:\n\n"الزكاة واجبة في الابل إذا بلغت خمسة وعشرون ناقة، فما فوق، ويجب أن يزكي عن كل خمس وعشرين ناقة بنت لبون".\n\nالإجراءات العملية:\n\n1. حساب عدد النوق: يجب على مالك الابل أن يحسب عدد النوق التي يملكها، ويراجعها كل سنة هجرية.\n2. تحديد النصاب: يجب أن يتحقق مالك الابل من أن عدد النوق يبلغ خمسة وعشرون ناقة أو أكثر.\n3. حساب الزكاة: يجب أن يحسب مالك الابل مقدار الزكاة الواجبة عنه، والتي تكون بنت لبون عن كل خمس وعشرين ناقة.\n4. دفع الزكاة: يجب أن يدفع مالك الابل الزكاة إلى المستحقين لها، وهم الفقراء والمساكين.\n\nنصائح عملية:\n\n* يجب على مالك الابل أن يراجع حساباته كل سنة هجرية لتحديد عدد النوق و مقدار الزكاة الواجبة عنه.\n* يجب أن يلتزم مالك الابل بدفع الزكاة في موعدها، لئلا يتحمل مسؤولية التأخير.\n* يجب أن يختار مالك الابل المستحقين للزكاة بعناية، لئلا يتحمل مسؤولية دفع الزكاة إلى غير المستحقين.\n\nفي النهاية، أتمنى أن تكون هذه الاستشارة القانونية مفيدة لك، وأرجو أن تحدد أي استفسارات أو شكوك لديك.\n\nمع تحياتي القانونية،\n\n[الاسم] محامي يمني خبير متخصص في القانون اليمني والشريعة\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 04:28:50.08161	2025-08-18 04:28:50.08161
90	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 1 - لإنشاء محتوى قابل للتمرير في المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:02:59.028873	2025-08-18 02:02:59.028873
190	1	ai	0	📚 **من الملفات القانونية المحلية:**\n\n**من قانون-الضرائب-على-الانتاج-والاستهلاك-والخدمات-وتعديلاته:**\nTitle: قانون  الضرائب على الانتاج والاستهلاك والخدمات وتعديلاته\nSource: https://agoyemen.net/lib_details.php?id=104\nScraped at: Mon Aug 18 02:13:33 2025\n\n---\n\nLANGUGE\nالعربية\nقانون  الضرائب على الانتاج والاستهلاك والخدمات وتعديلاته\nقانون رقم (70) لسنة 1991م بشأن\nالضرائب على الانتاج والاستهلاك والخدمات\n\n---\n\nقانون  الضرائب على الانتاج والاستهلاك والخدمات وتعديلاته\nقانون رقم (70) لسنة 1991م بشأن\nالضرائب على الانتاج والاستهلاك والخدمات\nباسم الشعب:-\nرئيس مجلس الرئاسة:-\n\n==================================================\n\n**من الدستور_اليمني:**\n\nالمادة (2):\nالإسلام دين الدولة، واللغة العربية لغتها الرسمية.\n\nالمادة (3):\n\n---\n\n...\n\n(هذا نموذج مبسط للدستور اليمني، النسخة الكاملة تحتوي على 306 مادة)\n\n\n📖 **المصادر:** قانون-الضرائب-على-الانتاج-والاستهلاك-والخدمات-وتعديلاته, الدستور_اليمني\n\n\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 04:12:53.571191	2025-08-18 04:12:53.571191
141	1	client	1	وانت هل تستطيع الرد على استفساري بخصوص قضية طلاق 	text	\N	\N	\N	\N	t	f	2025-08-18 02:56:45.839713	2025-08-18 02:56:45.839713
26	1	client	1	مرحبا، أحتاج مساعدة قانونية	text	\N	\N	\N	\N	t	f	2025-08-18 01:27:39.842519	2025-08-18 01:27:39.842519
34	1	client	1	كيف يمكنني حجز موعد؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:29:15.543882	2025-08-18 01:29:15.543882
139	1	client	1	أحتاج استشارة قانونية حول حقوق الموظف	text	\N	\N	\N	\N	t	f	2025-08-18 02:55:38.321897	2025-08-18 02:55:38.321897
46	1	client	1	ما هي الإجراءات المطلوبة؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:41:00.152131	2025-08-18 01:41:00.152131
201	1	ai	0	وعليكم السلام.\n\nأهلاً بك في استشارتنا القانونية. أنا مستعد لتقديم الاستشارة القانونية الدقيقة والمفصلة التي تحتاجها.\n\nما هو موضوع الاستشارة القانونية التي تريد أن تتناولها؟ هل هي متعلقة بالعمل، الأحوال الشخصية، الجرائم والعقوبات، المرافعات المدنية والتجارية، الميراث في الشريعة الإسلامية، أو قانون الشركات والاستثمار؟\n\nيرجى ذكر تفاصيل الاستشارة القانونية التي تريد أن تتناولها، وسأقدم إجابة شاملة ومفصلة مع ذكر المواد القانونية ذات الصلة والإجراءات العملية.\n\nسأكون هنا لتقديم الاستشارة القانونية التي تحتاجها، مع مراعاة قانون اليمن والشريعة الإسلامية.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 18:51:39.980728	2025-08-18 18:51:39.980728
89	1	client	1	رسالة اختبار رقم 4 - لإنشاء محتوى قابل للتمرير في المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 02:02:58.79481	2025-08-18 02:02:58.79481
91	1	client	1	رسالة اختبار رقم 5 - لإنشاء محتوى قابل للتمرير في المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 02:02:59.356678	2025-08-18 02:02:59.356678
66	1	client	1	رسالة رقم 7 - تأكيد الحجز	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:57.137341	2025-08-18 01:45:57.137341
62	1	client	1	رسالة رقم 5 - طلب موعد	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:52.925028	2025-08-18 01:45:52.925028
95	1	client	1	رسالة اختبار رقم 7 - لإنشاء محتوى قابل للتمرير في المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 02:03:00.452386	2025-08-18 02:03:00.452386
33	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "ما هي خدماتكم؟"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:29:14.282459	2025-08-18 01:29:14.282459
176	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nأستطيع مساعدتك في:\n• الاستشارات القانونية\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدماتنا\n\nما الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:27:26.236387	2025-08-18 03:27:26.236387
180	1	ai	0	سأوضح لك المفهوم بطريقة بسيطة. حدد لي الموضوع الذي تريد فهمه! 🤔\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:29:50.103984	2025-08-18 03:29:50.103984
140	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nيمكنني مساعدتك في:\n• الاستشارات القانونية العامة\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدمات المكتب\n\nما هو الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:55:41.96119	2025-08-18 02:55:41.96119
128	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب\n• توجيهك للمحامي المناسب\n\nما هو الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:45:46.796997	2025-08-18 02:45:46.796997
169	1	client	1	احتاج استشارة قانونية 	text	\N	\N	\N	\N	t	f	2025-08-18 03:26:27.123102	2025-08-18 03:26:27.123102
171	1	client	1	احتاج معرفة المواريث 	text	\N	\N	\N	\N	t	f	2025-08-18 03:26:46.99015	2025-08-18 03:26:46.99015
111	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "مرحبا"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:12:30.747789	2025-08-18 02:12:30.747789
59	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 3 - طلب معلومات إضافية"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:50.709763	2025-08-18 01:45:50.709763
53	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "متى يمكنني حجز موعد؟"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:41:11.558177	2025-08-18 01:41:11.558177
114	1	client	1	عندي قضية طلاق	text	\N	\N	\N	\N	t	f	2025-08-18 02:17:27.27439	2025-08-18 02:17:27.27439
151	1	client	1	توفي الاب اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد منهم 	text	\N	\N	\N	\N	t	f	2025-08-18 03:03:50.605521	2025-08-18 03:03:50.605521
96	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 4 - لإنشاء محتوى قابل للتمرير في المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:03:00.801441	2025-08-18 02:03:00.801441
37	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "مرحبا"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:30:48.195834	2025-08-18 01:30:48.195834
45	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "مرحبا، أحتاج استشارة قانونية"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:40:59.066357	2025-08-18 01:40:59.066357
44	1	client	1	مرحبا، أحتاج استشارة قانونية	text	\N	\N	\N	\N	t	f	2025-08-18 01:40:57.053722	2025-08-18 01:40:57.053722
52	1	client	1	متى يمكنني حجز موعد؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:41:09.549316	2025-08-18 01:41:09.549316
150	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nيمكنني مساعدتك في:\n• الاستشارات القانونية العامة\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدمات المكتب\n\nما هو الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:01:01.933467	2025-08-18 03:01:01.933467
156	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nيمكنني مساعدتك في:\n• الاستشارات القانونية العامة\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدمات المكتب\n\nما هو الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:04:56.125247	2025-08-18 03:04:56.125247
194	1	client	1	هل على الدولة ان تتحمل نفقات الدراسة لجميع الطلاب او كل طالب يتحمل نفقاتة لنفسة   ٠٤:١٢ ص  ٠٤:٢٠ ص	text	\N	\N	\N	\N	t	f	2025-08-18 04:26:58.147619	2025-08-18 04:26:58.147619
160	1	user	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 03:09:45.445064	2025-08-18 03:09:45.445064
184	1	ai	0	مرحباً بك! أنا محامي يمني متخصص في القانون اليمني والشريعة الإسلامية. أنا هنا لمساعدتك في أي استشارة قانونية تحتاجها.\n\nما هو السؤال أو الموقف القانوني الذي ترغب في مناقشته؟ هل لديك مشكلة قانونية معينة أو ترغب في معرفة المزيد عن قانون معين في اليمن؟请 توضح لي لكي أستطيع تقديم استشارة قانونية مفيدة لك.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:50:02.186891	2025-08-18 03:50:02.186891
49	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "كم تستغرق القضية؟"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:41:05.27978	2025-08-18 01:41:05.27978
69	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 8 - شكر وتقدير"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:46:01.231853	2025-08-18 01:46:01.231853
94	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 3 - لإنشاء محتوى قابل للتمرير في المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:03:00.21475	2025-08-18 02:03:00.21475
31	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "أحتاج مساعدة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:29:11.013764	2025-08-18 01:29:11.013764
152	1	ai	0	مرحباً! أنا المساعد القانوني للمكتب.\n\nيمكنني مساعدتك في:\n• الاستشارات القانونية العامة\n• شرح الإجراءات القانونية\n• توجيهك للمحامي المناسب\n• معلومات عن خدمات المكتب\n\nما هو الموضوع القانوني الذي تحتاج المساعدة فيه؟\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:03:55.163509	2025-08-18 03:03:55.163509
22	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب. كيف يمكنني مساعدتك؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:25:49.068887	2025-08-18 01:25:49.068887
188	1	ai	0	السلام عليكم,\n\nشكراً لثقتكم بي كمحامي متخصص في القانون اليمني والشريعة الإسلامية. سأقدم لكم استشارة قانونية مفيدة ومحددة حسب سؤالكم حول الزكاة.\n\nالزكاة هي فرض شرعي على كل مسلم يمتلك مالاً يفوق الحد الأدنى (النصاب) ويقوم بتخزينه لمدة سنة قمريّة. الزكاة هي حصة مقدرة شرعاً في مال المسلم بالشروط المقررة في الشريعة الإسلامية، حيث أنها فرض على الأموال النامية مثل المال والذهب والفضة والبضائع والذهب والفضة والanimals.\n\nشروط الزكاة هي:\n\n1. أن يكون المال نामياً أي ينمو ويزداد.\n2. أن يمتلك المال الحد الأدنى (النصاب) وهو 85 جراماً من الذهب أو 595 جراماً من الفضة.\n3. أن يملك المال لمدة سنة قمريّة.\n4. أن يكون المال خالياً من الدين.\n\nنصيب الزكاة هو 2.5% من المال النامي، ويجب دفعها في نهاية السنة القمرية.\n\nويجب أن يدفع المسلم الزكاة إلى المستحقين منهم، مثل الفقراء والمساكين والعبيد والغارمين وسبيل الله.\n\nهذه الاستشارة القانونية المفيدة والمحددة تهدف إلى توفير المعلومات القانونية والشرعية حول الزكاة، ونتمنى أن تكون مفيدة لكم. إذا كان لديكم أي استفسار آخر، فلا تترددوا في الاتصال بنا.\n\nجزاكم الله خيراً.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 03:54:00.725651	2025-08-18 03:54:00.725651
70	1	client	1	رسالة رقم 9 - سؤال إضافي	text	\N	\N	\N	\N	t	f	2025-08-18 01:46:01.331958	2025-08-18 01:46:01.331958
106	1	client	1	هل يمكنني الحصول على استشارة قانونية 	text	\N	\N	\N	\N	t	f	2025-08-18 02:09:33.907463	2025-08-18 02:09:33.907463
64	1	client	1	رسالة رقم 6 - استفسار عن المواعيد المتاحة	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:55.020159	2025-08-18 01:45:55.020159
126	1	user	1	لماذا لم يتم الرد على سؤال ي	text	\N	\N	\N	\N	t	f	2025-08-18 02:42:16.461879	2025-08-18 02:42:16.461879
198	1	user	1	احتاج استشارة قضائية 	text	\N	\N	\N	\N	t	f	2025-08-18 04:33:23.690984	2025-08-18 04:33:23.690984
29	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "مرحبا"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:29:07.702918	2025-08-18 01:29:07.702918
39	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "احتاج الى استشارة قانونية "\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:32:53.438383	2025-08-18 01:32:53.438383
50	1	client	1	ما هي التكلفة المتوقعة؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:41:06.446409	2025-08-18 01:41:06.446409
56	1	client	1	رسالة رقم 2 - استفسار عن الخدمات	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:46.601242	2025-08-18 01:45:46.601242
135	1	client	1	احتاج استشارة قانونية بخصوص قضية طلاق 	text	\N	\N	\N	\N	t	f	2025-08-18 02:51:08.102809	2025-08-18 02:51:08.102809
28	1	client	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 01:29:05.687926	2025-08-18 01:29:05.687926
181	1	client	1	هل اصبحت جاهز للرد	text	\N	\N	\N	\N	t	f	2025-08-18 03:31:30.787377	2025-08-18 03:31:30.787377
167	1	client	1	مرحبا 	text	\N	\N	\N	\N	t	f	2025-08-18 03:26:15.415381	2025-08-18 03:26:15.415381
155	1	client	1	توفي الاب اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد منهم 	text	\N	\N	\N	\N	t	f	2025-08-18 03:04:53.042635	2025-08-18 03:04:53.042635
145	1	client	1	اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد منهم 	text	\N	\N	\N	\N	t	f	2025-08-18 02:58:24.525073	2025-08-18 02:58:24.525073
137	1	client	1	احتاج كود انشاء آلة حاسبة 	text	\N	\N	\N	\N	t	f	2025-08-18 02:52:39.239527	2025-08-18 02:52:39.239527
132	1	client	1	أحتاج استشارة قانونية حول قضية طلاق	text	\N	\N	\N	\N	t	f	2025-08-18 02:50:34.579882	2025-08-18 02:50:34.579882
157	1	client	1	لماذا ترد بردود ثابته 	text	\N	\N	\N	\N	t	f	2025-08-18 03:06:02.996754	2025-08-18 03:06:02.996754
30	1	client	1	أحتاج مساعدة	text	\N	\N	\N	\N	t	f	2025-08-18 01:29:09.003452	2025-08-18 01:29:09.003452
179	1	client	1	ماهو السبب	text	\N	\N	\N	\N	t	f	2025-08-18 03:29:47.042504	2025-08-18 03:29:47.042504
58	1	client	1	رسالة رقم 3 - طلب معلومات إضافية	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:48.703389	2025-08-18 01:45:48.703389
131	1	client	1	مرحبا	text	\N	\N	\N	\N	t	f	2025-08-18 02:50:33.675469	2025-08-18 02:50:33.675469
92	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 2 - لإنشاء محتوى قابل للتمرير في المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:02:59.623182	2025-08-18 02:02:59.623182
124	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nأرى أنك تحتاج استشارة قانونية. يمكنني مساعدتك في:\n• فهم الإجراءات القانونية الأساسية\n• توضيح حقوقك وواجباتك\n• توجيهك للمحامي المختص\n\nللحصول على استشارة مفصلة، يرجى التواصل مع أحد محامينا المختصين.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:38:59.772525	2025-08-18 02:38:59.772525
42	1	client	1	لماذا تقوم بتحريك شريط العرض من الاعلى للاسفل كل ثانية يرجى تثبيت  المحاذاة على اخر الرسائل وعدم عرض الرسائل السابقة الا اذا قمت انا بذلك يدويا 	text	\N	\N	\N	\N	t	f	2025-08-18 01:36:23.100094	2025-08-18 01:36:23.100094
88	1	client	1	رسالة اختبار رقم 3 - لإنشاء محتوى قابل للتمرير في المحادثة	text	\N	\N	\N	\N	t	f	2025-08-18 02:02:58.206301	2025-08-18 02:02:58.206301
71	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 9 - سؤال إضافي"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:46:03.343677	2025-08-18 01:46:03.343677
204	1	ai	0	أهلاً بك في مكتب المحاماة! أنا محامي يمني خبير متخصص في القانون اليمني والشريعة الإسلامية. أنا هنا لمساعدتك في تقديم استشارة قانونية دقيقة ومفصلة.\n\nقبل أن نبدأ، أحب أن أعرف المزيد عن قضيتك. من فضلك، قدم لي المزيد من التفاصيل حول مشكلتك القانونية، مثل:\n\n* ما هو نوع القضية (civil، criminal، family، etc.)؟\n* ما هي الحقائق والملابسات المحيطة بالقضية؟\n* ما هي أهدافك من هذه الاستشارة القانونية؟\n\nبمجرد أن أحصل على هذه المعلومات، سأقدم لك استشارة قانونية شاملة ومفصلة، متضمنة:\n\n1. الشرح القانوني لما هو مؤثق في القضية\n2. الإجراءات العملية الخطوة بخطوة\n3. النصائح العملية القابلة للتطبيق\n4. الاستشهاد بالمواد القانونية ذات الصلة\n\nفيما يلي بعض المواد القانونية ذات الصلة التي قد تكون مرتبطة بقضيتك:\n\n* قانون العمل اليمني رقم 5 لسنة 1995\n* قانون الأحوال الشخصية اليمني\n* قانون الجرائم والعقوبات اليمني\n* قانون المرافعات المدنية والتجارية\n* أحكام الميراث في الشريعة الإسلامية\n* قانون الشركات والاستثمار اليمني\n\nأرجو أن تقدّم لي المزيد من المعلومات عن قضيتك، وسأقدم لك استشارة قانونية مفصلة ومتخصصة.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-19 14:42:50.996357	2025-08-19 14:42:50.996357
200	1	client	1	كيف الحال يامحامي 	text	\N	\N	\N	\N	t	f	2025-08-18 18:51:38.6584	2025-08-18 18:51:38.6584
134	1	ai	0	مرحباً! أنا CodeGeeX، مساعدك في البرمجة. كيف يمكنني مساعدتك اليوم؟ 🚀\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:50:41.996084	2025-08-18 02:50:41.996084
55	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة رقم 1 - بداية المحادثة"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:45:46.489903	2025-08-18 01:45:46.489903
25	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب. كيف يمكنني مساعدتك؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:27:24.857318	2025-08-18 01:27:24.857318
19	1	user	1	اختبار من المستخدم	text	\N	\N	\N	\N	t	f	2025-08-18 01:24:26.537719	2025-08-18 01:24:26.537719
107	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "هل يمكنني الحصول على استشارة قانونية "\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 02:09:35.91648	2025-08-18 02:09:35.91648
83	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 5 - للتحقق من التمرير"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:39.286235	2025-08-18 01:51:39.286235
78	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب.\n\nرسالتك: "رسالة اختبار رقم 2 - للتحقق من التمرير"\n\nيمكنني مساعدتك في:\n• الاستفسارات القانونية العامة\n• معلومات عن خدمات المكتب  \n• توجيهك للمحامي المناسب\n\nللحصول على مشورة قانونية مفصلة، يرجى التواصل مع أحد محامينا.\n\n---\n🤖 المساعد الذكي للمكتب	text	\N	\N	\N	\N	t	f	2025-08-18 01:51:35.94694	2025-08-18 01:51:35.94694
24	1	ai	0	مرحباً! أنا المساعد الذكي للمكتب. كيف يمكنني مساعدتك؟	text	\N	\N	\N	\N	t	f	2025-08-18 01:26:45.053064	2025-08-18 01:26:45.053064
\.


--
-- TOC entry 4669 (class 0 OID 16691)
-- Dependencies: 263
-- Data for Name: money_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.money_transactions (id, account_id, amount_type, amount, currency, transaction_date, user_id, related_id, issue_id, description, reference_number, created_at, updated_at) FROM stdin;
1	23	1	500000.00	rial	2024-01-15	1	1	1	أتعاب قضية تجارية - شركة الأمل	INV-2024-001	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
2	4	-1	500000.00	rial	2024-01-15	1	1	1	إيداع أتعاب في الصندوق	REC-2024-001	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
3	23	1	200000.00	rial	2024-01-20	2	2	2	أتعاب قضية عقارية	INV-2024-002	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
4	4	-1	200000.00	rial	2024-01-20	2	2	2	إيداع أتعاب في الصندوق	REC-2024-002	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
5	23	1	150000.00	rial	2024-02-01	3	3	3	أتعاب قضية تعويضات	INV-2024-003	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
6	26	1	100000.00	rial	2024-03-01	2	6	6	أتعاب استشارة قانونية	INV-2024-004	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
7	30	-1	50000.00	rial	2024-01-31	1	1	\N	راتب شهر يناير - أحمد المحامي	SAL-2024-001	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
8	30	-1	45000.00	rial	2024-01-31	1	2	\N	راتب شهر يناير - فاطمة المحامية	SAL-2024-002	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
9	31	-1	25000.00	rial	2024-02-01	1	\N	\N	إيجار المكتب - شهر فبراير	RENT-2024-002	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
10	32	-1	15000.00	rial	2024-02-01	1	\N	\N	فاتورة الكهرباء والماء	UTIL-2024-002	2025-07-11 23:14:16.209691	2025-07-11 23:14:16.209691
11	23	1	500000.00	rial	2024-01-15	1	1	1	أتعاب قضية تجارية - شركة الأمل	INV-2024-001	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
12	4	-1	500000.00	rial	2024-01-15	1	1	1	إيداع أتعاب في الصندوق	REC-2024-001	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
13	23	1	200000.00	rial	2024-01-20	2	2	2	أتعاب قضية عقارية	INV-2024-002	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
14	4	-1	200000.00	rial	2024-01-20	2	2	2	إيداع أتعاب في الصندوق	REC-2024-002	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
15	23	1	150000.00	rial	2024-02-01	3	3	3	أتعاب قضية تعويضات	INV-2024-003	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
16	26	1	100000.00	rial	2024-03-01	2	6	6	أتعاب استشارة قانونية	INV-2024-004	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
17	30	-1	50000.00	rial	2024-01-31	1	1	\N	راتب شهر يناير - أحمد المحامي	SAL-2024-001	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
18	30	-1	45000.00	rial	2024-01-31	1	2	\N	راتب شهر يناير - فاطمة المحامية	SAL-2024-002	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
19	31	-1	25000.00	rial	2024-02-01	1	\N	\N	إيجار المكتب - شهر فبراير	RENT-2024-002	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
20	32	-1	15000.00	rial	2024-02-01	1	\N	\N	فاتورة الكهرباء والماء	UTIL-2024-002	2025-07-12 00:16:12.96128	2025-07-12 00:16:12.96128
\.


--
-- TOC entry 4671 (class 0 OID 16702)
-- Dependencies: 265
-- Data for Name: movements; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.movements (id, case_id, case_number, movement_type, amount, description, movement_date, status, created_date, updated_at) FROM stdin;
\.


--
-- TOC entry 4673 (class 0 OID 16712)
-- Dependencies: 267
-- Data for Name: navigation_pages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.navigation_pages (id, page_title, page_url, page_description, category, keywords, is_active, created_date, updated_at) FROM stdin;
1	الصفحة الرئيسية	/	الصفحة الرئيسية للنظام	رئيسية	Home	t	2025-08-20	2025-08-20 21:57:33.997678
2	لوحة التحكم	/dashboard	لوحة التحكم الرئيسية	رئيسية	LayoutDashboard	t	2025-08-20	2025-08-20 21:57:33.997678
3	إدارة المحافظات	/governorates	إدارة المحافظات اليمنية	البيانات الأساسية	MapPin	t	2025-08-20	2025-08-20 21:57:33.997678
4	إدارة المحاكم	/courts	إدارة بيانات المحاكم	البيانات الأساسية	Building	t	2025-08-20	2025-08-20 21:57:33.997678
5	إدارة الفروع	/branches	إدارة فروع المكتب	البيانات الأساسية	GitBranch	t	2025-08-20	2025-08-20 21:57:33.997678
6	أنواع القضايا	/issue-types	إدارة أنواع القضايا	البيانات الأساسية	FileText	t	2025-08-20	2025-08-20 21:57:33.997678
7	الخدمات القانونية	/services	إدارة الخدمات القانونية	البيانات الأساسية	Briefcase	t	2025-08-20	2025-08-20 21:57:33.997678
8	إدارة الموظفين	/employees	إدارة بيانات الموظفين	إدارة الأشخاص	Users	t	2025-08-20	2025-08-20 21:57:33.997678
9	إدارة العملاء	/clients	إدارة بيانات العملاء	إدارة الأشخاص	UserCheck	t	2025-08-20	2025-08-20 21:57:33.997678
10	إدارة المستخدمين	/users	إدارة مستخدمي النظام	إدارة الأشخاص	UserCog	t	2025-08-20	2025-08-20 21:57:33.997678
11	إدارة القضايا	/issues	إدارة القضايا القانونية	إدارة القضايا	Scale	t	2025-08-20	2025-08-20 21:57:33.997678
12	إضافة قضية جديدة	/issues/add	إضافة قضية قانونية جديدة	إدارة القضايا	Plus	t	2025-08-20	2025-08-20 21:57:33.997678
13	توزيع القضايا	/case-distribution	توزيع القضايا على المحامين	إدارة القضايا	Shuffle	t	2025-08-20	2025-08-20 21:57:33.997678
14	المتابعات	/follows	متابعة القضايا والمهام	إدارة القضايا	CheckSquare	t	2025-08-20	2025-08-20 21:57:33.997678
15	النظام المحاسبي	/accounting	النظام المحاسبي الرئيسي	المحاسبة	Calculator	t	2025-08-20	2025-08-20 21:57:33.997678
16	دليل الحسابات	/accounting/chart-of-accounts	إدارة دليل الحسابات	المحاسبة	BookOpen	t	2025-08-20	2025-08-20 21:57:33.997678
17	الحسابات الرئيسية	/accounting/main-accounts	إدارة الحسابات الرئيسية	المحاسبة	Link2	t	2025-08-20	2025-08-20 21:57:33.997678
18	الأرصدة الافتتاحية	/opening-balances	إدارة الأرصدة الافتتاحية	المحاسبة	DollarSign	t	2025-08-20	2025-08-20 21:57:33.997678
19	القيود اليومية	/journal-entries-new	إدارة القيود اليومية	المحاسبة	FileEdit	t	2025-08-20	2025-08-20 21:57:33.997678
20	ميزان المراجعة	/trial-balance	ميزان المراجعة	المحاسبة	BarChart3	t	2025-08-20	2025-08-20 21:57:33.997678
21	سندات القبض	/receipt-vouchers	إدارة سندات القبض	السندات والمدفوعات	Receipt	t	2025-08-20	2025-08-20 21:57:33.997678
22	سندات الصرف	/payment-vouchers	إدارة سندات الصرف	السندات والمدفوعات	CreditCard	t	2025-08-20	2025-08-20 21:57:33.997678
23	الفواتير	/invoices	إدارة الفواتير	السندات والمدفوعات	FileText	t	2025-08-20	2025-08-20 21:57:33.997678
24	حسابات العملاء	/client-accounts	حسابات العملاء المالية	السندات والمدفوعات	Wallet	t	2025-08-20	2025-08-20 21:57:33.997678
25	التقارير المالية	/financial-reports	التقارير المالية	التقارير	TrendingUp	t	2025-08-20	2025-08-20 21:57:33.997678
26	تقارير الموظفين	/employee-reports	تقارير الموظفين	التقارير	Users	t	2025-08-20	2025-08-20 21:57:33.997678
27	تقارير القضايا	/case-reports	تقارير القضايا	التقارير	BarChart	t	2025-08-20	2025-08-20 21:57:33.997678
28	أرباح المحامين	/lawyer-earnings	تقارير أرباح المحامين	التقارير	PieChart	t	2025-08-20	2025-08-20 21:57:33.997678
29	إدارة الوثائق	/documents	إدارة الوثائق والملفات	الوثائق والملفات	FolderOpen	t	2025-08-20	2025-08-20 21:57:33.997678
30	رفع الوثائق	/documents/upload	رفع الوثائق الجديدة	الوثائق والملفات	Upload	t	2025-08-20	2025-08-20 21:57:33.997678
31	أرشيف الوثائق	/documents/archive	أرشيف الوثائق	الوثائق والملفات	Archive	t	2025-08-20	2025-08-20 21:57:33.997678
32	بوابة العملاء	/client-portal	بوابة العملاء الإلكترونية	بوابة العملاء	Globe	t	2025-08-20	2025-08-20 21:57:33.997678
33	تسجيل دخول العملاء	/client-login	صفحة تسجيل دخول العملاء	بوابة العملاء	LogIn	t	2025-08-20	2025-08-20 21:57:33.997678
34	إعدادات النظام	/settings	إعدادات النظام العامة	الإعدادات	Settings	t	2025-08-20	2025-08-20 21:57:33.997678
35	إدارة صفحات التنقل	/settings/navigation-pages	إدارة صفحات التنقل	الإعدادات	Navigation	t	2025-08-20	2025-08-20 21:57:33.997678
36	مراكز التكلفة	/settings/cost-centers	إدارة مراكز التكلفة	الإعدادات	Target	t	2025-08-20	2025-08-20 21:57:33.997678
37	الإعلانات	/settings/announcements	إدارة الإعلانات	الإعدادات	Megaphone	t	2025-08-20	2025-08-20 21:57:33.997678
38	إدارة قاعدة البيانات	/admin/database	إدارة قاعدة البيانات	الإدارة	Database	t	2025-08-20	2025-08-20 21:57:33.997678
39	عارض قاعدة البيانات	/admin/database-viewer	عرض بيانات قاعدة البيانات	الإدارة	Eye	t	2025-08-20	2025-08-20 21:57:33.997678
40	إعدادات الذكاء الاصطناعي	/admin/ai-settings	إعدادات الذكاء الاصطناعي	الإدارة	Brain	t	2025-08-20	2025-08-20 21:57:33.997678
41	تتبع الوقت	/time-tracking	تتبع وقت العمل	أخرى	Clock	t	2025-08-20	2025-08-20 21:57:33.997678
42	النسب والعمولات	/percentages	إدارة النسب والعمولات	أخرى	Percent	t	2025-08-20	2025-08-20 21:57:33.997678
43	الحركات المالية	/movements	الحركات المالية	أخرى	ArrowUpDown	t	2025-08-20	2025-08-20 21:57:33.997678
44	بيانات الشركة	/company	إدارة بيانات الشركة	أخرى	Building2	t	2025-08-20	2025-08-20 21:57:33.997678
\.


--
-- TOC entry 4675 (class 0 OID 16721)
-- Dependencies: 269
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notifications (id, recipient_type, recipient_id, sender_type, sender_id, notification_type, title, content, related_id, is_read, created_at) FROM stdin;
1	client	1	user	1	message	رسالة جديدة	سلام 	4	f	2025-07-14 14:07:32.814296
2	client	1	user	1	message	رسالة جديدة	تستاهل 	5	f	2025-07-18 02:22:20.109734
3	client	1	user	1	message	رسالة جديدة	بالله عليك يامحامي 	6	f	2025-07-24 03:14:32.432608
4	user	1	client	1	message	رسالة جديدة	مرحبا 	7	f	2025-08-18 00:44:46.655454
5	user	1	client	1	message	رسالة جديدة	مرحبا	8	f	2025-08-18 01:11:44.543785
6	user	1	client	1	message	رسالة جديدة	مرحبا	9	f	2025-08-18 01:14:37.188235
7	user	1	client	1	message	رسالة جديدة	مرحبا، أحتاج مساعدة قانونية	11	f	2025-08-18 01:19:52.22842
8	user	1	client	1	message	رسالة جديدة	اختبار الرد التلقائي	13	f	2025-08-18 01:20:31.314537
9	user	1	client	1	message	رسالة جديدة	مرحبا، هل يمكنك مساعدتي؟	15	f	2025-08-18 01:21:34.761914
10	client	1	user	1	message	رسالة جديدة	اختبار من المستخدم	19	f	2025-08-18 01:24:26.547474
15	user	1	ai	0	message	رسالة جديدة	مرحباً! أنا المساعد الذكي للمكتب. كيف يمكنني مساعدتك؟	25	f	2025-08-18 01:27:24.864091
16	user	1	client	1	message	رسالة جديدة	مرحبا، أحتاج مساعدة قانونية	26	f	2025-08-18 01:27:39.847869
17	user	1	client	1	message	رسالة جديدة	مرحبا	28	f	2025-08-18 01:29:05.697666
18	user	1	client	1	message	رسالة جديدة	أحتاج مساعدة	30	f	2025-08-18 01:29:09.009993
19	user	1	client	1	message	رسالة جديدة	ما هي خدماتكم؟	32	f	2025-08-18 01:29:12.278499
20	user	1	client	1	message	رسالة جديدة	كيف يمكنني حجز موعد؟	34	f	2025-08-18 01:29:15.548883
21	user	1	client	1	message	رسالة جديدة	مرحبا	36	f	2025-08-18 01:30:46.192242
22	user	1	client	1	message	رسالة جديدة	احتاج الى استشارة قانونية 	38	f	2025-08-18 01:32:51.435198
23	user	1	client	1	message	رسالة جديدة	عندي قضية طلاق	40	f	2025-08-18 01:33:52.735505
24	user	1	client	1	message	رسالة جديدة	لماذا تقوم بتحريك شريط العرض من الاعلى للاسفل كل ثانية يرجى تثبيت  المحاذاة على اخر الرسائل وعدم عرض	42	f	2025-08-18 01:36:23.108908
25	user	1	client	1	message	رسالة جديدة	مرحبا، أحتاج استشارة قانونية	44	f	2025-08-18 01:40:57.062867
26	user	1	client	1	message	رسالة جديدة	ما هي الإجراءات المطلوبة؟	46	f	2025-08-18 01:41:00.158733
27	user	1	client	1	message	رسالة جديدة	كم تستغرق القضية؟	48	f	2025-08-18 01:41:03.276271
28	user	1	client	1	message	رسالة جديدة	ما هي التكلفة المتوقعة؟	50	f	2025-08-18 01:41:06.465131
29	user	1	client	1	message	رسالة جديدة	متى يمكنني حجز موعد؟	52	f	2025-08-18 01:41:09.554289
30	user	1	client	1	message	رسالة جديدة	رسالة رقم 1 - بداية المحادثة	54	f	2025-08-18 01:45:44.485084
31	user	1	client	1	message	رسالة جديدة	رسالة رقم 2 - استفسار عن الخدمات	56	f	2025-08-18 01:45:46.61559
32	user	1	client	1	message	رسالة جديدة	رسالة رقم 3 - طلب معلومات إضافية	58	f	2025-08-18 01:45:48.707598
33	user	1	client	1	message	رسالة جديدة	رسالة رقم 4 - سؤال عن الأسعار	60	f	2025-08-18 01:45:50.798646
34	user	1	client	1	message	رسالة جديدة	رسالة رقم 5 - طلب موعد	62	f	2025-08-18 01:45:52.931135
35	user	1	client	1	message	رسالة جديدة	رسالة رقم 6 - استفسار عن المواعيد المتاحة	64	f	2025-08-18 01:45:55.024907
36	user	1	client	1	message	رسالة جديدة	رسالة رقم 7 - تأكيد الحجز	66	f	2025-08-18 01:45:57.147376
37	user	1	client	1	message	رسالة جديدة	رسالة رقم 8 - شكر وتقدير	68	f	2025-08-18 01:45:59.228163
38	user	1	client	1	message	رسالة جديدة	رسالة رقم 9 - سؤال إضافي	70	f	2025-08-18 01:46:01.340108
39	user	1	client	1	message	رسالة جديدة	رسالة رقم 10 - الرسالة الأخيرة	72	f	2025-08-18 01:46:03.440275
40	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 1 - للتحقق من التمرير	74	f	2025-08-18 01:51:32.835104
41	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 2 - للتحقق من التمرير	75	f	2025-08-18 01:51:33.943097
42	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 3 - للتحقق من التمرير	77	f	2025-08-18 01:51:35.056297
43	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 4 - للتحقق من التمرير	79	f	2025-08-18 01:51:36.14424
44	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 5 - للتحقق من التمرير	81	f	2025-08-18 01:51:37.281852
45	user	1	client	1	message	رسالة جديدة	مرحبا، اختبار الإعدادات الجديدة	84	f	2025-08-18 01:58:18.0231
46	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 1 - لإنشاء محتوى قابل للتمرير في المحادثة	86	f	2025-08-18 02:02:57.025022
47	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 2 - لإنشاء محتوى قابل للتمرير في المحادثة	87	f	2025-08-18 02:02:57.619961
48	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 3 - لإنشاء محتوى قابل للتمرير في المحادثة	88	f	2025-08-18 02:02:58.211508
49	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 4 - لإنشاء محتوى قابل للتمرير في المحادثة	89	f	2025-08-18 02:02:58.798825
50	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 5 - لإنشاء محتوى قابل للتمرير في المحادثة	91	f	2025-08-18 02:02:59.35957
51	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 6 - لإنشاء محتوى قابل للتمرير في المحادثة	93	f	2025-08-18 02:02:59.907745
52	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 7 - لإنشاء محتوى قابل للتمرير في المحادثة	95	f	2025-08-18 02:03:00.45578
53	user	1	client	1	message	رسالة جديدة	رسالة اختبار رقم 8 - لإنشاء محتوى قابل للتمرير في المحادثة	97	f	2025-08-18 02:03:01.005712
54	user	1	client	1	message	رسالة جديدة	اختبار الحل البسيط - هل سيتوقف التمرير المستمر؟	102	f	2025-08-18 02:07:06.892943
55	user	1	client	1	message	رسالة جديدة	مرحبا	104	f	2025-08-18 02:09:11.2349
56	user	1	client	1	message	رسالة جديدة	هل يمكنني الحصول على استشارة قانونية 	106	f	2025-08-18 02:09:33.913374
57	user	1	client	1	message	رسالة جديدة	اختبار اتجاه التمرير - يجب أن يذهب للأسفل	108	f	2025-08-18 02:11:39.464287
58	user	1	client	1	message	رسالة جديدة	مرحبا	110	f	2025-08-18 02:12:28.745772
59	user	1	client	1	message	رسالة جديدة	هل التمرير ثابت بالاسفل	112	f	2025-08-18 02:13:07.269472
60	user	1	client	1	message	رسالة جديدة	عندي قضية طلاق	114	f	2025-08-18 02:17:27.283284
61	client	1	user	1	message	رسالة جديدة	هل الردود حقيقية 	116	f	2025-08-18 02:24:05.338671
62	client	1	user	1	message	رسالة جديدة	مرحبا	117	f	2025-08-18 02:33:15.299945
63	client	1	user	1	message	رسالة جديدة	هل تعمل الان	118	f	2025-08-18 02:34:48.083393
64	user	1	client	1	message	رسالة جديدة	أحتاج استشارة قانونية حول حقوق العمال	119	f	2025-08-18 02:34:49.440631
65	client	1	user	1	message	رسالة جديدة	اريد الحصول على استشارة قانونية 	121	f	2025-08-18 02:36:05.630405
66	client	1	user	1	message	رسالة جديدة	حول قضية طلاق 	122	f	2025-08-18 02:36:18.738146
67	user	1	client	1	message	رسالة جديدة	أحتاج استشارة قانونية حول حقوق العمال في اليمن	123	f	2025-08-18 02:38:25.968124
68	client	1	user	1	message	رسالة جديدة	احتاج استشارة بقضية تجارية 	125	f	2025-08-18 02:40:58.864749
69	client	1	user	1	message	رسالة جديدة	لماذا لم يتم الرد على سؤال ي	126	f	2025-08-18 02:42:16.468654
70	user	1	client	1	message	رسالة جديدة	لماذا تتجاهل سؤالي	127	f	2025-08-18 02:45:13.159298
71	user	1	client	1	message	رسالة جديدة	احتاج مساعدة في قضية طلاق 	129	f	2025-08-18 02:46:12.744181
72	user	1	client	1	message	رسالة جديدة	مرحبا	131	f	2025-08-18 02:50:33.683336
73	user	1	client	1	message	رسالة جديدة	أحتاج استشارة قانونية حول قضية طلاق	132	f	2025-08-18 02:50:34.582582
74	user	1	client	1	message	رسالة جديدة	احتاج استشارة قانونية بخصوص قضية طلاق 	135	f	2025-08-18 02:51:08.108904
75	user	1	client	1	message	رسالة جديدة	احتاج كود انشاء آلة حاسبة 	137	f	2025-08-18 02:52:39.246101
76	user	1	client	1	message	رسالة جديدة	أريد معرفة حقوقي في عقد العمل	138	f	2025-08-18 02:54:03.526388
77	user	1	client	1	message	رسالة جديدة	أحتاج استشارة قانونية حول حقوق الموظف	139	f	2025-08-18 02:55:38.327358
78	user	1	client	1	message	رسالة جديدة	وانت هل تستطيع الرد على استفساري بخصوص قضية طلاق 	141	f	2025-08-18 02:56:45.84554
79	user	1	client	1	message	رسالة جديدة	هل تفهم في المواريث 	143	f	2025-08-18 02:57:42.155308
80	user	1	client	1	message	رسالة جديدة	اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد منهم 	145	f	2025-08-18 02:58:24.529991
81	user	1	client	1	message	رسالة جديدة	لماذا لم تجيب على السؤال 	147	f	2025-08-18 02:59:40.911349
82	user	1	client	1	message	رسالة جديدة	توفي الاب اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد	149	f	2025-08-18 03:00:58.825384
83	user	1	client	1	message	رسالة جديدة	توفي الاب اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد	151	f	2025-08-18 03:03:50.635597
84	user	1	client	1	message	رسالة جديدة	توفي الاب اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد	153	f	2025-08-18 03:04:01.683422
85	user	1	client	1	message	رسالة جديدة	توفي الاب اذا كان عدد الابناء 3 ذكور و2 اناث والزوجة متوفيه والثروة تساوي عشرة مليون كم نصيب كل واحد	155	f	2025-08-18 03:04:53.048679
86	user	1	client	1	message	رسالة جديدة	لماذا ترد بردود ثابته 	157	f	2025-08-18 03:06:03.001987
87	user	1	client	1	message	رسالة جديدة	وليس حسب السؤال المقدم لك 	158	f	2025-08-18 03:06:10.402726
88	client	1	user	1	message	رسالة جديدة	هل يمكنك الرد 	159	f	2025-08-18 03:09:07.558572
89	client	1	user	1	message	رسالة جديدة	مرحبا	160	f	2025-08-18 03:09:45.455304
90	client	1	user	1	message	رسالة جديدة	لماذا لايوجد رد 	161	f	2025-08-18 03:11:04.903412
91	client	1	user	1	message	رسالة جديدة	مرحبا	162	f	2025-08-18 03:21:52.482705
92	user	1	client	1	message	رسالة جديدة	أحتاج استشارة قانونية حول عقد العمل	163	f	2025-08-18 03:22:31.893129
93	client	1	user	1	message	رسالة جديدة	هل تعمل الان 	165	f	2025-08-18 03:22:40.356225
94	client	1	user	1	message	رسالة جديدة	احتاج استشارة قانونية 	166	f	2025-08-18 03:24:11.742053
95	user	1	client	1	message	رسالة جديدة	مرحبا 	167	f	2025-08-18 03:26:15.420095
96	user	1	client	1	message	رسالة جديدة	احتاج استشارة قانونية 	169	f	2025-08-18 03:26:27.127039
97	user	1	client	1	message	رسالة جديدة	احتاج معرفة المواريث 	171	f	2025-08-18 03:26:46.995338
98	user	1	client	1	message	رسالة جديدة	رجل توفي ولدية ثروة عشرة مليون ريال ولدية 2 اولاد وبنت 	173	f	2025-08-18 03:27:09.477476
99	user	1	client	1	message	رسالة جديدة	مانصيب كل واحد منهم 	175	f	2025-08-18 03:27:23.181908
100	user	1	client	1	message	رسالة جديدة	لماذا ترد بنفس الرد في جميع الحالات	177	f	2025-08-18 03:28:45.579642
101	user	1	client	1	message	رسالة جديدة	ماهو السبب	179	f	2025-08-18 03:29:47.045453
102	user	1	client	1	message	رسالة جديدة	هل اصبحت جاهز للرد	181	f	2025-08-18 03:31:30.795413
103	user	1	client	1	message	رسالة جديدة	هل سوف نحصل على ردود حقيقية 	182	f	2025-08-18 03:32:20.521507
104	user	1	client	1	message	رسالة جديدة	مرحبا 	183	f	2025-08-18 03:49:57.665359
105	user	1	client	1	message	رسالة جديدة	ماهو حكم المظاهرات في اليمن 	185	f	2025-08-18 03:52:06.596779
106	user	1	client	1	message	رسالة جديدة	الزكــاة:الحصة المقدرة شرعا في مال المسلم بالشروط المقررة في الشريعة الإسلامية	187	f	2025-08-18 03:53:54.694289
107	user	1	client	1	message	رسالة جديدة	هل على الدولة ان تتحمل نفقات الدراسة لجميع الطلاب او كل طالب يتحمل نفقاتة لنفسة 	189	f	2025-08-18 04:12:49.582897
108	user	1	client	1	message	رسالة جديدة	سالت عن نفقات الدراسة لماذا تجيب عن الضرائب 	191	f	2025-08-18 04:14:55.079453
109	user	1	client	1	message	رسالة جديدة	هل على الدولة ان تتحمل نفقات الدراسة لجميع الطلاب او كل طالب يتحمل نفقاتة لنفسة   ٠٤:١٢ ص	193	f	2025-08-18 04:20:47.926534
110	user	1	client	1	message	رسالة جديدة	هل على الدولة ان تتحمل نفقات الدراسة لجميع الطلاب او كل طالب يتحمل نفقاتة لنفسة   ٠٤:١٢ ص  ٠٤:٢٠ ص	194	f	2025-08-18 04:26:58.154219
111	user	1	client	1	message	رسالة جديدة	ماهو نصاب زكاة الابل 	196	f	2025-08-18 04:28:44.004913
112	client	1	user	1	message	رسالة جديدة	احتاج استشارة قضائية 	198	f	2025-08-18 04:33:23.698601
113	user	1	client	1	message	رسالة جديدة	السلام عليكم 	199	f	2025-08-18 18:51:34.126219
114	user	1	client	1	message	رسالة جديدة	كيف الحال يامحامي 	200	f	2025-08-18 18:51:38.66027
115	user	1	client	1	message	رسالة جديدة	احتاج استشارة قضائية 	203	f	2025-08-19 14:42:44.936076
\.


--
-- TOC entry 4697 (class 0 OID 17340)
-- Dependencies: 291
-- Data for Name: opening_balances; Type: TABLE DATA; Schema: public; Owner: yemen
--

COPY public.opening_balances (id, account_id, debit_balance, credit_balance, balance_date, created_date, updated_at) FROM stdin;
\.


--
-- TOC entry 4677 (class 0 OID 16739)
-- Dependencies: 271
-- Data for Name: opening_balances_history; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.opening_balances_history (id, opening_balance_id, account_name, account_code, old_debit_amount, old_credit_amount, new_debit_amount, new_credit_amount, old_balance_type, new_balance_type, change_type, changed_by, change_reason, change_date) FROM stdin;
1	1	النقدية بالصندوق	1001	0.00	0.00	50000.00	0.00	\N	مدين	INITIAL_IMPORT	النظام	استيراد البيانات الأولية	2025-07-14 02:24:58.942134
2	2	البنك الأهلي	1002	0.00	0.00	150000.00	0.00	\N	مدين	INITIAL_IMPORT	النظام	استيراد البيانات الأولية	2025-07-14 02:24:58.944538
3	3	العملاء	1101	0.00	0.00	75000.00	0.00	\N	مدين	INITIAL_IMPORT	النظام	استيراد البيانات الأولية	2025-07-14 02:24:58.945683
4	4	المخزون	1201	0.00	0.00	25000.00	0.00	\N	مدين	INITIAL_IMPORT	النظام	استيراد البيانات الأولية	2025-07-14 02:24:58.946408
5	5	رأس المال	3001	0.00	0.00	0.00	200000.00	\N	دائن	INITIAL_IMPORT	النظام	استيراد البيانات الأولية	2025-07-14 02:24:58.947775
6	6	الموردين	2001	0.00	0.00	0.00	50000.00	\N	دائن	INITIAL_IMPORT	النظام	استيراد البيانات الأولية	2025-07-14 02:24:58.948465
7	7	مصروفات التأسيس	1301	0.00	0.00	15000.00	0.00	\N	مدين	INITIAL_IMPORT	النظام	استيراد البيانات الأولية	2025-07-14 02:24:58.949232
8	8	إيرادات الخدمات	4001	0.00	0.00	0.00	65000.00	\N	دائن	INITIAL_IMPORT	النظام	استيراد البيانات الأولية	2025-07-14 02:24:58.949767
9	1	النقدية بالصندوق	1001	45000.00	0.00	50000.00	0.00	مدين	مدين	UPDATE	ماجد أحمد	تصحيح رصيد الصندوق بعد الجرد	2025-07-14 02:24:58.950671
10	2	البنك الأهلي	1002	140000.00	0.00	150000.00	0.00	مدين	مدين	UPDATE	محمد صالح	تحديث رصيد البنك حسب كشف الحساب	2025-07-14 02:24:58.951274
\.


--
-- TOC entry 4679 (class 0 OID 16751)
-- Dependencies: 273
-- Data for Name: payment; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.payment (id, reference, payment_class, payment_date, closed, entity_id, employee_id, currency, notes, department_id, gl_id, approved, workflow_id, created_date) FROM stdin;
\.


--
-- TOC entry 4681 (class 0 OID 16761)
-- Dependencies: 275
-- Data for Name: payment_links; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.payment_links (payment_id, entry_id, type) FROM stdin;
\.


--
-- TOC entry 4742 (class 0 OID 18801)
-- Dependencies: 336
-- Data for Name: payment_methods; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.payment_methods (id, method_name, method_code, description, is_active, created_date) FROM stdin;
1	نقداً	CASH	دفع نقدي مباشر	t	2025-08-24 03:33:37.107948
2	شيك	CHECK	دفع بالشيك	t	2025-08-24 03:33:37.110053
3	تحويل بنكي	BANK_TRANSFER	تحويل بنكي	t	2025-08-24 03:33:37.111137
4	بطاقة ائتمان	CREDIT_CARD	دفع بالبطاقة الائتمانية	t	2025-08-24 03:33:37.112766
5	بطاقة خصم	DEBIT_CARD	دفع بالبطاقة المصرفية	t	2025-08-24 03:33:37.113668
6	محفظة إلكترونية	E_WALLET	دفع بالمحفظة الإلكترونية	t	2025-08-24 03:33:37.114375
7	حوالة	MONEY_ORDER	حوالة مالية	t	2025-08-24 03:33:37.115115
8	دفع آجل	DEFERRED	دفع مؤجل	t	2025-08-24 03:33:37.116953
9	قسط	INSTALLMENT	دفع بالأقساط	t	2025-08-24 03:33:37.118769
10	مقايضة	BARTER	مقايضة بخدمة أو سلعة	f	2025-08-24 03:33:37.120673
11	عملة رقمية	CRYPTO	دفع بالعملة الرقمية	f	2025-08-24 03:33:37.122258
\.


--
-- TOC entry 4740 (class 0 OID 18771)
-- Dependencies: 334
-- Data for Name: payment_vouchers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.payment_vouchers (id, entry_number, voucher_number, entry_date, voucher_date, payee_name, payee_type, debit_account_id, credit_account_id, amount, currency_id, payment_method_id, cost_center_id, description, reference_number, case_id, service_id, status, created_by, created_date, updated_at) FROM stdin;
\.


--
-- TOC entry 4756 (class 0 OID 19009)
-- Dependencies: 350
-- Data for Name: permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.permissions (id, permission_key, permission_name, category, description, is_active, created_date) FROM stdin;
1	issues_view	عرض القضايا	القضايا		t	2025-08-27 16:53:41.914574
2	issues_add	إضافة قضية جديدة	القضايا		t	2025-08-27 16:53:41.916785
3	issues_edit	تعديل القضايا	القضايا		t	2025-08-27 16:53:41.918086
4	issues_delete	حذف القضايا	القضايا		t	2025-08-27 16:53:41.919261
5	issues_print	طباعة القضايا	القضايا		t	2025-08-27 16:53:41.920163
6	clients_view	عرض العملاء	العملاء		t	2025-08-27 16:53:41.921272
7	clients_add	إضافة عميل جديد	العملاء		t	2025-08-27 16:53:41.922971
8	clients_edit	تعديل بيانات العملاء	العملاء		t	2025-08-27 16:53:41.924178
9	clients_delete	حذف العملاء	العملاء		t	2025-08-27 16:53:41.925875
10	clients_print	طباعة بيانات العملاء	العملاء		t	2025-08-27 16:53:41.927394
11	follows_view	عرض المتابعات	المتابعات		t	2025-08-27 16:53:41.928441
12	follows_add	إضافة متابعة جديدة	المتابعات		t	2025-08-27 16:53:41.929362
13	follows_edit	تعديل المتابعات	المتابعات		t	2025-08-27 16:53:41.930194
14	follows_delete	حذف المتابعات	المتابعات		t	2025-08-27 16:53:41.931017
15	follows_print	طباعة المتابعات	المتابعات		t	2025-08-27 16:53:41.931824
16	accounting_view	عرض البيانات المحاسبية	المحاسبة		t	2025-08-27 16:53:41.932524
17	accounting_add	إضافة قيود محاسبية	المحاسبة		t	2025-08-27 16:53:41.933157
18	accounting_edit	تعديل القيود المحاسبية	المحاسبة		t	2025-08-27 16:53:41.934048
19	accounting_delete	حذف القيود المحاسبية	المحاسبة		t	2025-08-27 16:53:41.935218
20	accounting_print	طباعة التقارير المحاسبية	المحاسبة		t	2025-08-27 16:53:41.936216
21	vouchers_view	عرض السندات	السندات		t	2025-08-27 16:53:41.937538
22	vouchers_add	إضافة سند جديد	السندات		t	2025-08-27 16:53:41.938846
23	vouchers_edit	تعديل السندات	السندات		t	2025-08-27 16:53:41.939746
24	vouchers_delete	حذف السندات	السندات		t	2025-08-27 16:53:41.940508
25	vouchers_print	طباعة السندات	السندات		t	2025-08-27 16:53:41.941499
26	users_view	عرض المستخدمين	إدارة النظام		t	2025-08-27 16:53:41.942512
27	users_add	إضافة مستخدم جديد	إدارة النظام		t	2025-08-27 16:53:41.943619
28	users_edit	تعديل بيانات المستخدمين	إدارة النظام		t	2025-08-27 16:53:41.945158
29	users_delete	حذف المستخدمين	إدارة النظام		t	2025-08-27 16:53:41.946473
30	users_permissions	إدارة صلاحيات المستخدمين	إدارة النظام		t	2025-08-27 16:53:41.94796
31	reports_view	عرض التقارير	التقارير		t	2025-08-27 16:53:41.949126
32	reports_print	طباعة التقارير	التقارير		t	2025-08-27 16:53:41.950367
33	reports_export	تصدير التقارير	التقارير		t	2025-08-27 16:53:41.951185
34	settings_view	عرض الإعدادات	الإعدادات		t	2025-08-27 16:53:41.95226
35	settings_edit	تعديل إعدادات النظام	الإعدادات		t	2025-08-27 16:53:41.953548
36	website_admin	إدارة الموقع الإلكتروني	الإعدادات		t	2025-08-27 16:53:41.954556
37	courts_view	عرض المحاكم	البيانات الأساسية		t	2025-08-27 16:53:41.95545
38	courts_add	إضافة محكمة جديدة	البيانات الأساسية		t	2025-08-27 16:53:41.956253
39	courts_edit	تعديل بيانات المحاكم	البيانات الأساسية		t	2025-08-27 16:53:41.95707
40	courts_delete	حذف المحاكم	البيانات الأساسية		t	2025-08-27 16:53:41.957938
41	branches_view	عرض الفروع	البيانات الأساسية		t	2025-08-27 16:53:41.958927
42	branches_add	إضافة فرع جديد	البيانات الأساسية		t	2025-08-27 16:53:41.959838
43	branches_edit	تعديل بيانات الفروع	البيانات الأساسية		t	2025-08-27 16:53:41.961265
44	branches_delete	حذف الفروع	البيانات الأساسية		t	2025-08-27 16:53:41.962297
45	employees_view	عرض الموظفين	الموظفين		t	2025-08-27 16:53:41.963039
46	employees_add	إضافة موظف جديد	الموظفين		t	2025-08-27 16:53:41.963911
47	employees_edit	تعديل بيانات الموظفين	الموظفين		t	2025-08-27 16:53:41.964885
48	employees_delete	حذف الموظفين	الموظفين		t	2025-08-27 16:53:41.965854
49	percentages_view	عرض النسب المالية	النسب المالية		t	2025-08-27 16:53:41.966743
50	percentages_add	إضافة نسبة مالية جديدة	النسب المالية		t	2025-08-27 16:53:41.967497
51	percentages_edit	تعديل النسب المالية	النسب المالية		t	2025-08-27 16:53:41.968571
52	percentages_delete	حذف النسب المالية	النسب المالية		t	2025-08-27 16:53:41.96956
53	system_admin	مدير النظام الكامل	صلاحيات خاصة		t	2025-08-27 16:53:41.970534
54	backup_restore	النسخ الاحتياطي والاستعادة	صلاحيات خاصة		t	2025-08-27 16:53:41.971306
55	system_logs	عرض سجلات النظام	صلاحيات خاصة		t	2025-08-27 16:53:41.972282
\.


--
-- TOC entry 4682 (class 0 OID 16772)
-- Dependencies: 276
-- Data for Name: project; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.project (id, projectnumber, description, startdate, enddate, parts_id, production, completed, customer_id, created_date) FROM stdin;
\.


--
-- TOC entry 4744 (class 0 OID 18871)
-- Dependencies: 338
-- Data for Name: public_announcements; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.public_announcements (id, title, content, type, is_active, created_date, updated_date) FROM stdin;
2	افتتاح فرع جديد	نعلن عن افتتاح فرعنا الجديد في المنطقة التجارية لخدمتكم بشكل أفضل	public_2	t	2025-08-25 01:18:12.826558	2025-08-25 01:18:12.826558
1	خصم خاص على الاستشارات القانونية	احصل على خصم 20% على جميع الاستشارات القانونية خلال شهر رمضان المبارك	public_1	f	2025-08-25 01:17:55.141885	2025-08-25 01:17:55.141885
3	يسر مؤسسة الجرافي للمحاماة والاستشارات القانونية تقديم خدماتها للجميع وبكل المجالات القانونية 	يمكنكم التواصل معنا عبر الارقام التالية - *********	public_1	t	2025-08-27 00:24:57.288209	2025-08-27 00:24:57.288209
\.


--
-- TOC entry 4738 (class 0 OID 18755)
-- Dependencies: 332
-- Data for Name: receipt_vouchers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.receipt_vouchers (id, entry_number, entry_date, payer_name, payer_type, debit_account_id, credit_account_id, amount, description, reference_number, status, created_by, created_date, updated_at) FROM stdin;
\.


--
-- TOC entry 4684 (class 0 OID 16781)
-- Dependencies: 278
-- Data for Name: security_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.security_logs (id, user_id, action, ip_address, user_agent, device_id, success, created_at) FROM stdin;
\.


--
-- TOC entry 4686 (class 0 OID 16789)
-- Dependencies: 280
-- Data for Name: service_distributions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.service_distributions (id, case_distribution_id, service_id, percentage, amount, lawyer_id, created_date) FROM stdin;
\.


--
-- TOC entry 4750 (class 0 OID 18959)
-- Dependencies: 344
-- Data for Name: services; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.services (id, name, lineage_id, created_date) FROM stdin;
1	الاستشارات القانونية	1	2025-08-25 23:52:33.225519
2	التمثيل القضائي	1	2025-08-25 23:52:33.225519
3	صياغة العقود	1	2025-08-25 23:52:33.225519
4	قضايا الأحوال الشخصية	1	2025-08-25 23:52:33.225519
5	القضايا التجارية	1	2025-08-25 23:52:33.225519
6	قضايا العمل	1	2025-08-25 23:52:33.225519
\.


--
-- TOC entry 4752 (class 0 OID 18972)
-- Dependencies: 346
-- Data for Name: serviceslow; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.serviceslow (id, title, slug, description, content, icon_name, icon_color, image_url, is_active, sort_order, meta_title, meta_description, created_date, updated_at) FROM stdin;
4	القانون الجنائي والدفاع	criminal-law-defense	دفاع قوي ومتخصص في القضايا الجنائية مع فريق من أمهر المحامين الجنائيين	<h2>خدمات القانون الجنائي والدفاع</h2>\n    <p>نوفر دفاعاً قوياً ومتخصصاً في جميع أنواع القضايا الجنائية مع ضمان حماية حقوقك.</p>\n    <h3>مجالات تخصصنا:</h3>\n    <ul>\n        <li>الجرائم المالية والاقتصادية</li>\n        <li>جرائم الإنترنت والتكنولوجيا</li>\n        <li>القضايا الجنائية العامة</li>\n        <li>جرائم الشركات</li>\n        <li>التحقيقات الجنائية</li>\n        <li>الطعون الجنائية</li>\n    </ul>	Shield	#dc2626	\N	t	4	القانون الجنائي والدفاع - محامي جنائي	دفاع قوي ومتخصص في القضايا الجنائية مع فريق من أمهر المحامين	2025-08-26	2025-08-26 02:40:32.290389
5	قانون العمل والعلاقات الصناعية	labor-industrial-relations	حلول قانونية متكاملة لقضايا العمل وحقوق الموظفين وأصحاب العمل	<h2>خدمات قانون العمل والعلاقات الصناعية</h2>\n    <p>نقدم حلولاً قانونية شاملة لجميع قضايا العمل والعلاقات الصناعية.</p>\n    <h3>خدماتنا تشمل:</h3>\n    <ul>\n        <li>عقود العمل والتوظيف</li>\n        <li>فصل الموظفين والتعويضات</li>\n        <li>النزاعات العمالية</li>\n        <li>حقوق الموظفين</li>\n        <li>سياسات الشركات</li>\n        <li>التأمينات الاجتماعية</li>\n    </ul>	Briefcase	#ea580c	\N	t	5	قانون العمل والعلاقات الصناعية	حلول قانونية متكاملة لقضايا العمل وحقوق الموظفين وأصحاب العمل	2025-08-26	2025-08-26 02:40:32.290389
6	القانون المدني والأحوال الشخصية	civil-personal-status-law	خدمات قانونية شاملة في القضايا المدنية وقضايا الأسرة والميراث	<h2>خدمات القانون المدني والأحوال الشخصية</h2>\n    <p>نتخصص في جميع القضايا المدنية وقضايا الأحوال الشخصية والأسرة.</p>\n    <h3>مجالات خدماتنا:</h3>\n    <ul>\n        <li>قضايا الطلاق والخلع</li>\n        <li>النفقة وحضانة الأطفال</li>\n        <li>قسمة التركات والميراث</li>\n        <li>القضايا المدنية العامة</li>\n        <li>التعويضات والأضرار</li>\n        <li>عقود الزواج</li>\n    </ul>	UserCheck	#0891b2	\N	t	6	القانون المدني والأحوال الشخصية	خدمات قانونية شاملة في القضايا المدنية وقضايا الأسرة والميراث	2025-08-26	2025-08-26 02:40:32.290389
3	قانون الشركات والاستثمار تجربة	qnwn-lshrkt-wlstthmr-tjrb	استشارات قانونية متخصصة في تأسيس الشركات والاستثمار والامتثال التنظيمي	<h2>خدمات قانون الشركات والاستثمار</h2>\n    <p>نقدم استشارات قانونية شاملة للشركات والمستثمرين في جميع مراحل النمو والتطوير.</p>\n    <h3>خدماتنا تشمل:</h3>\n    <ul>\n        <li>تأسيس الشركات بجميع أنواعها</li>\n        <li>هيكلة الاستثمارات</li>\n        <li>الامتثال التنظيمي</li>\n        <li>عمليات الاندماج والاستحواذ</li>\n        <li>حوكمة الشركات</li>\n        <li>قانون الأوراق المالية</li>\n    </ul>	Building	#7c3aed		t	3	قانون الشركات والاستثمار تجربة	استشارات قانونية متخصصة في تأسيس الشركات والاستثمار والامتثال التنظيمي	2025-08-26	2025-08-26 02:52:58.812578
2	صياغة العقود والاتفاقيات	contracts-agreements	إعداد ومراجعة العقود القانونية بأعلى معايير الدقة والاحترافية لحماية مصالحك	<h2>خدمات صياغة العقود والاتفاقيات</h2>\n    <p>نتخصص في إعداد ومراجعة جميع أنواع العقود والاتفاقيات القانونية بأعلى معايير الدقة.</p>\n    <h3>أنواع العقود التي نصيغها:</h3>\n    <ul>\n        <li>عقود البيع والشراء</li>\n        <li>عقود الإيجار والتأجير</li>\n        <li>عقود العمل والتوظيف</li>\n        <li>عقود الشراكة التجارية</li>\n        <li>اتفاقيات السرية</li>\n        <li>عقود الخدمات المهنية</li>\n    </ul>	FileText	#059669	\N	f	2	صياغة العقود والاتفاقيات القانونية	إعداد ومراجعة العقود القانونية بأعلى معايير الدقة والاحترافية	2025-08-26	2025-08-27 00:20:30.805497
1	التقاضي والمرافعات 	ltqdy-wlmrfat-	تمثيل قضائي احترافي أمام جميع المحاكم والدرجات القضائية مع ضمان أفضل النتائج	<h2>خدمات التقاضي والمرافعات</h2>\n    <p>نقدم خدمات تمثيل قضائي شاملة أمام جميع المحاكم والدرجات القضائية، مع فريق من أمهر المحامين المتخصصين.</p>\n    <h3>خدماتنا تشمل:</h3>\n    <ul>\n        <li>التمثيل أمام المحاكم الابتدائية</li>\n        <li>الاستئناف أمام محاكم الاستئناف</li>\n        <li>الطعن أمام المحكمة العليا</li>\n        <li>إعداد المذكرات القانونية</li>\n        <li>جمع الأدلة والشهادات</li>\n    </ul>	Gavel	#c99f08		f	1	التقاضي والمرافعات 	تمثيل قضائي احترافي أمام جميع المحاكم مع ضمان أفضل النتائج القانونية	2025-08-26	2025-08-27 00:47:14.475819
\.


--
-- TOC entry 4728 (class 0 OID 18515)
-- Dependencies: 322
-- Data for Name: suppliers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.suppliers (id, name, company_name, phone, email, address, tax_number, commercial_register, contact_person, payment_terms, credit_limit, current_balance, status, account_id, created_date, updated_at) FROM stdin;
\.


--
-- TOC entry 4688 (class 0 OID 16801)
-- Dependencies: 282
-- Data for Name: test_table; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.test_table (id, name, created_at) FROM stdin;
1	اختبار 1	2025-07-19 16:28:01.149564
2	اختبار 2	2025-07-19 16:28:01.149564
\.


--
-- TOC entry 4709 (class 0 OID 17513)
-- Dependencies: 303
-- Data for Name: time_entries; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.time_entries (id, case_id, client_id, employee_id, start_time, end_time, duration_minutes, task_description, task_category, hourly_rate, billable_amount, is_billable, is_billed, status, notes, created_date, updated_at) FROM stdin;
3	\N	\N	1	2025-08-04 00:21:08.070843	2025-08-04 01:21:08.070843	60	مراجعة الوثائق القانونية	documentation	200.00	200.00	t	f	completed	\N	2025-08-04 03:21:08.070843	2025-08-04 03:21:08.070843
4	\N	\N	1	2025-08-02 03:21:08.070843	2025-08-02 04:51:08.070843	90	اجتماع مع العميل	meeting	200.00	300.00	t	f	completed	\N	2025-08-04 03:21:08.070843	2025-08-04 03:21:08.070843
5	\N	\N	1	2025-08-03 03:21:08.070843	2025-08-03 04:06:08.070843	45	بحث قانوني	research	200.00	150.00	t	f	completed	\N	2025-08-04 03:21:08.070843	2025-08-04 03:21:08.070843
6	\N	\N	1	2025-08-03 23:21:08.070843	2025-08-04 00:21:08.070843	60	إعداد التقارير	documentation	200.00	200.00	t	f	completed	\N	2025-08-04 03:21:08.070843	2025-08-04 03:21:08.070843
7	\N	\N	1	2025-08-03 21:21:08.070843	2025-08-03 22:21:08.070843	60	مراسلات قانونية	correspondence	200.00	200.00	t	f	completed	\N	2025-08-04 03:21:08.070843	2025-08-04 03:21:08.070843
8	\N	\N	1	2025-08-04 00:21:26.538484	2025-08-04 01:21:26.538484	60	مراجعة الوثائق القانونية	documentation	200.00	200.00	t	f	completed	\N	2025-08-04 03:21:26.538484	2025-08-04 03:21:26.538484
9	\N	\N	1	2025-08-02 03:21:26.538484	2025-08-02 04:51:26.538484	90	اجتماع مع العميل	meeting	200.00	300.00	t	f	completed	\N	2025-08-04 03:21:26.538484	2025-08-04 03:21:26.538484
10	\N	\N	1	2025-08-03 03:21:26.538484	2025-08-03 04:06:26.538484	45	بحث قانوني	research	200.00	150.00	t	f	completed	\N	2025-08-04 03:21:26.538484	2025-08-04 03:21:26.538484
11	\N	\N	1	2025-08-03 23:21:26.538484	2025-08-04 00:21:26.538484	60	إعداد التقارير	documentation	200.00	200.00	t	f	completed	\N	2025-08-04 03:21:26.538484	2025-08-04 03:21:26.538484
12	\N	\N	1	2025-08-03 21:21:26.538484	2025-08-03 22:21:26.538484	60	مراسلات قانونية	correspondence	200.00	200.00	t	f	completed	\N	2025-08-04 03:21:26.538484	2025-08-04 03:21:26.538484
13	\N	\N	1	2025-08-04 00:22:20.713206	2025-08-04 01:22:20.713206	60	مراجعة الوثائق القانونية	documentation	200.00	200.00	t	f	completed	\N	2025-08-04 03:22:20.713206	2025-08-04 03:22:20.713206
14	\N	\N	1	2025-08-02 03:22:20.713206	2025-08-02 04:52:20.713206	90	اجتماع مع العميل	meeting	200.00	300.00	t	f	completed	\N	2025-08-04 03:22:20.713206	2025-08-04 03:22:20.713206
15	\N	\N	1	2025-08-03 03:22:20.713206	2025-08-03 04:07:20.713206	45	بحث قانوني	research	200.00	150.00	t	f	completed	\N	2025-08-04 03:22:20.713206	2025-08-04 03:22:20.713206
16	\N	\N	1	2025-08-03 23:22:20.713206	2025-08-04 00:22:20.713206	60	إعداد التقارير	documentation	200.00	200.00	t	f	completed	\N	2025-08-04 03:22:20.713206	2025-08-04 03:22:20.713206
17	\N	\N	1	2025-08-03 21:22:20.713206	2025-08-03 22:22:20.713206	60	مراسلات قانونية	correspondence	200.00	200.00	t	f	completed	\N	2025-08-04 03:22:20.713206	2025-08-04 03:22:20.713206
18	\N	\N	1	2025-08-04 02:52:20.713206	\N	\N	مراجعة ملف قضية جديدة	documentation	200.00	0.00	t	f	active	\N	2025-08-04 03:22:20.713206	2025-08-04 03:22:20.713206
\.


--
-- TOC entry 4690 (class 0 OID 16806)
-- Dependencies: 284
-- Data for Name: timecard; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.timecard (id, employee_id, project_id, business_unit_id, transdate, description, qty, sellprice, fxsellprice, curr, allocated, notes, jctype, total, non_billable, created_date) FROM stdin;
\.


--
-- TOC entry 4758 (class 0 OID 19022)
-- Dependencies: 352
-- Data for Name: user_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_permissions (id, user_id, permission_key, granted_by, granted_date, is_active) FROM stdin;
1	5	issues_view	5	2025-08-27 16:53:41.981862	t
2	5	issues_add	5	2025-08-27 16:53:41.98396	t
3	5	issues_edit	5	2025-08-27 16:53:41.984976	t
4	5	issues_delete	5	2025-08-27 16:53:41.985808	t
5	5	issues_print	5	2025-08-27 16:53:41.986493	t
6	5	clients_view	5	2025-08-27 16:53:41.987208	t
7	5	clients_add	5	2025-08-27 16:53:41.987943	t
8	5	clients_edit	5	2025-08-27 16:53:41.988724	t
9	5	clients_delete	5	2025-08-27 16:53:41.989294	t
10	5	clients_print	5	2025-08-27 16:53:41.989952	t
11	5	follows_view	5	2025-08-27 16:53:41.990635	t
12	5	follows_add	5	2025-08-27 16:53:41.991235	t
13	5	follows_edit	5	2025-08-27 16:53:41.992002	t
14	5	follows_delete	5	2025-08-27 16:53:41.992873	t
15	5	follows_print	5	2025-08-27 16:53:41.993898	t
16	5	accounting_view	5	2025-08-27 16:53:41.995097	t
17	5	accounting_add	5	2025-08-27 16:53:41.996155	t
18	5	accounting_edit	5	2025-08-27 16:53:41.997303	t
19	5	accounting_delete	5	2025-08-27 16:53:41.998442	t
20	5	accounting_print	5	2025-08-27 16:53:41.999348	t
21	5	vouchers_view	5	2025-08-27 16:53:42.000231	t
22	5	vouchers_add	5	2025-08-27 16:53:42.001406	t
23	5	vouchers_edit	5	2025-08-27 16:53:42.002326	t
24	5	vouchers_delete	5	2025-08-27 16:53:42.003187	t
25	5	vouchers_print	5	2025-08-27 16:53:42.004084	t
26	5	users_view	5	2025-08-27 16:53:42.005075	t
27	5	users_add	5	2025-08-27 16:53:42.006079	t
28	5	users_edit	5	2025-08-27 16:53:42.006929	t
29	5	users_delete	5	2025-08-27 16:53:42.007979	t
30	5	users_permissions	5	2025-08-27 16:53:42.008753	t
31	5	reports_view	5	2025-08-27 16:53:42.00952	t
32	5	reports_print	5	2025-08-27 16:53:42.010261	t
33	5	reports_export	5	2025-08-27 16:53:42.010967	t
34	5	settings_view	5	2025-08-27 16:53:42.011801	t
35	5	settings_edit	5	2025-08-27 16:53:42.012528	t
36	5	website_admin	5	2025-08-27 16:53:42.013347	t
37	5	courts_view	5	2025-08-27 16:53:42.014279	t
38	5	courts_add	5	2025-08-27 16:53:42.015085	t
39	5	courts_edit	5	2025-08-27 16:53:42.015897	t
40	5	courts_delete	5	2025-08-27 16:53:42.01675	t
41	5	branches_view	5	2025-08-27 16:53:42.017515	t
42	5	branches_add	5	2025-08-27 16:53:42.018333	t
43	5	branches_edit	5	2025-08-27 16:53:42.019197	t
44	5	branches_delete	5	2025-08-27 16:53:42.019909	t
45	5	employees_view	5	2025-08-27 16:53:42.020607	t
46	5	employees_add	5	2025-08-27 16:53:42.021521	t
47	5	employees_edit	5	2025-08-27 16:53:42.022514	t
48	5	employees_delete	5	2025-08-27 16:53:42.023519	t
49	5	percentages_view	5	2025-08-27 16:53:42.024466	t
50	5	percentages_add	5	2025-08-27 16:53:42.025741	t
51	5	percentages_edit	5	2025-08-27 16:53:42.026686	t
52	5	percentages_delete	5	2025-08-27 16:53:42.027462	t
53	5	system_admin	5	2025-08-27 16:53:42.028098	t
54	5	backup_restore	5	2025-08-27 16:53:42.028738	t
55	5	system_logs	5	2025-08-27 16:53:42.029448	t
\.


--
-- TOC entry 4760 (class 0 OID 19043)
-- Dependencies: 354
-- Data for Name: user_role_assignments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_role_assignments (id, user_id, role_name, assigned_by, assigned_date, is_active) FROM stdin;
1	1	admin	1	2025-08-27 17:15:07.659398	t
2	5	admin	5	2025-08-27 17:15:07.661828	t
\.


--
-- TOC entry 4723 (class 0 OID 17724)
-- Dependencies: 317
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_roles (id, role_name, display_name, description, permissions, is_active, created_date) FROM stdin;
7	lawyer	محامي	محامي يمكنه إدارة القضايا والمتابعات	{issues_view,issues_add,issues_edit,clients_view,follows_view,follows_add,reports_view}	t	2025-08-27 16:53:41.975348
8	secretary	سكرتير	سكرتير يمكنه إدارة المتابعات والعملاء	{clients_view,clients_add,clients_edit,follows_view,follows_add,issues_view}	t	2025-08-27 16:53:41.976863
9	accountant	محاسب	محاسب يمكنه إدارة الحسابات والسندات	{accounting_view,accounting_add,accounting_edit,vouchers_view,vouchers_add,vouchers_edit,reports_view}	t	2025-08-27 16:53:41.977619
10	viewer	مستعرض	مستخدم يمكنه عرض البيانات فقط	{issues_view,clients_view,follows_view,reports_view}	t	2025-08-27 16:53:41.978309
11	secretary_accountant	سكرتير + محاسب	دور مختلط يجمع بين السكرتارية والمحاسبة	{clients_view,clients_add,clients_edit,follows_view,follows_add,follows_edit,accounting_view,accounting_add,accounting_edit,vouchers_view,vouchers_add,vouchers_edit,reports_view}	t	2025-08-27 17:15:07.65013
12	lawyer_manager	محامي + مدير	محامي مع صلاحيات إدارية	{issues_view,issues_add,issues_edit,clients_view,clients_add,clients_edit,follows_view,follows_add,follows_edit,users_view,reports_view,reports_print}	t	2025-08-27 17:15:07.652578
13	senior_secretary	سكرتير أول	سكرتير مع صلاحيات إضافية	{clients_view,clients_add,clients_edit,follows_view,follows_add,follows_edit,issues_view,reports_view,courts_view,branches_view}	t	2025-08-27 17:15:07.654296
14	financial_manager	مدير مالي	مدير مالي مع صلاحيات شاملة	{accounting_view,accounting_add,accounting_edit,vouchers_view,vouchers_add,vouchers_edit,reports_view,reports_print,reports_export,percentages_view,percentages_add,percentages_edit}	t	2025-08-27 17:15:07.655496
6	manager	مدير	\N	{all}	t	2025-08-20 02:32:27.763973
1	admin	مدير النظام	\N	{issues_view,issues_add,issues_edit,issues_delete,issues_print,clients_view,clients_add,clients_edit,clients_delete,clients_print,follows_view,follows_add,follows_edit,follows_delete,follows_print,accounting_view,accounting_add,accounting_edit,accounting_delete,accounting_print,vouchers_view,vouchers_add,vouchers_edit,vouchers_delete,vouchers_print,users_view,users_add,users_edit,users_delete,users_permissions,reports_view,reports_print,reports_export,settings_view,settings_edit,website_admin,courts_view,courts_add,courts_edit,courts_delete,branches_view,branches_add,branches_edit,branches_delete,employees_view,employees_add,employees_edit,employees_delete,percentages_view,percentages_add,percentages_edit,percentages_delete,system_admin,backup_restore,system_logs}	t	2025-08-20 02:03:22.411456
\.


--
-- TOC entry 4692 (class 0 OID 16816)
-- Dependencies: 286
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, username, email, password_hash, status, last_login, created_date, updated_at, device_id, employee_id, is_active, last_logout, last_failed_login, is_online, login_attempts, locked_until, role, permissions, user_type) FROM stdin;
1	old_admin	<EMAIL>	$2b$10$e4UGPyyP1N3bCrUAj0BjeeaRmQeQJRMxTrsNCv9FSzy7MWsb.Htpq	active	2025-08-20 02:22:51.908759	2025-08-20	2025-08-20 02:06:34.162209	\N	\N	t	\N	\N	t	0	\N	admin	{all}	admin
5	admin	<EMAIL>	$2b$10$uBpHup2dyAMojDwEeHNbxe.3w57GEsYT0.kSmRGg6MlPGF.XSL1.C	active	2025-08-27 17:09:05.87784	2025-08-20	2025-08-20 03:21:11.553448	\N	5	t	\N	\N	t	0	\N	admin	{all}	admin
\.


--
-- TOC entry 4694 (class 0 OID 16828)
-- Dependencies: 288
-- Data for Name: vouchers; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.vouchers (id, voucher_number, voucher_type, voucher_date, description, total_amount, reference_number, status, created_by, created_date) FROM stdin;
\.


--
-- TOC entry 4748 (class 0 OID 18928)
-- Dependencies: 342
-- Data for Name: website_services; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.website_services (id, title, slug, description, content, icon_name, icon_color, image_url, is_active, sort_order, meta_title, meta_description, created_date, updated_date) FROM stdin;
1	الاستشارات القانونية	legal-consultation	استشارات قانونية شاملة في جميع فروع القانون	<h2>الاستشارات القانونية</h2><p>نقدم استشارات قانونية متخصصة وشاملة في جميع فروع القانون لمساعدتكم في اتخاذ القرارات الصحيحة.</p><h3>خدماتنا تشمل:</h3><ul><li>استشارات في القانون المدني والتجاري</li><li>استشارات في قانون العمل والضمان الاجتماعي</li><li>استشارات في قانون الأحوال الشخصية</li><li>استشارات في القانون الجنائي</li><li>استشارات في القانون الإداري</li></ul>	Scale	#2563eb	\N	t	1	\N	\N	2025-08-25 22:48:10.841476	2025-08-25 22:48:10.841476
2	التمثيل القضائي	court-representation	تمثيل قانوني محترف أمام جميع المحاكم والجهات القضائية	<h2>التمثيل القضائي</h2><p>نوفر خدمات التمثيل القضائي المحترف أمام جميع درجات المحاكم والجهات القضائية.</p><h3>خدماتنا تشمل:</h3><ul><li>التمثيل أمام المحاكم الابتدائية</li><li>التمثيل أمام محاكم الاستئناف</li><li>التمثيل أمام المحكمة العليا</li><li>التمثيل في القضايا التجارية</li><li>التمثيل في القضايا الجنائية</li></ul>	Gavel	#dc2626	\N	t	2	\N	\N	2025-08-25 22:48:10.841476	2025-08-25 22:48:10.841476
3	صياغة العقود	contract-drafting	صياغة ومراجعة العقود والاتفاقيات القانونية	<h2>صياغة العقود</h2><p>نقدم خدمات صياغة ومراجعة العقود والاتفاقيات القانونية بأعلى معايير الدقة والاحترافية.</p><h3>أنواع العقود:</h3><ul><li>عقود البيع والشراء</li><li>عقود الإيجار</li><li>عقود العمل</li><li>عقود الشراكة</li><li>عقود الخدمات</li></ul>	FileText	#059669	\N	t	3	\N	\N	2025-08-25 22:48:10.841476	2025-08-25 22:48:10.841476
4	قضايا الأحوال الشخصية	family-law	خدمات قانونية متخصصة في قضايا الأسرة والأحوال الشخصية	<h2>قضايا الأحوال الشخصية</h2><p>نتخصص في التعامل مع قضايا الأحوال الشخصية والأسرة بحساسية واحترافية عالية.</p><h3>خدماتنا تشمل:</h3><ul><li>قضايا الطلاق والخلع</li><li>قضايا النفقة والحضانة</li><li>قضايا الميراث</li><li>عقود الزواج</li><li>قضايا النسب</li></ul>	Users	#7c3aed	\N	t	4	\N	\N	2025-08-25 22:48:10.841476	2025-08-25 22:48:10.841476
5	القضايا التجارية	commercial-law	حلول قانونية للشركات والمؤسسات التجارية	<h2>القضايا التجارية</h2><p>نقدم حلولاً قانونية شاملة للشركات والمؤسسات التجارية في جميع المجالات.</p><h3>خدماتنا تشمل:</h3><ul><li>تأسيس الشركات</li><li>العقود التجارية</li><li>قضايا الإفلاس</li><li>حقوق الملكية الفكرية</li><li>المنازعات التجارية</li></ul>	Building	#ea580c	\N	t	5	\N	\N	2025-08-25 22:48:10.841476	2025-08-25 22:48:10.841476
6	قضايا العمل	labor-law	حماية حقوق العمال وأصحاب العمل	<h2>قضايا العمل</h2><p>نحمي حقوق العمال وأصحاب العمل ونقدم الاستشارات في جميع مسائل قانون العمل.</p><h3>خدماتنا تشمل:</h3><ul><li>عقود العمل</li><li>قضايا الفصل التعسفي</li><li>المنازعات العمالية</li><li>حقوق العمال</li><li>التأمينات الاجتماعية</li></ul>	Shield	#0891b2	\N	t	6	\N	\N	2025-08-25 22:48:10.841476	2025-08-25 22:48:10.841476
\.


--
-- TOC entry 4852 (class 0 OID 0)
-- Dependencies: 212
-- Name: acc_trans_entry_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.acc_trans_entry_id_seq', 1, false);


--
-- TOC entry 4853 (class 0 OID 0)
-- Dependencies: 329
-- Name: accounting_transaction_details_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.accounting_transaction_details_id_seq', 37, true);


--
-- TOC entry 4854 (class 0 OID 0)
-- Dependencies: 327
-- Name: accounting_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.accounting_transactions_id_seq', 19, true);


--
-- TOC entry 4855 (class 0 OID 0)
-- Dependencies: 214
-- Name: accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.accounts_id_seq', 32, true);


--
-- TOC entry 4856 (class 0 OID 0)
-- Dependencies: 216
-- Name: announcements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.announcements_id_seq', 3, true);


--
-- TOC entry 4857 (class 0 OID 0)
-- Dependencies: 218
-- Name: ar_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.ar_id_seq', 1, false);


--
-- TOC entry 4858 (class 0 OID 0)
-- Dependencies: 220
-- Name: branches_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.branches_id_seq', 4, true);


--
-- TOC entry 4859 (class 0 OID 0)
-- Dependencies: 222
-- Name: budget_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.budget_id_seq', 1, false);


--
-- TOC entry 4860 (class 0 OID 0)
-- Dependencies: 224
-- Name: case_distribution_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.case_distribution_id_seq', 1, false);


--
-- TOC entry 4861 (class 0 OID 0)
-- Dependencies: 226
-- Name: chart_of_accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.chart_of_accounts_id_seq', 144, true);


--
-- TOC entry 4862 (class 0 OID 0)
-- Dependencies: 310
-- Name: client_notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.client_notifications_id_seq', 6, true);


--
-- TOC entry 4863 (class 0 OID 0)
-- Dependencies: 308
-- Name: client_portal_accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.client_portal_accounts_id_seq', 2, true);


--
-- TOC entry 4864 (class 0 OID 0)
-- Dependencies: 312
-- Name: client_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.client_requests_id_seq', 5, true);


--
-- TOC entry 4865 (class 0 OID 0)
-- Dependencies: 314
-- Name: client_sessions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.client_sessions_id_seq', 1, true);


--
-- TOC entry 4866 (class 0 OID 0)
-- Dependencies: 228
-- Name: clients_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.clients_id_seq', 16, true);


--
-- TOC entry 4867 (class 0 OID 0)
-- Dependencies: 339
-- Name: companies_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.companies_id_seq', 1, false);


--
-- TOC entry 4868 (class 0 OID 0)
-- Dependencies: 230
-- Name: company_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.company_id_seq', 1, false);


--
-- TOC entry 4869 (class 0 OID 0)
-- Dependencies: 232
-- Name: conversations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.conversations_id_seq', 1, true);


--
-- TOC entry 4870 (class 0 OID 0)
-- Dependencies: 234
-- Name: cost_centers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.cost_centers_id_seq', 11, true);


--
-- TOC entry 4871 (class 0 OID 0)
-- Dependencies: 236
-- Name: courts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.courts_id_seq', 20, true);


--
-- TOC entry 4872 (class 0 OID 0)
-- Dependencies: 238
-- Name: currencies_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.currencies_id_seq', 12, true);


--
-- TOC entry 4873 (class 0 OID 0)
-- Dependencies: 300
-- Name: document_shares_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.document_shares_id_seq', 1, false);


--
-- TOC entry 4874 (class 0 OID 0)
-- Dependencies: 298
-- Name: document_versions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.document_versions_id_seq', 1, false);


--
-- TOC entry 4875 (class 0 OID 0)
-- Dependencies: 296
-- Name: documents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.documents_id_seq', 22, true);


--
-- TOC entry 4876 (class 0 OID 0)
-- Dependencies: 240
-- Name: employees_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.employees_id_seq', 15, true);


--
-- TOC entry 4877 (class 0 OID 0)
-- Dependencies: 243
-- Name: entity_class_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.entity_class_id_seq', 1, false);


--
-- TOC entry 4878 (class 0 OID 0)
-- Dependencies: 244
-- Name: entity_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.entity_id_seq', 1, false);


--
-- TOC entry 4879 (class 0 OID 0)
-- Dependencies: 246
-- Name: follows_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.follows_id_seq', 1, false);


--
-- TOC entry 4880 (class 0 OID 0)
-- Dependencies: 347
-- Name: footer_links_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.footer_links_id_seq', 18, true);


--
-- TOC entry 4881 (class 0 OID 0)
-- Dependencies: 248
-- Name: gl_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.gl_id_seq', 1, false);


--
-- TOC entry 4882 (class 0 OID 0)
-- Dependencies: 250
-- Name: governorates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.governorates_id_seq', 67, true);


--
-- TOC entry 4883 (class 0 OID 0)
-- Dependencies: 292
-- Name: hearings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: yemen
--

SELECT pg_catalog.setval('public.hearings_id_seq', 10, true);


--
-- TOC entry 4884 (class 0 OID 0)
-- Dependencies: 252
-- Name: invoice_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.invoice_id_seq', 1, false);


--
-- TOC entry 4885 (class 0 OID 0)
-- Dependencies: 306
-- Name: invoice_items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.invoice_items_id_seq', 5, true);


--
-- TOC entry 4886 (class 0 OID 0)
-- Dependencies: 304
-- Name: invoices_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.invoices_id_seq', 2, true);


--
-- TOC entry 4887 (class 0 OID 0)
-- Dependencies: 254
-- Name: issue_types_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.issue_types_id_seq', 5, true);


--
-- TOC entry 4888 (class 0 OID 0)
-- Dependencies: 256
-- Name: issues_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.issues_id_seq', 15, true);


--
-- TOC entry 4889 (class 0 OID 0)
-- Dependencies: 323
-- Name: journal_entries_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.journal_entries_id_seq', 47, true);


--
-- TOC entry 4890 (class 0 OID 0)
-- Dependencies: 325
-- Name: journal_entry_details_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.journal_entry_details_id_seq', 94, true);


--
-- TOC entry 4891 (class 0 OID 0)
-- Dependencies: 294
-- Name: lawyer_earnings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: yemen
--

SELECT pg_catalog.setval('public.lawyer_earnings_id_seq', 1, false);


--
-- TOC entry 4892 (class 0 OID 0)
-- Dependencies: 258
-- Name: lineages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.lineages_id_seq', 13, true);


--
-- TOC entry 4893 (class 0 OID 0)
-- Dependencies: 318
-- Name: main_accounts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.main_accounts_id_seq', 11, true);


--
-- TOC entry 4894 (class 0 OID 0)
-- Dependencies: 260
-- Name: message_read_status_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.message_read_status_id_seq', 198, true);


--
-- TOC entry 4895 (class 0 OID 0)
-- Dependencies: 262
-- Name: messages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.messages_id_seq', 204, true);


--
-- TOC entry 4896 (class 0 OID 0)
-- Dependencies: 264
-- Name: money_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.money_transactions_id_seq', 20, true);


--
-- TOC entry 4897 (class 0 OID 0)
-- Dependencies: 266
-- Name: movements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.movements_id_seq', 1, false);


--
-- TOC entry 4898 (class 0 OID 0)
-- Dependencies: 268
-- Name: navigation_pages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.navigation_pages_id_seq', 44, true);


--
-- TOC entry 4899 (class 0 OID 0)
-- Dependencies: 270
-- Name: notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notifications_id_seq', 115, true);


--
-- TOC entry 4900 (class 0 OID 0)
-- Dependencies: 272
-- Name: opening_balances_history_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.opening_balances_history_id_seq', 10, true);


--
-- TOC entry 4901 (class 0 OID 0)
-- Dependencies: 290
-- Name: opening_balances_id_seq; Type: SEQUENCE SET; Schema: public; Owner: yemen
--

SELECT pg_catalog.setval('public.opening_balances_id_seq', 10, true);


--
-- TOC entry 4902 (class 0 OID 0)
-- Dependencies: 274
-- Name: payment_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.payment_id_seq', 1, false);


--
-- TOC entry 4903 (class 0 OID 0)
-- Dependencies: 335
-- Name: payment_methods_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.payment_methods_id_seq', 11, true);


--
-- TOC entry 4904 (class 0 OID 0)
-- Dependencies: 333
-- Name: payment_vouchers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.payment_vouchers_id_seq', 1, false);


--
-- TOC entry 4905 (class 0 OID 0)
-- Dependencies: 349
-- Name: permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.permissions_id_seq', 55, true);


--
-- TOC entry 4906 (class 0 OID 0)
-- Dependencies: 277
-- Name: project_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.project_id_seq', 1, false);


--
-- TOC entry 4907 (class 0 OID 0)
-- Dependencies: 337
-- Name: public_announcements_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.public_announcements_id_seq', 3, true);


--
-- TOC entry 4908 (class 0 OID 0)
-- Dependencies: 331
-- Name: receipt_vouchers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.receipt_vouchers_id_seq', 1, false);


--
-- TOC entry 4909 (class 0 OID 0)
-- Dependencies: 279
-- Name: security_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.security_logs_id_seq', 1, false);


--
-- TOC entry 4910 (class 0 OID 0)
-- Dependencies: 281
-- Name: service_distributions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.service_distributions_id_seq', 1, false);


--
-- TOC entry 4911 (class 0 OID 0)
-- Dependencies: 343
-- Name: services_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.services_id_seq', 6, true);


--
-- TOC entry 4912 (class 0 OID 0)
-- Dependencies: 345
-- Name: serviceslow_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.serviceslow_id_seq', 6, true);


--
-- TOC entry 4913 (class 0 OID 0)
-- Dependencies: 321
-- Name: suppliers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.suppliers_id_seq', 4, true);


--
-- TOC entry 4914 (class 0 OID 0)
-- Dependencies: 283
-- Name: test_table_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.test_table_id_seq', 2, true);


--
-- TOC entry 4915 (class 0 OID 0)
-- Dependencies: 302
-- Name: time_entries_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.time_entries_id_seq', 18, true);


--
-- TOC entry 4916 (class 0 OID 0)
-- Dependencies: 285
-- Name: timecard_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.timecard_id_seq', 1, false);


--
-- TOC entry 4917 (class 0 OID 0)
-- Dependencies: 351
-- Name: user_permissions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.user_permissions_id_seq', 55, true);


--
-- TOC entry 4918 (class 0 OID 0)
-- Dependencies: 353
-- Name: user_role_assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.user_role_assignments_id_seq', 2, true);


--
-- TOC entry 4919 (class 0 OID 0)
-- Dependencies: 316
-- Name: user_roles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.user_roles_id_seq', 14, true);


--
-- TOC entry 4920 (class 0 OID 0)
-- Dependencies: 287
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 5, true);


--
-- TOC entry 4921 (class 0 OID 0)
-- Dependencies: 289
-- Name: vouchers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.vouchers_id_seq', 1, false);


--
-- TOC entry 4922 (class 0 OID 0)
-- Dependencies: 341
-- Name: website_services_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.website_services_id_seq', 6, true);


--
-- TOC entry 4084 (class 2606 OID 16886)
-- Name: acc_trans acc_trans_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.acc_trans
    ADD CONSTRAINT acc_trans_pkey PRIMARY KEY (entry_id);


--
-- TOC entry 4346 (class 2606 OID 18735)
-- Name: accounting_transaction_details accounting_transaction_details_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transaction_details
    ADD CONSTRAINT accounting_transaction_details_pkey PRIMARY KEY (id);


--
-- TOC entry 4339 (class 2606 OID 18720)
-- Name: accounting_transactions accounting_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transactions
    ADD CONSTRAINT accounting_transactions_pkey PRIMARY KEY (id);


--
-- TOC entry 4341 (class 2606 OID 18722)
-- Name: accounting_transactions accounting_transactions_transaction_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transactions
    ADD CONSTRAINT accounting_transactions_transaction_number_key UNIQUE (transaction_number);


--
-- TOC entry 4089 (class 2606 OID 16892)
-- Name: accounts accounts_account_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_account_code_key UNIQUE (account_code);


--
-- TOC entry 4091 (class 2606 OID 16894)
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- TOC entry 4323 (class 2606 OID 18414)
-- Name: ai_settings ai_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_settings
    ADD CONSTRAINT ai_settings_pkey PRIMARY KEY (id);


--
-- TOC entry 4095 (class 2606 OID 16896)
-- Name: announcements announcements_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.announcements
    ADD CONSTRAINT announcements_pkey PRIMARY KEY (id);


--
-- TOC entry 4099 (class 2606 OID 16898)
-- Name: ar ar_invnumber_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ar
    ADD CONSTRAINT ar_invnumber_key UNIQUE (invnumber);


--
-- TOC entry 4101 (class 2606 OID 16900)
-- Name: ar ar_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ar
    ADD CONSTRAINT ar_pkey PRIMARY KEY (id);


--
-- TOC entry 4105 (class 2606 OID 16902)
-- Name: branches branches_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.branches
    ADD CONSTRAINT branches_pkey PRIMARY KEY (id);


--
-- TOC entry 4107 (class 2606 OID 16904)
-- Name: budget budget_budget_year_account_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget
    ADD CONSTRAINT budget_budget_year_account_id_key UNIQUE (budget_year, account_id);


--
-- TOC entry 4109 (class 2606 OID 16906)
-- Name: budget budget_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.budget
    ADD CONSTRAINT budget_pkey PRIMARY KEY (id);


--
-- TOC entry 4111 (class 2606 OID 16908)
-- Name: case_distribution case_distribution_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_distribution
    ADD CONSTRAINT case_distribution_pkey PRIMARY KEY (id);


--
-- TOC entry 4113 (class 2606 OID 16910)
-- Name: chart_of_accounts chart_of_accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chart_of_accounts
    ADD CONSTRAINT chart_of_accounts_pkey PRIMARY KEY (id);


--
-- TOC entry 4305 (class 2606 OID 17641)
-- Name: client_notifications client_notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications
    ADD CONSTRAINT client_notifications_pkey PRIMARY KEY (id);


--
-- TOC entry 4297 (class 2606 OID 17618)
-- Name: client_portal_accounts client_portal_accounts_client_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_client_id_key UNIQUE (client_id);


--
-- TOC entry 4299 (class 2606 OID 17622)
-- Name: client_portal_accounts client_portal_accounts_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_email_key UNIQUE (email);


--
-- TOC entry 4301 (class 2606 OID 17616)
-- Name: client_portal_accounts client_portal_accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_pkey PRIMARY KEY (id);


--
-- TOC entry 4303 (class 2606 OID 17620)
-- Name: client_portal_accounts client_portal_accounts_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_username_key UNIQUE (username);


--
-- TOC entry 4309 (class 2606 OID 17669)
-- Name: client_requests client_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests
    ADD CONSTRAINT client_requests_pkey PRIMARY KEY (id);


--
-- TOC entry 4311 (class 2606 OID 17695)
-- Name: client_sessions client_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT client_sessions_pkey PRIMARY KEY (id);


--
-- TOC entry 4313 (class 2606 OID 17697)
-- Name: client_sessions client_sessions_session_token_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT client_sessions_session_token_key UNIQUE (session_token);


--
-- TOC entry 4126 (class 2606 OID 16912)
-- Name: clients clients_id_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_id_number_key UNIQUE (id_number);


--
-- TOC entry 4128 (class 2606 OID 16914)
-- Name: clients clients_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_pkey PRIMARY KEY (id);


--
-- TOC entry 4130 (class 2606 OID 16916)
-- Name: clients clients_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_username_key UNIQUE (username);


--
-- TOC entry 4367 (class 2606 OID 18908)
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (id);


--
-- TOC entry 4137 (class 2606 OID 16920)
-- Name: company company_control_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_control_code_key UNIQUE (control_code);


--
-- TOC entry 4139 (class 2606 OID 16922)
-- Name: company company_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_pkey PRIMARY KEY (id);


--
-- TOC entry 4141 (class 2606 OID 16924)
-- Name: conversations conversations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_pkey PRIMARY KEY (id);


--
-- TOC entry 4147 (class 2606 OID 16926)
-- Name: cost_centers cost_centers_center_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cost_centers
    ADD CONSTRAINT cost_centers_center_code_key UNIQUE (center_code);


--
-- TOC entry 4149 (class 2606 OID 16928)
-- Name: cost_centers cost_centers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cost_centers
    ADD CONSTRAINT cost_centers_pkey PRIMARY KEY (id);


--
-- TOC entry 4154 (class 2606 OID 16930)
-- Name: courts courts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.courts
    ADD CONSTRAINT courts_pkey PRIMARY KEY (id);


--
-- TOC entry 4156 (class 2606 OID 16932)
-- Name: currencies currencies_currency_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.currencies
    ADD CONSTRAINT currencies_currency_code_key UNIQUE (currency_code);


--
-- TOC entry 4158 (class 2606 OID 16934)
-- Name: currencies currencies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.currencies
    ADD CONSTRAINT currencies_pkey PRIMARY KEY (id);


--
-- TOC entry 4281 (class 2606 OID 17491)
-- Name: document_shares document_shares_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_pkey PRIMARY KEY (id);


--
-- TOC entry 4279 (class 2606 OID 17471)
-- Name: document_versions document_versions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_versions
    ADD CONSTRAINT document_versions_pkey PRIMARY KEY (id);


--
-- TOC entry 4272 (class 2606 OID 17441)
-- Name: documents documents_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_pkey PRIMARY KEY (id);


--
-- TOC entry 4161 (class 2606 OID 16936)
-- Name: employees employees_id_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_id_number_key UNIQUE (id_number);


--
-- TOC entry 4163 (class 2606 OID 16938)
-- Name: employees employees_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_pkey PRIMARY KEY (id);


--
-- TOC entry 4171 (class 2606 OID 16940)
-- Name: entity_class entity_class_class_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_class
    ADD CONSTRAINT entity_class_class_key UNIQUE (class);


--
-- TOC entry 4173 (class 2606 OID 16942)
-- Name: entity_class entity_class_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity_class
    ADD CONSTRAINT entity_class_pkey PRIMARY KEY (id);


--
-- TOC entry 4167 (class 2606 OID 16944)
-- Name: entity entity_control_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity
    ADD CONSTRAINT entity_control_code_key UNIQUE (control_code);


--
-- TOC entry 4169 (class 2606 OID 16946)
-- Name: entity entity_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.entity
    ADD CONSTRAINT entity_pkey PRIMARY KEY (id);


--
-- TOC entry 4175 (class 2606 OID 16948)
-- Name: follows follows_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_pkey PRIMARY KEY (id);


--
-- TOC entry 4382 (class 2606 OID 19006)
-- Name: footer_links footer_links_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.footer_links
    ADD CONSTRAINT footer_links_pkey PRIMARY KEY (id);


--
-- TOC entry 4179 (class 2606 OID 16950)
-- Name: gl gl_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.gl
    ADD CONSTRAINT gl_pkey PRIMARY KEY (id);


--
-- TOC entry 4182 (class 2606 OID 16952)
-- Name: governorates governorates_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.governorates
    ADD CONSTRAINT governorates_code_key UNIQUE (code);


--
-- TOC entry 4184 (class 2606 OID 16954)
-- Name: governorates governorates_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.governorates
    ADD CONSTRAINT governorates_pkey PRIMARY KEY (id);


--
-- TOC entry 4265 (class 2606 OID 17366)
-- Name: hearings hearings_pkey; Type: CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.hearings
    ADD CONSTRAINT hearings_pkey PRIMARY KEY (id);


--
-- TOC entry 4295 (class 2606 OID 17584)
-- Name: invoice_items invoice_items_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items
    ADD CONSTRAINT invoice_items_pkey PRIMARY KEY (id);


--
-- TOC entry 4186 (class 2606 OID 16956)
-- Name: invoice invoice_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice
    ADD CONSTRAINT invoice_pkey PRIMARY KEY (id);


--
-- TOC entry 4291 (class 2606 OID 17562)
-- Name: invoices invoices_invoice_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_invoice_number_key UNIQUE (invoice_number);


--
-- TOC entry 4293 (class 2606 OID 17560)
-- Name: invoices invoices_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_pkey PRIMARY KEY (id);


--
-- TOC entry 4188 (class 2606 OID 16958)
-- Name: issue_types issue_types_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issue_types
    ADD CONSTRAINT issue_types_pkey PRIMARY KEY (id);


--
-- TOC entry 4195 (class 2606 OID 16960)
-- Name: issues issues_case_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT issues_case_number_key UNIQUE (case_number);


--
-- TOC entry 4197 (class 2606 OID 16962)
-- Name: issues issues_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT issues_pkey PRIMARY KEY (id);


--
-- TOC entry 4332 (class 2606 OID 18681)
-- Name: journal_entries journal_entries_entry_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entries
    ADD CONSTRAINT journal_entries_entry_number_key UNIQUE (entry_number);


--
-- TOC entry 4334 (class 2606 OID 18679)
-- Name: journal_entries journal_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entries
    ADD CONSTRAINT journal_entries_pkey PRIMARY KEY (id);


--
-- TOC entry 4337 (class 2606 OID 18693)
-- Name: journal_entry_details journal_entry_details_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entry_details
    ADD CONSTRAINT journal_entry_details_pkey PRIMARY KEY (id);


--
-- TOC entry 4270 (class 2606 OID 17404)
-- Name: lawyer_earnings lawyer_earnings_pkey; Type: CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.lawyer_earnings
    ADD CONSTRAINT lawyer_earnings_pkey PRIMARY KEY (id);


--
-- TOC entry 4199 (class 2606 OID 16970)
-- Name: lineages lineages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.lineages
    ADD CONSTRAINT lineages_pkey PRIMARY KEY (id);


--
-- TOC entry 4321 (class 2606 OID 17799)
-- Name: main_accounts main_accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.main_accounts
    ADD CONSTRAINT main_accounts_pkey PRIMARY KEY (id);


--
-- TOC entry 4203 (class 2606 OID 16972)
-- Name: message_read_status message_read_status_message_id_reader_type_reader_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message_read_status
    ADD CONSTRAINT message_read_status_message_id_reader_type_reader_id_key UNIQUE (message_id, reader_type, reader_id);


--
-- TOC entry 4205 (class 2606 OID 16974)
-- Name: message_read_status message_read_status_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message_read_status
    ADD CONSTRAINT message_read_status_pkey PRIMARY KEY (id);


--
-- TOC entry 4212 (class 2606 OID 16976)
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id);


--
-- TOC entry 4214 (class 2606 OID 16978)
-- Name: money_transactions money_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.money_transactions
    ADD CONSTRAINT money_transactions_pkey PRIMARY KEY (id);


--
-- TOC entry 4216 (class 2606 OID 16980)
-- Name: movements movements_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movements
    ADD CONSTRAINT movements_pkey PRIMARY KEY (id);


--
-- TOC entry 4218 (class 2606 OID 16982)
-- Name: navigation_pages navigation_pages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.navigation_pages
    ADD CONSTRAINT navigation_pages_pkey PRIMARY KEY (id);


--
-- TOC entry 4223 (class 2606 OID 16984)
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- TOC entry 4225 (class 2606 OID 16986)
-- Name: opening_balances_history opening_balances_history_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.opening_balances_history
    ADD CONSTRAINT opening_balances_history_pkey PRIMARY KEY (id);


--
-- TOC entry 4263 (class 2606 OID 17349)
-- Name: opening_balances opening_balances_pkey; Type: CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.opening_balances
    ADD CONSTRAINT opening_balances_pkey PRIMARY KEY (id);


--
-- TOC entry 4230 (class 2606 OID 16990)
-- Name: payment_links payment_links_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_links
    ADD CONSTRAINT payment_links_pkey PRIMARY KEY (payment_id, entry_id);


--
-- TOC entry 4361 (class 2606 OID 18812)
-- Name: payment_methods payment_methods_method_code_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_methods
    ADD CONSTRAINT payment_methods_method_code_key UNIQUE (method_code);


--
-- TOC entry 4363 (class 2606 OID 18810)
-- Name: payment_methods payment_methods_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_methods
    ADD CONSTRAINT payment_methods_pkey PRIMARY KEY (id);


--
-- TOC entry 4228 (class 2606 OID 16996)
-- Name: payment payment_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment
    ADD CONSTRAINT payment_pkey PRIMARY KEY (id);


--
-- TOC entry 4357 (class 2606 OID 18786)
-- Name: payment_vouchers payment_vouchers_entry_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers
    ADD CONSTRAINT payment_vouchers_entry_number_key UNIQUE (entry_number);


--
-- TOC entry 4359 (class 2606 OID 18784)
-- Name: payment_vouchers payment_vouchers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers
    ADD CONSTRAINT payment_vouchers_pkey PRIMARY KEY (id);


--
-- TOC entry 4384 (class 2606 OID 19020)
-- Name: permissions permissions_permission_key_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_permission_key_key UNIQUE (permission_key);


--
-- TOC entry 4386 (class 2606 OID 19018)
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- TOC entry 4233 (class 2606 OID 16998)
-- Name: project project_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project
    ADD CONSTRAINT project_pkey PRIMARY KEY (id);


--
-- TOC entry 4235 (class 2606 OID 17000)
-- Name: project project_projectnumber_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.project
    ADD CONSTRAINT project_projectnumber_key UNIQUE (projectnumber);


--
-- TOC entry 4365 (class 2606 OID 18882)
-- Name: public_announcements public_announcements_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.public_announcements
    ADD CONSTRAINT public_announcements_pkey PRIMARY KEY (id);


--
-- TOC entry 4350 (class 2606 OID 18769)
-- Name: receipt_vouchers receipt_vouchers_entry_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.receipt_vouchers
    ADD CONSTRAINT receipt_vouchers_entry_number_key UNIQUE (entry_number);


--
-- TOC entry 4352 (class 2606 OID 18767)
-- Name: receipt_vouchers receipt_vouchers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.receipt_vouchers
    ADD CONSTRAINT receipt_vouchers_pkey PRIMARY KEY (id);


--
-- TOC entry 4237 (class 2606 OID 17002)
-- Name: security_logs security_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.security_logs
    ADD CONSTRAINT security_logs_pkey PRIMARY KEY (id);


--
-- TOC entry 4239 (class 2606 OID 17004)
-- Name: service_distributions service_distributions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_distributions
    ADD CONSTRAINT service_distributions_pkey PRIMARY KEY (id);


--
-- TOC entry 4373 (class 2606 OID 18965)
-- Name: services services_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_pkey PRIMARY KEY (id);


--
-- TOC entry 4378 (class 2606 OID 18985)
-- Name: serviceslow serviceslow_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.serviceslow
    ADD CONSTRAINT serviceslow_pkey PRIMARY KEY (id);


--
-- TOC entry 4380 (class 2606 OID 18987)
-- Name: serviceslow serviceslow_slug_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.serviceslow
    ADD CONSTRAINT serviceslow_slug_key UNIQUE (slug);


--
-- TOC entry 4327 (class 2606 OID 18527)
-- Name: suppliers suppliers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.suppliers
    ADD CONSTRAINT suppliers_pkey PRIMARY KEY (id);


--
-- TOC entry 4241 (class 2606 OID 17008)
-- Name: test_table test_table_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.test_table
    ADD CONSTRAINT test_table_pkey PRIMARY KEY (id);


--
-- TOC entry 4286 (class 2606 OID 17525)
-- Name: time_entries time_entries_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_pkey PRIMARY KEY (id);


--
-- TOC entry 4245 (class 2606 OID 17010)
-- Name: timecard timecard_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.timecard
    ADD CONSTRAINT timecard_pkey PRIMARY KEY (id);


--
-- TOC entry 4124 (class 2606 OID 17014)
-- Name: chart_of_accounts unique_level_code; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chart_of_accounts
    ADD CONSTRAINT unique_level_code UNIQUE (account_code);


--
-- TOC entry 4388 (class 2606 OID 19029)
-- Name: user_permissions user_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_pkey PRIMARY KEY (id);


--
-- TOC entry 4390 (class 2606 OID 19031)
-- Name: user_permissions user_permissions_user_id_permission_key_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_user_id_permission_key_key UNIQUE (user_id, permission_key);


--
-- TOC entry 4394 (class 2606 OID 19050)
-- Name: user_role_assignments user_role_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_pkey PRIMARY KEY (id);


--
-- TOC entry 4396 (class 2606 OID 19052)
-- Name: user_role_assignments user_role_assignments_user_id_role_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_user_id_role_name_key UNIQUE (user_id, role_name);


--
-- TOC entry 4315 (class 2606 OID 17734)
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- TOC entry 4317 (class 2606 OID 17736)
-- Name: user_roles user_roles_role_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_name_key UNIQUE (role_name);


--
-- TOC entry 4251 (class 2606 OID 17016)
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- TOC entry 4253 (class 2606 OID 17018)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 4255 (class 2606 OID 17020)
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- TOC entry 4259 (class 2606 OID 17022)
-- Name: vouchers vouchers_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vouchers
    ADD CONSTRAINT vouchers_pkey PRIMARY KEY (id);


--
-- TOC entry 4261 (class 2606 OID 17024)
-- Name: vouchers vouchers_voucher_number_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vouchers
    ADD CONSTRAINT vouchers_voucher_number_key UNIQUE (voucher_number);


--
-- TOC entry 4369 (class 2606 OID 18941)
-- Name: website_services website_services_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.website_services
    ADD CONSTRAINT website_services_pkey PRIMARY KEY (id);


--
-- TOC entry 4371 (class 2606 OID 18943)
-- Name: website_services website_services_slug_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.website_services
    ADD CONSTRAINT website_services_slug_key UNIQUE (slug);


--
-- TOC entry 4085 (class 1259 OID 17025)
-- Name: idx_acc_trans_chart_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_acc_trans_chart_id ON public.acc_trans USING btree (chart_id);


--
-- TOC entry 4086 (class 1259 OID 17026)
-- Name: idx_acc_trans_trans_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_acc_trans_trans_id ON public.acc_trans USING btree (trans_id);


--
-- TOC entry 4087 (class 1259 OID 17027)
-- Name: idx_acc_trans_transdate; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_acc_trans_transdate ON public.acc_trans USING btree (transdate);


--
-- TOC entry 4347 (class 1259 OID 18746)
-- Name: idx_accounting_transaction_details_account; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transaction_details_account ON public.accounting_transaction_details USING btree (account_id);


--
-- TOC entry 4348 (class 1259 OID 18747)
-- Name: idx_accounting_transaction_details_transaction; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transaction_details_transaction ON public.accounting_transaction_details USING btree (transaction_id);


--
-- TOC entry 4342 (class 1259 OID 18744)
-- Name: idx_accounting_transactions_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transactions_date ON public.accounting_transactions USING btree (transaction_date);


--
-- TOC entry 4343 (class 1259 OID 18745)
-- Name: idx_accounting_transactions_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transactions_status ON public.accounting_transactions USING btree (status);


--
-- TOC entry 4344 (class 1259 OID 18743)
-- Name: idx_accounting_transactions_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounting_transactions_type ON public.accounting_transactions USING btree (transaction_type);


--
-- TOC entry 4092 (class 1259 OID 17031)
-- Name: idx_accounts_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounts_code ON public.accounts USING btree (account_code);


--
-- TOC entry 4093 (class 1259 OID 17032)
-- Name: idx_accounts_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_accounts_type ON public.accounts USING btree (account_type);


--
-- TOC entry 4324 (class 1259 OID 18415)
-- Name: idx_ai_settings_enabled; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ai_settings_enabled ON public.ai_settings USING btree (enabled);


--
-- TOC entry 4096 (class 1259 OID 17033)
-- Name: idx_announcements_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_announcements_active ON public.announcements USING btree (is_active);


--
-- TOC entry 4097 (class 1259 OID 17034)
-- Name: idx_announcements_created_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_announcements_created_date ON public.announcements USING btree (created_date);


--
-- TOC entry 4102 (class 1259 OID 17035)
-- Name: idx_ar_entity_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ar_entity_id ON public.ar USING btree (entity_id);


--
-- TOC entry 4103 (class 1259 OID 17036)
-- Name: idx_ar_transdate; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ar_transdate ON public.ar USING btree (transdate);


--
-- TOC entry 4114 (class 1259 OID 17037)
-- Name: idx_chart_account_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_account_code ON public.chart_of_accounts USING btree (account_code);


--
-- TOC entry 4115 (class 1259 OID 17038)
-- Name: idx_chart_account_level; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_account_level ON public.chart_of_accounts USING btree (account_level);


--
-- TOC entry 4116 (class 1259 OID 17780)
-- Name: idx_chart_accounts_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_accounts_code ON public.chart_of_accounts USING btree (account_code);


--
-- TOC entry 4117 (class 1259 OID 17779)
-- Name: idx_chart_accounts_linked_table; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_accounts_linked_table ON public.chart_of_accounts USING btree (linked_table);


--
-- TOC entry 4118 (class 1259 OID 17777)
-- Name: idx_chart_accounts_parent; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_accounts_parent ON public.chart_of_accounts USING btree (parent_id);


--
-- TOC entry 4119 (class 1259 OID 17778)
-- Name: idx_chart_accounts_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_accounts_type ON public.chart_of_accounts USING btree (account_type);


--
-- TOC entry 4120 (class 1259 OID 18629)
-- Name: idx_chart_linked_record; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_linked_record ON public.chart_of_accounts USING btree (is_linked_record, original_table, linked_record_id);


--
-- TOC entry 4121 (class 1259 OID 17039)
-- Name: idx_chart_linked_table; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_linked_table ON public.chart_of_accounts USING btree (linked_table);


--
-- TOC entry 4122 (class 1259 OID 17040)
-- Name: idx_chart_parent_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_chart_parent_id ON public.chart_of_accounts USING btree (parent_id);


--
-- TOC entry 4306 (class 1259 OID 17714)
-- Name: idx_client_notifications_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_client_notifications_client_id ON public.client_notifications USING btree (client_id);


--
-- TOC entry 4307 (class 1259 OID 17715)
-- Name: idx_client_notifications_read; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_client_notifications_read ON public.client_notifications USING btree (is_read);


--
-- TOC entry 4131 (class 1259 OID 17041)
-- Name: idx_clients_account_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_account_id ON public.clients USING btree (account_id);


--
-- TOC entry 4132 (class 1259 OID 17042)
-- Name: idx_clients_account_id_not_null; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_account_id_not_null ON public.clients USING btree (account_id) WHERE (account_id IS NOT NULL);


--
-- TOC entry 4133 (class 1259 OID 17043)
-- Name: idx_clients_last_login; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_last_login ON public.clients USING btree (last_login);


--
-- TOC entry 4134 (class 1259 OID 17044)
-- Name: idx_clients_online; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_online ON public.clients USING btree (is_online);


--
-- TOC entry 4135 (class 1259 OID 17045)
-- Name: idx_clients_username; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_clients_username ON public.clients USING btree (username);


--
-- TOC entry 4142 (class 1259 OID 17046)
-- Name: idx_conversations_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_conversations_client_id ON public.conversations USING btree (client_id);


--
-- TOC entry 4143 (class 1259 OID 17047)
-- Name: idx_conversations_last_message; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_conversations_last_message ON public.conversations USING btree (last_message_at DESC);


--
-- TOC entry 4144 (class 1259 OID 17048)
-- Name: idx_conversations_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_conversations_status ON public.conversations USING btree (status);


--
-- TOC entry 4145 (class 1259 OID 17049)
-- Name: idx_conversations_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_conversations_user_id ON public.conversations USING btree (user_id);


--
-- TOC entry 4150 (class 1259 OID 17050)
-- Name: idx_cost_centers_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cost_centers_active ON public.cost_centers USING btree (is_active);


--
-- TOC entry 4151 (class 1259 OID 17051)
-- Name: idx_cost_centers_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cost_centers_code ON public.cost_centers USING btree (center_code);


--
-- TOC entry 4152 (class 1259 OID 17052)
-- Name: idx_cost_centers_parent; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cost_centers_parent ON public.cost_centers USING btree (parent_id);


--
-- TOC entry 4159 (class 1259 OID 17053)
-- Name: idx_currencies_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_currencies_code ON public.currencies USING btree (currency_code);


--
-- TOC entry 4273 (class 1259 OID 17703)
-- Name: idx_documents_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_case_id ON public.documents USING btree (case_id);


--
-- TOC entry 4274 (class 1259 OID 17705)
-- Name: idx_documents_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_category ON public.documents USING btree (category);


--
-- TOC entry 4275 (class 1259 OID 17704)
-- Name: idx_documents_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_client_id ON public.documents USING btree (client_id);


--
-- TOC entry 4276 (class 1259 OID 17707)
-- Name: idx_documents_content_text; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_content_text ON public.documents USING gin (to_tsvector('arabic'::regconfig, content_text));


--
-- TOC entry 4277 (class 1259 OID 17706)
-- Name: idx_documents_tags; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_documents_tags ON public.documents USING gin (tags);


--
-- TOC entry 4164 (class 1259 OID 17054)
-- Name: idx_employees_account_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_employees_account_id ON public.employees USING btree (account_id);


--
-- TOC entry 4165 (class 1259 OID 17055)
-- Name: idx_employees_account_id_not_null; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_employees_account_id_not_null ON public.employees USING btree (account_id) WHERE (account_id IS NOT NULL);


--
-- TOC entry 4176 (class 1259 OID 17426)
-- Name: idx_follows_approved; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_follows_approved ON public.follows USING btree (is_approved);


--
-- TOC entry 4177 (class 1259 OID 17425)
-- Name: idx_follows_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_follows_user_id ON public.follows USING btree (user_id);


--
-- TOC entry 4180 (class 1259 OID 17056)
-- Name: idx_gl_transdate; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_gl_transdate ON public.gl USING btree (transdate);


--
-- TOC entry 4287 (class 1259 OID 17711)
-- Name: idx_invoices_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_invoices_client_id ON public.invoices USING btree (client_id);


--
-- TOC entry 4288 (class 1259 OID 17713)
-- Name: idx_invoices_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_invoices_date ON public.invoices USING btree (invoice_date);


--
-- TOC entry 4289 (class 1259 OID 17712)
-- Name: idx_invoices_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_invoices_status ON public.invoices USING btree (status);


--
-- TOC entry 4189 (class 1259 OID 17057)
-- Name: idx_issues_case_number; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_case_number ON public.issues USING btree (case_number);


--
-- TOC entry 4190 (class 1259 OID 17058)
-- Name: idx_issues_client_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_client_id ON public.issues USING btree (client_id);


--
-- TOC entry 4191 (class 1259 OID 17059)
-- Name: idx_issues_court_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_court_id ON public.issues USING btree (court_id);


--
-- TOC entry 4192 (class 1259 OID 17060)
-- Name: idx_issues_created_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_created_date ON public.issues USING btree (created_date);


--
-- TOC entry 4193 (class 1259 OID 17061)
-- Name: idx_issues_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_issues_status ON public.issues USING btree (status);


--
-- TOC entry 4328 (class 1259 OID 18751)
-- Name: idx_journal_entries_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_journal_entries_date ON public.journal_entries USING btree (entry_date);


--
-- TOC entry 4329 (class 1259 OID 18752)
-- Name: idx_journal_entries_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_journal_entries_status ON public.journal_entries USING btree (status);


--
-- TOC entry 4330 (class 1259 OID 18750)
-- Name: idx_journal_entries_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_journal_entries_type ON public.journal_entries USING btree (entry_type);


--
-- TOC entry 4335 (class 1259 OID 18753)
-- Name: idx_journal_entry_details_account; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_journal_entry_details_account ON public.journal_entry_details USING btree (account_id);


--
-- TOC entry 4266 (class 1259 OID 17421)
-- Name: idx_lawyer_earnings_case_id; Type: INDEX; Schema: public; Owner: yemen
--

CREATE INDEX idx_lawyer_earnings_case_id ON public.lawyer_earnings USING btree (case_id);


--
-- TOC entry 4267 (class 1259 OID 17420)
-- Name: idx_lawyer_earnings_lawyer_id; Type: INDEX; Schema: public; Owner: yemen
--

CREATE INDEX idx_lawyer_earnings_lawyer_id ON public.lawyer_earnings USING btree (lawyer_id);


--
-- TOC entry 4268 (class 1259 OID 17422)
-- Name: idx_lawyer_earnings_service_id; Type: INDEX; Schema: public; Owner: yemen
--

CREATE INDEX idx_lawyer_earnings_service_id ON public.lawyer_earnings USING btree (service_id);


--
-- TOC entry 4318 (class 1259 OID 17808)
-- Name: idx_main_accounts_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_main_accounts_code ON public.main_accounts USING btree (account_code);


--
-- TOC entry 4319 (class 1259 OID 17807)
-- Name: idx_main_accounts_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_main_accounts_name ON public.main_accounts USING btree (account_name);


--
-- TOC entry 4200 (class 1259 OID 17070)
-- Name: idx_message_read_status_message; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_message_read_status_message ON public.message_read_status USING btree (message_id);


--
-- TOC entry 4201 (class 1259 OID 17071)
-- Name: idx_message_read_status_reader; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_message_read_status_reader ON public.message_read_status USING btree (reader_type, reader_id);


--
-- TOC entry 4206 (class 1259 OID 17072)
-- Name: idx_messages_conversation_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_conversation_id ON public.messages USING btree (conversation_id);


--
-- TOC entry 4207 (class 1259 OID 17073)
-- Name: idx_messages_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_created_at ON public.messages USING btree (created_at DESC);


--
-- TOC entry 4208 (class 1259 OID 17074)
-- Name: idx_messages_is_read; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_is_read ON public.messages USING btree (is_read);


--
-- TOC entry 4209 (class 1259 OID 17075)
-- Name: idx_messages_reply_to; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_reply_to ON public.messages USING btree (reply_to_message_id);


--
-- TOC entry 4210 (class 1259 OID 17076)
-- Name: idx_messages_sender; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_messages_sender ON public.messages USING btree (sender_type, sender_id);


--
-- TOC entry 4219 (class 1259 OID 17077)
-- Name: idx_notifications_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_notifications_created_at ON public.notifications USING btree (created_at DESC);


--
-- TOC entry 4220 (class 1259 OID 17078)
-- Name: idx_notifications_is_read; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_notifications_is_read ON public.notifications USING btree (is_read);


--
-- TOC entry 4221 (class 1259 OID 17079)
-- Name: idx_notifications_recipient; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_notifications_recipient ON public.notifications USING btree (recipient_type, recipient_id);


--
-- TOC entry 4226 (class 1259 OID 17080)
-- Name: idx_payment_entity_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payment_entity_id ON public.payment USING btree (entity_id);


--
-- TOC entry 4353 (class 1259 OID 18797)
-- Name: idx_payment_vouchers_entry_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payment_vouchers_entry_date ON public.payment_vouchers USING btree (entry_date);


--
-- TOC entry 4354 (class 1259 OID 18799)
-- Name: idx_payment_vouchers_payee_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payment_vouchers_payee_type ON public.payment_vouchers USING btree (payee_type);


--
-- TOC entry 4355 (class 1259 OID 18798)
-- Name: idx_payment_vouchers_status; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_payment_vouchers_status ON public.payment_vouchers USING btree (status);


--
-- TOC entry 4231 (class 1259 OID 17081)
-- Name: idx_project_customer_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_project_customer_id ON public.project USING btree (customer_id);


--
-- TOC entry 4374 (class 1259 OID 18989)
-- Name: idx_serviceslow_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_serviceslow_active ON public.serviceslow USING btree (is_active);


--
-- TOC entry 4375 (class 1259 OID 18988)
-- Name: idx_serviceslow_slug; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_serviceslow_slug ON public.serviceslow USING btree (slug);


--
-- TOC entry 4376 (class 1259 OID 18990)
-- Name: idx_serviceslow_sort; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_serviceslow_sort ON public.serviceslow USING btree (sort_order);


--
-- TOC entry 4325 (class 1259 OID 18701)
-- Name: idx_suppliers_account_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_suppliers_account_id ON public.suppliers USING btree (account_id);


--
-- TOC entry 4282 (class 1259 OID 17708)
-- Name: idx_time_entries_case_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_time_entries_case_id ON public.time_entries USING btree (case_id);


--
-- TOC entry 4283 (class 1259 OID 17710)
-- Name: idx_time_entries_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_time_entries_date ON public.time_entries USING btree (start_time);


--
-- TOC entry 4284 (class 1259 OID 17709)
-- Name: idx_time_entries_employee_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_time_entries_employee_id ON public.time_entries USING btree (employee_id);


--
-- TOC entry 4242 (class 1259 OID 17082)
-- Name: idx_timecard_employee_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_timecard_employee_id ON public.timecard USING btree (employee_id);


--
-- TOC entry 4243 (class 1259 OID 17083)
-- Name: idx_timecard_project_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_timecard_project_id ON public.timecard USING btree (project_id);


--
-- TOC entry 4391 (class 1259 OID 19069)
-- Name: idx_user_role_assignments_role_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_user_role_assignments_role_name ON public.user_role_assignments USING btree (role_name);


--
-- TOC entry 4392 (class 1259 OID 19068)
-- Name: idx_user_role_assignments_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_user_role_assignments_user_id ON public.user_role_assignments USING btree (user_id);


--
-- TOC entry 4246 (class 1259 OID 17084)
-- Name: idx_users_device_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_device_id ON public.users USING btree (device_id);


--
-- TOC entry 4247 (class 1259 OID 17085)
-- Name: idx_users_last_login; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_last_login ON public.users USING btree (last_login);


--
-- TOC entry 4248 (class 1259 OID 17086)
-- Name: idx_users_online; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_online ON public.users USING btree (is_online);


--
-- TOC entry 4249 (class 1259 OID 17087)
-- Name: idx_users_username; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_users_username ON public.users USING btree (username);


--
-- TOC entry 4256 (class 1259 OID 17088)
-- Name: idx_vouchers_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_vouchers_date ON public.vouchers USING btree (voucher_date);


--
-- TOC entry 4257 (class 1259 OID 17089)
-- Name: idx_vouchers_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_vouchers_type ON public.vouchers USING btree (voucher_type);


--
-- TOC entry 4467 (class 2620 OID 17090)
-- Name: clients trigger_auto_link_client_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_auto_link_client_account BEFORE INSERT ON public.clients FOR EACH ROW EXECUTE FUNCTION public.auto_link_client_account();


--
-- TOC entry 4471 (class 2620 OID 17091)
-- Name: employees trigger_auto_link_employee_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_auto_link_employee_account BEFORE INSERT ON public.employees FOR EACH ROW EXECUTE FUNCTION public.auto_link_employee_account();


--
-- TOC entry 4468 (class 2620 OID 18626)
-- Name: clients trigger_create_client_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_create_client_account BEFORE INSERT ON public.clients FOR EACH ROW WHEN (((new.status)::text = 'active'::text)) EXECUTE FUNCTION public.create_client_account();


--
-- TOC entry 4472 (class 2620 OID 18627)
-- Name: employees trigger_create_employee_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_create_employee_account BEFORE INSERT ON public.employees FOR EACH ROW WHEN (((new.status)::text = 'active'::text)) EXECUTE FUNCTION public.create_employee_account();


--
-- TOC entry 4476 (class 2620 OID 18621)
-- Name: suppliers trigger_create_supplier_account; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_create_supplier_account AFTER INSERT ON public.suppliers FOR EACH ROW WHEN (((new.status)::text = 'active'::text)) EXECUTE FUNCTION public.create_supplier_account();


--
-- TOC entry 4469 (class 2620 OID 18624)
-- Name: clients trigger_update_client_account_name; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_client_account_name AFTER UPDATE OF name ON public.clients FOR EACH ROW WHEN ((((old.name)::text IS DISTINCT FROM (new.name)::text) AND (new.account_id IS NOT NULL))) EXECUTE FUNCTION public.update_client_account_name();


--
-- TOC entry 4475 (class 2620 OID 17092)
-- Name: messages trigger_update_conversation_last_message; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_conversation_last_message AFTER INSERT ON public.messages FOR EACH ROW EXECUTE FUNCTION public.update_conversation_last_message();


--
-- TOC entry 4473 (class 2620 OID 18625)
-- Name: employees trigger_update_employee_account_name; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_employee_account_name AFTER UPDATE OF name ON public.employees FOR EACH ROW WHEN ((((old.name)::text IS DISTINCT FROM (new.name)::text) AND (new.account_id IS NOT NULL))) EXECUTE FUNCTION public.update_employee_account_name();


--
-- TOC entry 4470 (class 2620 OID 18543)
-- Name: clients trigger_update_parent_balance_clients; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_parent_balance_clients AFTER INSERT OR DELETE OR UPDATE OF current_balance ON public.clients FOR EACH ROW EXECUTE FUNCTION public.update_parent_balance_on_client_change();


--
-- TOC entry 4474 (class 2620 OID 18545)
-- Name: employees trigger_update_parent_balance_employees; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_parent_balance_employees AFTER INSERT OR DELETE OR UPDATE OF current_balance ON public.employees FOR EACH ROW EXECUTE FUNCTION public.update_parent_balance_on_employee_change();


--
-- TOC entry 4477 (class 2620 OID 18547)
-- Name: suppliers trigger_update_parent_balance_suppliers; Type: TRIGGER; Schema: public; Owner: postgres
--

CREATE TRIGGER trigger_update_parent_balance_suppliers AFTER INSERT OR DELETE OR UPDATE OF current_balance ON public.suppliers FOR EACH ROW EXECUTE FUNCTION public.update_parent_balance_on_supplier_change();


--
-- TOC entry 4397 (class 2606 OID 17093)
-- Name: acc_trans acc_trans_trans_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.acc_trans
    ADD CONSTRAINT acc_trans_trans_id_fkey FOREIGN KEY (trans_id) REFERENCES public.gl(id);


--
-- TOC entry 4458 (class 2606 OID 18736)
-- Name: accounting_transaction_details accounting_transaction_details_transaction_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounting_transaction_details
    ADD CONSTRAINT accounting_transaction_details_transaction_id_fkey FOREIGN KEY (transaction_id) REFERENCES public.accounting_transactions(id) ON DELETE CASCADE;


--
-- TOC entry 4398 (class 2606 OID 17098)
-- Name: accounts accounts_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.accounts(id);


--
-- TOC entry 4399 (class 2606 OID 17103)
-- Name: branches branches_governorate_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.branches
    ADD CONSTRAINT branches_governorate_id_fkey FOREIGN KEY (governorate_id) REFERENCES public.governorates(id);


--
-- TOC entry 4400 (class 2606 OID 17108)
-- Name: case_distribution case_distribution_lineage_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_distribution
    ADD CONSTRAINT case_distribution_lineage_id_fkey FOREIGN KEY (lineage_id) REFERENCES public.lineages(id) ON DELETE SET NULL;


--
-- TOC entry 4401 (class 2606 OID 17113)
-- Name: chart_of_accounts chart_of_accounts_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chart_of_accounts
    ADD CONSTRAINT chart_of_accounts_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.chart_of_accounts(id);


--
-- TOC entry 4448 (class 2606 OID 17647)
-- Name: client_notifications client_notifications_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications
    ADD CONSTRAINT client_notifications_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- TOC entry 4449 (class 2606 OID 17642)
-- Name: client_notifications client_notifications_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications
    ADD CONSTRAINT client_notifications_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- TOC entry 4450 (class 2606 OID 17652)
-- Name: client_notifications client_notifications_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_notifications
    ADD CONSTRAINT client_notifications_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id);


--
-- TOC entry 4447 (class 2606 OID 17623)
-- Name: client_portal_accounts client_portal_accounts_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_portal_accounts
    ADD CONSTRAINT client_portal_accounts_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- TOC entry 4451 (class 2606 OID 17680)
-- Name: client_requests client_requests_assigned_to_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests
    ADD CONSTRAINT client_requests_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.employees(id);


--
-- TOC entry 4452 (class 2606 OID 17675)
-- Name: client_requests client_requests_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests
    ADD CONSTRAINT client_requests_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- TOC entry 4453 (class 2606 OID 17670)
-- Name: client_requests client_requests_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_requests
    ADD CONSTRAINT client_requests_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- TOC entry 4454 (class 2606 OID 17698)
-- Name: client_sessions client_sessions_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.client_sessions
    ADD CONSTRAINT client_sessions_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- TOC entry 4402 (class 2606 OID 18610)
-- Name: clients clients_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.clients
    ADD CONSTRAINT clients_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.chart_of_accounts(id);


--
-- TOC entry 4403 (class 2606 OID 17118)
-- Name: conversations conversations_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id) ON DELETE CASCADE;


--
-- TOC entry 4404 (class 2606 OID 17123)
-- Name: conversations conversations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.conversations
    ADD CONSTRAINT conversations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- TOC entry 4405 (class 2606 OID 17128)
-- Name: cost_centers cost_centers_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cost_centers
    ADD CONSTRAINT cost_centers_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.cost_centers(id) ON DELETE SET NULL;


--
-- TOC entry 4435 (class 2606 OID 17492)
-- Name: document_shares document_shares_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- TOC entry 4436 (class 2606 OID 17507)
-- Name: document_shares document_shares_shared_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_shared_by_fkey FOREIGN KEY (shared_by) REFERENCES public.users(id);


--
-- TOC entry 4437 (class 2606 OID 17502)
-- Name: document_shares document_shares_shared_with_client_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_shared_with_client_fkey FOREIGN KEY (shared_with_client) REFERENCES public.clients(id);


--
-- TOC entry 4438 (class 2606 OID 17497)
-- Name: document_shares document_shares_shared_with_user_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_shares
    ADD CONSTRAINT document_shares_shared_with_user_fkey FOREIGN KEY (shared_with_user) REFERENCES public.users(id);


--
-- TOC entry 4433 (class 2606 OID 17472)
-- Name: document_versions document_versions_document_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_versions
    ADD CONSTRAINT document_versions_document_id_fkey FOREIGN KEY (document_id) REFERENCES public.documents(id) ON DELETE CASCADE;


--
-- TOC entry 4434 (class 2606 OID 17477)
-- Name: document_versions document_versions_uploaded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.document_versions
    ADD CONSTRAINT document_versions_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- TOC entry 4429 (class 2606 OID 17442)
-- Name: documents documents_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- TOC entry 4430 (class 2606 OID 17447)
-- Name: documents documents_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- TOC entry 4431 (class 2606 OID 17452)
-- Name: documents documents_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.employees(id);


--
-- TOC entry 4432 (class 2606 OID 17457)
-- Name: documents documents_uploaded_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT documents_uploaded_by_fkey FOREIGN KEY (uploaded_by) REFERENCES public.users(id);


--
-- TOC entry 4406 (class 2606 OID 18605)
-- Name: employees employees_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.chart_of_accounts(id);


--
-- TOC entry 4407 (class 2606 OID 17133)
-- Name: employees employees_branch_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_branch_id_fkey FOREIGN KEY (branch_id) REFERENCES public.branches(id);


--
-- TOC entry 4408 (class 2606 OID 17138)
-- Name: employees employees_department_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.courts(id);


--
-- TOC entry 4409 (class 2606 OID 17143)
-- Name: employees employees_governorate_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.employees
    ADD CONSTRAINT employees_governorate_id_fkey FOREIGN KEY (governorate_id) REFERENCES public.governorates(id);


--
-- TOC entry 4413 (class 2606 OID 17148)
-- Name: issues fk_issues_client_id; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT fk_issues_client_id FOREIGN KEY (client_id) REFERENCES public.clients(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- TOC entry 4410 (class 2606 OID 17163)
-- Name: follows follows_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- TOC entry 4411 (class 2606 OID 17716)
-- Name: follows follows_next_hearing_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.follows
    ADD CONSTRAINT follows_next_hearing_id_fkey FOREIGN KEY (next_hearing_id) REFERENCES public.hearings(id) ON DELETE SET NULL;


--
-- TOC entry 4426 (class 2606 OID 17367)
-- Name: hearings hearings_issue_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.hearings
    ADD CONSTRAINT hearings_issue_id_fkey FOREIGN KEY (issue_id) REFERENCES public.issues(id);


--
-- TOC entry 4444 (class 2606 OID 17595)
-- Name: invoice_items invoice_items_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items
    ADD CONSTRAINT invoice_items_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- TOC entry 4445 (class 2606 OID 17585)
-- Name: invoice_items invoice_items_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items
    ADD CONSTRAINT invoice_items_invoice_id_fkey FOREIGN KEY (invoice_id) REFERENCES public.invoices(id) ON DELETE CASCADE;


--
-- TOC entry 4446 (class 2606 OID 17590)
-- Name: invoice_items invoice_items_time_entry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice_items
    ADD CONSTRAINT invoice_items_time_entry_id_fkey FOREIGN KEY (time_entry_id) REFERENCES public.time_entries(id);


--
-- TOC entry 4412 (class 2606 OID 17168)
-- Name: invoice invoice_trans_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoice
    ADD CONSTRAINT invoice_trans_id_fkey FOREIGN KEY (trans_id) REFERENCES public.ar(id);


--
-- TOC entry 4442 (class 2606 OID 17563)
-- Name: invoices invoices_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- TOC entry 4443 (class 2606 OID 17568)
-- Name: invoices invoices_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.invoices
    ADD CONSTRAINT invoices_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- TOC entry 4414 (class 2606 OID 17173)
-- Name: issues issues_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT issues_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- TOC entry 4415 (class 2606 OID 17178)
-- Name: issues issues_issue_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.issues
    ADD CONSTRAINT issues_issue_type_id_fkey FOREIGN KEY (issue_type_id) REFERENCES public.issue_types(id);


--
-- TOC entry 4457 (class 2606 OID 18694)
-- Name: journal_entry_details journal_entry_details_journal_entry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.journal_entry_details
    ADD CONSTRAINT journal_entry_details_journal_entry_id_fkey FOREIGN KEY (journal_entry_id) REFERENCES public.journal_entries(id) ON DELETE CASCADE;


--
-- TOC entry 4427 (class 2606 OID 17405)
-- Name: lawyer_earnings lawyer_earnings_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.lawyer_earnings
    ADD CONSTRAINT lawyer_earnings_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- TOC entry 4428 (class 2606 OID 17415)
-- Name: lawyer_earnings lawyer_earnings_follow_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: yemen
--

ALTER TABLE ONLY public.lawyer_earnings
    ADD CONSTRAINT lawyer_earnings_follow_id_fkey FOREIGN KEY (follow_id) REFERENCES public.follows(id);


--
-- TOC entry 4455 (class 2606 OID 17802)
-- Name: main_accounts main_accounts_chart_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.main_accounts
    ADD CONSTRAINT main_accounts_chart_account_id_fkey FOREIGN KEY (chart_account_id) REFERENCES public.chart_of_accounts(id);


--
-- TOC entry 4416 (class 2606 OID 17218)
-- Name: message_read_status message_read_status_message_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.message_read_status
    ADD CONSTRAINT message_read_status_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id) ON DELETE CASCADE;


--
-- TOC entry 4417 (class 2606 OID 17223)
-- Name: messages messages_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.conversations(id) ON DELETE CASCADE;


--
-- TOC entry 4418 (class 2606 OID 17228)
-- Name: messages messages_reply_to_message_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_reply_to_message_id_fkey FOREIGN KEY (reply_to_message_id) REFERENCES public.messages(id) ON DELETE SET NULL;


--
-- TOC entry 4419 (class 2606 OID 17233)
-- Name: movements movements_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.movements
    ADD CONSTRAINT movements_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- TOC entry 4420 (class 2606 OID 17238)
-- Name: payment payment_gl_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment
    ADD CONSTRAINT payment_gl_id_fkey FOREIGN KEY (gl_id) REFERENCES public.gl(id);


--
-- TOC entry 4421 (class 2606 OID 17243)
-- Name: payment_links payment_links_entry_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_links
    ADD CONSTRAINT payment_links_entry_id_fkey FOREIGN KEY (entry_id) REFERENCES public.acc_trans(entry_id);


--
-- TOC entry 4422 (class 2606 OID 17248)
-- Name: payment_links payment_links_payment_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_links
    ADD CONSTRAINT payment_links_payment_id_fkey FOREIGN KEY (payment_id) REFERENCES public.payment(id);


--
-- TOC entry 4459 (class 2606 OID 18792)
-- Name: payment_vouchers payment_vouchers_credit_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers
    ADD CONSTRAINT payment_vouchers_credit_account_id_fkey FOREIGN KEY (credit_account_id) REFERENCES public.chart_of_accounts(id);


--
-- TOC entry 4460 (class 2606 OID 18787)
-- Name: payment_vouchers payment_vouchers_debit_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.payment_vouchers
    ADD CONSTRAINT payment_vouchers_debit_account_id_fkey FOREIGN KEY (debit_account_id) REFERENCES public.chart_of_accounts(id);


--
-- TOC entry 4423 (class 2606 OID 17253)
-- Name: service_distributions service_distributions_case_distribution_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.service_distributions
    ADD CONSTRAINT service_distributions_case_distribution_id_fkey FOREIGN KEY (case_distribution_id) REFERENCES public.case_distribution(id) ON DELETE CASCADE;


--
-- TOC entry 4461 (class 2606 OID 18966)
-- Name: services services_lineage_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.services
    ADD CONSTRAINT services_lineage_id_fkey FOREIGN KEY (lineage_id) REFERENCES public.lineages(id);


--
-- TOC entry 4456 (class 2606 OID 18560)
-- Name: suppliers suppliers_account_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.suppliers
    ADD CONSTRAINT suppliers_account_id_fkey FOREIGN KEY (account_id) REFERENCES public.main_accounts(id);


--
-- TOC entry 4439 (class 2606 OID 17526)
-- Name: time_entries time_entries_case_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_case_id_fkey FOREIGN KEY (case_id) REFERENCES public.issues(id);


--
-- TOC entry 4440 (class 2606 OID 17531)
-- Name: time_entries time_entries_client_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id);


--
-- TOC entry 4441 (class 2606 OID 17536)
-- Name: time_entries time_entries_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.time_entries
    ADD CONSTRAINT time_entries_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.employees(id);


--
-- TOC entry 4424 (class 2606 OID 17268)
-- Name: timecard timecard_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.timecard
    ADD CONSTRAINT timecard_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.project(id);


--
-- TOC entry 4462 (class 2606 OID 19037)
-- Name: user_permissions user_permissions_granted_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_granted_by_fkey FOREIGN KEY (granted_by) REFERENCES public.users(id);


--
-- TOC entry 4463 (class 2606 OID 19032)
-- Name: user_permissions user_permissions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_permissions
    ADD CONSTRAINT user_permissions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- TOC entry 4464 (class 2606 OID 19063)
-- Name: user_role_assignments user_role_assignments_assigned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_assigned_by_fkey FOREIGN KEY (assigned_by) REFERENCES public.users(id);


--
-- TOC entry 4465 (class 2606 OID 19058)
-- Name: user_role_assignments user_role_assignments_role_name_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_role_name_fkey FOREIGN KEY (role_name) REFERENCES public.user_roles(role_name) ON DELETE CASCADE;


--
-- TOC entry 4466 (class 2606 OID 19053)
-- Name: user_role_assignments user_role_assignments_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_role_assignments
    ADD CONSTRAINT user_role_assignments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- TOC entry 4425 (class 2606 OID 17273)
-- Name: users users_employee_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_employee_id_fkey FOREIGN KEY (employee_id) REFERENCES public.employees(id);


--
-- TOC entry 4767 (class 0 OID 0)
-- Dependencies: 4
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;
GRANT ALL ON SCHEMA public TO PUBLIC;


-- Completed on 2025-08-27 17:54:25 +03

--
-- PostgreSQL database dump complete
--

