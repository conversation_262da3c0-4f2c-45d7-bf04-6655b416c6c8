---
description: Repository Information Overview
alwaysApply: true
---

# Legal System Information

## Summary
Advanced legal office management system built with modern technologies, featuring a comprehensive suite of tools for legal case management, accounting, document management, and client portal. The system is designed for Arabic-speaking users with a focus on legal practice management.

## Structure
- **src/**: Core application code (Next.js components, pages, API routes)
- **public/**: Static assets and uploaded files
- **database/**: Database setup scripts and utilities
- **fonts/**: Custom Arabic fonts for the application
- **.next/**: Next.js build output (generated)

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: TypeScript 5.x, ES2017 target
**Framework**: Next.js 15.3.5, React 19.0.0
**Build System**: Next.js build system
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- Next.js 15.3.5 - React framework
- React 19.0.0 - UI library
- pg 8.16.3 - PostgreSQL client
- bcrypt 6.0.0 - Password hashing
- jsonwebtoken 9.0.2 - Authentication
- Radix UI components - UI component library
- Lucide React - Icon library

**Development Dependencies**:
- TypeScript 5.x - Type checking
- ESLint 9.x - Code linting
- Tailwind CSS 4.x - Styling

## Database
**Type**: PostgreSQL 15+
**Connection**: Local PostgreSQL instance
**Configuration**:
```
Host: localhost
Port: 5432
Database: mohammi
User: postgres
Password: yemen123
```

**Main Tables**:
- clients - Client management
- employees - Employee records
- issues - Legal cases
- follows - Case follow-ups
- journal_entries - Accounting entries
- users - System users

## Build & Installation
```bash
# Install dependencies
npm install

# Development server
npm run dev

# Production build
npm run build

# Start production server
npm start
```

## Docker
**Docker Compose**: docker-compose-ledgersmb.yml
**Services**:
- ledgersmb-db: PostgreSQL 15 database
- ledgersmb: LedgerSMB accounting application

**Run Command**:
```bash
docker-compose -f docker-compose-ledgersmb.yml up -d
```

## Main Components
**Frontend**:
- Next.js pages and API routes
- React components for UI
- Tailwind CSS for styling

**Backend**:
- Next.js API routes
- PostgreSQL database
- Node.js scripts for database operations

**Authentication**:
- Custom JWT-based authentication
- Role-based access control

## Key Features
- Legal case management
- Client portal with case tracking
- Document management system
- Integrated accounting system
- Time tracking and billing
- Advanced reporting and analytics
- Employee management
- Court and hearing tracking